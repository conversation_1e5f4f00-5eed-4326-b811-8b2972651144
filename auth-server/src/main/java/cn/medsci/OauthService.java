package cn.medsci;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.xingyuv.http.config.HttpConfig;
import com.xingyuv.jushauth.config.AuthConfig;
import com.xingyuv.jushauth.config.AuthDefaultSource;
import com.xingyuv.jushauth.enums.scope.AuthFacebookScope;
import com.xingyuv.jushauth.model.AuthCallback;
import com.xingyuv.jushauth.model.AuthResponse;
import com.xingyuv.jushauth.request.AuthRequest;
import com.xingyuv.justauth.AuthRequestFactory;
import com.xingyuv.justauth.autoconfigure.JustAuthProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Arrays;

@Slf4j
@Service
public class OauthService {

    @Autowired
    private AuthRequestFactory factory;

    @Value("${spring.profiles.active}")
    private String env;

    @Autowired
    private AiBaseProperties aiBaseProperties;

    @Autowired
    private JustAuthProperties justAuthProperties;

    /**
     * 授权回调，并获取用户信息
     * @param type
     * @param callback
     * @return
     */
    public String callback(Integer type, AuthCallback callback) {
        log.info("【授权callback】code:{} state:{}", callback.getCode(), callback.getState());

        SocialTypeEnum socialType = SocialTypeEnum.valueOfType(type);
        if (socialType == null) {
            return "/oauth/error?msg=This platform is currently not supported";
        }

        try {
            AuthRequest request = factory.get(socialType.getSource());
            AuthConfig authConfig = (AuthConfig) ReflectUtil.getFieldValue(request, "config");
            AuthConfig newAuthConfig = ReflectUtil.newInstance(authConfig.getClass());
            BeanUtil.copyProperties(authConfig, newAuthConfig);
            if (type > 50) {
                AuthConfig configProperties = justAuthProperties.getType().get(socialType.name());
                newAuthConfig.setClientId(configProperties.getClientId());
                newAuthConfig.setClientSecret(configProperties.getClientSecret());
                newAuthConfig.setRedirectUri(configProperties.getRedirectUri());
                if (configProperties.getAgentId() != null) { // 如果有 agentId 则修改 agentId
                    newAuthConfig.setAgentId(configProperties.getAgentId());
                }
            }

            if (AuthDefaultSource.FACEBOOK.name().equals(SocialTypeEnum.valueOfType(type).getSource())) {
                newAuthConfig.setScopes(Arrays.asList(AuthFacebookScope.EMAIL.getScope(), "public_profile"));
            }

            HttpConfig httpConfig = HttpConfig.builder().timeout(60000).build();
            if (env.equals("dev")) {
                httpConfig.setProxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("127.0.0.1", 7897)));
            }

            newAuthConfig.setHttpConfig(httpConfig);
            // 设置会 request 里，进行后续使用
            ReflectUtil.setFieldValue(request, "config", newAuthConfig);
            // 请求授权
            AuthResponse response = request.login(callback);

//        AuthResponse response = new AuthResponse();
//        response.setCode(200);
//        response.setMsg("成功");
//        response.setData(callback);

            String body = JSONUtil.toJsonStr(response);
            log.info("【授权response】= {}", body);
            if (response.ok()) {
                String url = aiBaseProperties.getClientUrl().getOrDefault(env, "dev") + type + "?state=" + callback.getState() + "&code=" + callback.getCode();
                HttpResponse resp = sendToClient(url, body);
                JSONObject res = JSONUtil.parseObj(resp.body());
                if (res.getInt("code")==0 || res.getInt("code")==200) {
                    return res.getStr("data") + "/" + type + "?authState=" + callback.getState() + "&authCode=" + callback.getCode();
                } else {
                    return res.getStr("msg");
                }
            }
            return "/oauth/error?msg="+response.getMsg()+"-authState=" + callback.getState() + "-authCode=" + callback.getCode();
        } catch (Exception e) {
            e.printStackTrace();
            return "/oauth/error?msg=" + e.getMessage()+"-authState=" + callback.getState() + "-authCode=" + callback.getCode();
        }
    }

    /**
     * 推送数据到待授权服务
     * @param url
     * @param body
     * @return
     */
    public HttpResponse sendToClient(String url, String body) {
        // 创建HTTP POST请求
        HttpRequest postRequest = HttpRequest.post(url);

        // 设置请求体，例如JSON数据
        postRequest.body(body);

        // 设置请求头信息
        postRequest.header("Content-Type", "application/json");

        // 发送请求，并获取响应
        HttpResponse response = postRequest.execute();

        // 输出响应的内容
        log.info("【回传response】= {} - {}", url, response.body());
        return response;
    }



}
