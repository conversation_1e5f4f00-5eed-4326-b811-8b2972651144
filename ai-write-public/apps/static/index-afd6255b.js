import{x as e,au as a,W as s,ah as o,ai as t,av as n,aw as l,d as r,e as i,j as c,t as d,i as m,F as u,y as g,$ as h,ae as p,af as v,k as w}from"https://static.medsci.cn/ai-write/static/index-ce91ddf0.js";import{_ as I}from"./_plugin-vue_export-helper-1b428a4d.js";const f={data:()=>({avatar:"",userInfo:null,isIncludeTool:!1,selectedLanguage:"",langs:[]}),watch:{userInfoCookie(e,a){}},computed:{baseUrl:()=>"https://www.medsci.cn/",userInfoCookie:()=>e.get("userInfo")},methods:{changeImg(){this.avatar="https://img.medsci.cn/web/img/user_icon.png"},getCookieUserInfo:()=>e.get("userInfo"),toggle(e){const a=this.$route.path,s=this.$route.params.lang;if(s){const o=a.replace(`/${s}`,"");this.$router.push(`/${e}${o}`)}else this.$router.push(`/${e}${a}`);window.localStorage.setItem("ai_apps_lang",e),this.selectedLanguage=e,this.$i18n.locale=e,this.$emit("getAppLang",e),"zh-CN"==localStorage.getItem("ai_apps_lang")?this.$emit("isZHChange",!0):this.$emit("isZHChange",!1)},async logout(){e.remove("userInfo",{domain:".medon.com.cn"}),e.remove("userInfo",{domain:".medsci.cn"}),e.remove("userInfo",{domain:"localhost"}),localStorage.removeItem("conversation"),localStorage.removeItem("writeContent");let s=localStorage.getItem("socialType");s&&35==s||s&&36==s?a().then((()=>{localStorage.removeItem("hasuraToken"),localStorage.removeItem("yudaoToken"),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid"),location.reload()})).catch((e=>{})):(localStorage.removeItem("hasuraToken"),localStorage.removeItem("yudaoToken"),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid"),window.location.origin.includes("medsci.cn")?window.location.href="https://www.medsci.cn/sso_logout?redirectUrl="+window.location.href:window.location.href="https://portal-test.medon.com.cn/sso_logout?redirectUrl="+window.location.href)},async loginAccount(){let e=await this.getLocationData();e&&"中国"!=e?this.$router.push("/login"):window.addLoginDom()},medsciSearch(){let e=document.getElementById("medsciSearchKeyword").value;e?window.open(`https://www.medsci.cn/search?q=${e}`,"_blank"):window.open("https://search.medsci.cn/","_blank")},async getLocationData(){let e=s("current_location_country",1);return e||await o().then((e=>{t("current_location_country",e)})),e=s("current_location_country",1),e},getAppLangsData(){n().then((e=>{e.map((e=>{e.status||this.langs.push({name:e.value,value:e.remark})}))}))}},mounted(){var a,s;this.getAppLangsData(),this.userInfo=e.get("userInfo")?JSON.parse(e.get("userInfo")):null,this.isIncludeTool=window.location.href.includes("/tool"),setTimeout((()=>{this.selectedLanguage=l()}),0),this.avatar=(null==(a=this.userInfo)?void 0:a.avatar)?null==(s=this.userInfo)?void 0:s.avatar:"https://img.medsci.cn/web/img/user_icon.png"}},k=e=>(p("data-v-0babbbc3"),e=e(),v(),e),_={class:"header ms-header-media"},y={class:"ms-header"},b={class:"wrapper"},S={class:"main-menu-placeholder wrapper clearfix",style:{height:"56px",display:"block !important"}},$=k((()=>c("div",{class:"ms-header-img"},[c("img",{src:"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png",alt:""})],-1))),C={id:"main-menu",class:"ms-header-nav"},T={class:"header-top header-user",id:"user-info-header"},L={class:"index-user-img"},x={key:0,class:"change_lang"},A={class:"current_lang"},U={class:"ms-link"},j={class:"ms-dropdown-menu"},D={class:"new-header-avator-pop",id:"new-header-avator"},N={class:"new-header-bottom",style:{padding:"0"}},q={class:"langUl"},E=["onClick"],H=k((()=>c("li",null,null,-1))),Z={key:1,class:"index-user-img"},z={href:"#"},B={class:"img-area"},F=["src"],G={class:"ms-dropdown-menu"},J={class:"new-header-avator-pop",id:"new-header-avator"},K={class:"new-header-top"},O={class:"new-header-info"},Q=["src"],R={class:"new-header-name"};const W=I(f,[["render",function(e,a,s,o,t,n){var l,p,v,w,I;return r(),i("div",_,[c("div",y,[c("div",b,[c("div",S,[$,c("nav",C,[c("div",T,[c("ul",null,[c("li",L,[t.isIncludeTool?m("",!0):(r(),i("div",x,[c("span",A,d(null==(l=t.langs.filter((e=>e.value==t.selectedLanguage))[0])?void 0:l.name),1),c("span",U,d(e.$t("market.switch")),1)])),c("div",j,[c("div",D,[c("div",N,[c("div",q,[(r(!0),i(u,null,g(t.langs,(e=>(r(),i("p",{key:e,onClick:a=>n.toggle(e.value),class:h({langItemSelected:e.value===t.selectedLanguage})},d(e.name),11,E)))),128))])])])])]),(null==(p=t.userInfo)?void 0:p.userId)?(r(),i("li",Z,[c("a",z,[c("div",B,[c("img",{src:t.avatar,onError:a[1]||(a[1]=(...e)=>n.changeImg&&n.changeImg(...e)),alt:""},null,40,F)])]),c("div",G,[c("div",J,[c("a",{class:"new-header-exit ms-statis","ms-statis":"logout",href:"#",onClick:a[2]||(a[2]=e=>n.logout())},d(e.$t("market.logout")),1),c("div",K,[c("div",O,[c("img",{class:"new-header-avatar",src:t.avatar,onError:a[3]||(a[3]=(...e)=>n.changeImg&&n.changeImg(...e)),alt:""},null,40,Q),c("div",R,[c("span",null,d((null==(v=t.userInfo)?void 0:v.realName)?null==(w=t.userInfo)?void 0:w.realName:null==(I=t.userInfo)?void 0:I.userName),1)])])])])])])):(r(),i(u,{key:0},[c("li",null,[c("a",{href:"javascript: void(0)",class:"ms-link",onClick:a[0]||(a[0]=(...e)=>n.loginAccount&&n.loginAccount(...e))},d(e.$t("market.login")),1)]),H],64))])])])])])])])}],["__scopeId","data-v-0babbbc3"]]),M={name:"AssistantComponent",data:()=>({}),methods:{}},P=e=>(p("data-v-********"),e=e(),v(),e),V={class:"assistant-container"},X=[P((()=>c("div",{class:"assistant-icon"},[c("img",{src:"/apps/static/kefu-ae1166e2.png",class:"fas fa-user-astronaut",alt:"客服"})],-1))),P((()=>c("div",{class:"qr-code"},[c("img",{src:"/apps/static/qrcode-4bdb4a15.png",alt:"QR Code"}),w(" 扫码添加小助手 ")],-1)))];const Y=I(M,[["render",function(e,a,s,o,t,n){return r(),i("div",V,X)}],["__scopeId","data-v-********"]]);export{W as _,Y as c};
