/* empty css                  */import{_ as e,c as a}from"./index-a878ba90.js";import{_ as t,u as i,r as n,a as l,w as s,o,n as p,b as c,c as d,d as r,e as m,f as u,g as _,h as v,i as g,t as f,j as h,k as b,l as x,m as y,p as I,q as w,s as A,E,v as j}from"./index-d35f61c7.js";import{g as T}from"./index-d3608df3.js";import{_ as S}from"./_plugin-vue_export-helper-1b428a4d.js";const V=[{name:"writingAssistant",path:"/writingAssistant",type:"医学写作",meta:{title:"论文",dify_app_uuid:"10c03431-1933-4f59-8848-e2e3ccb2c557",desc:"医学论文是医学领域的研究者撰写的，旨在探讨疾病病因、诊断、治疗、预防或医学理论、技术进展的学术性文章。它基于科学实验或临床观察，通过严谨的数据分析，提出新发现、新观点或验证已有理论，为医学实践提供科学依据和指导。",icon:T("AI写作助手.svg"),bg:T("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-58d01644.js")),["static/index-58d01644.js","static/Editor-361361cf.js","static/index-d35f61c7.js","static/index-87571f09.css","static/_plugin-vue_export-helper-1b428a4d.js","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/el-tooltip-4ed993c7.js","static/index-5a807990.js","static/index-d3608df3.js","static/index-044dd070.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"intelligentPolishing",path:"/intelligentPolishing",type:"医学写作",meta:{title:"综述",dify_app_uuid:"4955c5cc-a366-45d8-8046-aa3044ff77bc",desc:"医学综述是对某一医学领域或专题在一定时期内研究成果的汇总和分析，旨在系统回顾研究进展，评价研究质量，探讨存在问题，预测未来方向，为研究者提供全面、深入的信息参考。",icon:T("AI智能润色.svg"),bg:T("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-f97f852c.js")),["static/index-f97f852c.js","static/index-d35f61c7.js","static/index-87571f09.css","static/index-5a807990.js","static/_plugin-vue_export-helper-1b428a4d.js","static/index-84dd1b27.css","static/el-button-b4242b8c.css","static/el-divider-f4d3946e.css","static/el-icon-b1770749.css","static/el-input-bec20aed.css","static/el-message-5edaa45a.css"])},{name:"reviewer",path:"/reviewer",type:"医学写作",meta:{title:"研究方案",dify_app_uuid:"d7df5b59-280e-4dda-b0f2-700a12b0b1f9",desc:"医学研究方案是科研人员为探索医学问题而设计的详细计划，包括研究目的、假设、方法、样本选择、数据收集与分析步骤、预期结果及伦理考量等，确保研究过程科学、严谨、可重复。",icon:T("审稿人回复信.svg"),bg:T("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-7c9cd5d8.js")),["static/index-7c9cd5d8.js","static/Editor-361361cf.js","static/index-d35f61c7.js","static/index-87571f09.css","static/_plugin-vue_export-helper-1b428a4d.js","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-5a807990.js","static/index-d3608df3.js","static/index-cc9df6ae.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"rewrite",path:"/rewrite",type:"医学写作",meta:{title:"软文",dify_app_uuid:"d1fc1b66-47ce-45cd-9ff9-ee49a2e44881",desc:"医学软文是以医疗、健康领域为背景，通过撰写具有吸引力和可读性的文章，间接推广医疗产品、服务或健康理念的一种营销手段。",icon:T("AI降重改写.svg"),bg:T("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-c173b8e2.js")),["static/index-c173b8e2.js","static/index-5a807990.js","static/index-d35f61c7.js","static/index-87571f09.css","static/index-d3608df3.js","static/el-button-b4242b8c.css","static/el-icon-b1770749.css","static/el-input-bec20aed.css","static/el-message-5edaa45a.css"])},{name:"topTitle",path:"/topTitle",type:"医学写作",meta:{title:"指南共识",dify_app_uuid:"7252420d-c356-4bed-9e06-ce6c3bc01270",desc:"指南共识是指在医学领域中，基于系统评价的证据和专家经验，针对特定临床问题制定的指导性建议或推荐意见。",icon:T("高分title生成器.svg"),bg:T("题材和论文章节的写作语料.png")},component:()=>t((()=>import("./index-071ed11d.js")),["static/index-071ed11d.js","static/el-tooltip-4ed993c7.js","static/index-5a807990.js","static/index-d35f61c7.js","static/index-87571f09.css","static/index-d3608df3.js","static/_plugin-vue_export-helper-1b428a4d.js","static/index-19af116e.css","static/el-button-b4242b8c.css","static/el-popper-27830cce.css","static/el-divider-f4d3946e.css","static/el-icon-b1770749.css","static/el-input-bec20aed.css","static/el-message-5edaa45a.css"])},{name:"reviewSearch",path:"/reviewSearch",type:"医学写作",meta:{title:"病例报告",dify_app_uuid:"05544f18-9efb-42e4-ad5d-8ca78feaf56c",desc:"病例报告是详细记录并分析个别患者疾病发生、诊断、治疗及转归过程的医学文献，用于探讨罕见病、特殊临床表现或治疗经验。",icon:T("综述Review.svg"),bg:T("题材和论文章节的写作语料.png")},component:()=>t((()=>import("./index-898e156a.js")),["static/index-898e156a.js","static/Editor-361361cf.js","static/index-d35f61c7.js","static/index-87571f09.css","static/_plugin-vue_export-helper-1b428a4d.js","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-5a807990.js","static/index-d3608df3.js","static/index-224cdab4.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"reviewSearch",path:"/reviewSearch",type:"医学写作",meta:{title:"国自然基金写作",dify_app_uuid:"cc0c4388-8c02-4f1c-8a32-e0f3e84146c7",desc:"“国自然基金写作”是撰写国家自然科学基金申请书的过程，该过程需遵循科学基金的撰写要求和规范，涵盖项目名称、关键词、摘要、立项依据、研究目标、内容、方案及创新性等多个方面的撰写，旨在清晰、准确、有逻辑地展示研究项目的科学性、创新性和可行性，以获得基金资助。",icon:T("综述Review.svg"),bg:T("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-898e156a.js")),["static/index-898e156a.js","static/Editor-361361cf.js","static/index-d35f61c7.js","static/index-87571f09.css","static/_plugin-vue_export-helper-1b428a4d.js","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-5a807990.js","static/index-d3608df3.js","static/index-224cdab4.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"reviewSearch",path:"/chat",type:"医学会话",meta:{title:"学术写作问答",dify_app_uuid:"c81251e7-9269-413b-95d3-0a4b88ce84b9",desc:"",icon:T("场景写作.svg"),bg:T("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-898e156a.js")),["static/index-898e156a.js","static/Editor-361361cf.js","static/index-d35f61c7.js","static/index-87571f09.css","static/_plugin-vue_export-helper-1b428a4d.js","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-5a807990.js","static/index-d3608df3.js","static/index-224cdab4.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"reviewSearch",path:"/chat",type:"医学会话",meta:{title:"AI产品发布会",dify_app_uuid:"5ec1c9f5-a9a3-4697-bb66-71793e8375a6",desc:"",icon:T("场景写作.svg"),bg:T("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-898e156a.js")),["static/index-898e156a.js","static/Editor-361361cf.js","static/index-d35f61c7.js","static/index-87571f09.css","static/_plugin-vue_export-helper-1b428a4d.js","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-5a807990.js","static/index-d3608df3.js","static/index-224cdab4.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])}],L={class:"flex-1 flex flex-col overflow-hidden"},O={class:"text-gray-500 mb-2"},P=["href"],R={class:"flex items-center my-2"},D=["src"],k={class:"text-xl font-bold text-dark-200 mr-4"},C={class:"flex-1 overflow-hidden"},N={class:"flex items-center my-2"},$=["src"],U={class:"text-xl font-bold text-dark-200"},z={class:"text-dark-500 mb-6"},F={class:"flex justify-center"},H=S({__name:"Layout",setup(t){var T;const S=i(),H=n([]),J=n(null),q=n(null),K=n(!1),Z=n(0),B=n(!1),{locale:G}=l(),M=navigator.browserLanguage||navigator.language,Q=(null==(T=S.params)?void 0:T.lang)||M,W=n();G.value=Q;const X=()=>{K.value=!0},Y=e=>{B.value=e};s((()=>S),(()=>{q.value=JSON.parse(sessionStorage.getItem("nodeInfo")),J.value.setCurrentKey(node.name)}),{deep:!0});const ee=n({appUuid:S.params.appUuid});return o((async()=>{W.value="https://ai.medon.com.cn",await void A(ee.value).then((e=>{q.value=e[0]})).catch((e=>{})),"zh-CN"==localStorage.getItem("ai_apps_lang")&&(B.value=!0),V.map((e=>(e.label=e.meta.title,{label:e.type,children:V.filter((a=>a.type==e.type))}))).forEach((e=>{H.value.find((a=>a.type===e.label))||H.value.push(e)})),sessionStorage.getItem("nodeInfo")?q.value=JSON.parse(sessionStorage.getItem("nodeInfo")):q.value=H.value[0].children[0],p((()=>{})),Z.value=document.body.clientHeight-56})),(t,i)=>{var n,l,s,o,p,A;const T=e,S=E,V=c("router-view"),H=j;return d(),r("div",null,[m(T,{onIsZHChange:Y}),u(B)?(d(),_(a,{key:0})):v("",!0),g("div",{class:"h-full flex p-6",style:w({height:`${u(Z)}px`})},[g("main",L,[g("div",null,[g("div",O,[g("a",{class:"cursor-pointer hover:text-[#5298FF]",href:u(W)},f(t.$t("tool.home")),9,P),h("/"+f((null==(n=u(q))?void 0:n.appType)?t.$t(`${u(b)[null==(l=u(q))?void 0:l.appType]}`):"")+"/"+f(null==(s=u(q))?void 0:s.appName),1)]),g("div",R,[g("img",{class:"w-[38px] h-[38px] mr-4",src:(null==(o=u(q))?void 0:o.appIcon)?null==(p=u(q))?void 0:p.appIcon:"https://img.medsci.cn/web/prod/img/user_icon.png",alt:"这是icon"},null,8,D),g("div",k,f(null==(A=u(q))?void 0:A.appName),1),m(S,{plain:"",size:"small",onClick:X},{default:x((()=>[h(f(t.$t("tool.intro")),1)])),_:1})])]),g("div",C,[m(V,null,{default:x((({Component:e})=>[(d(),_(y(e)))])),_:1})])]),m(H,{modelValue:u(K),"onUpdate:modelValue":i[1]||(i[1]=e=>I(K)?K.value=e:null),"show-close":!1,width:"500"},{header:x((()=>{var e,a,t;return[g("div",N,[g("img",{class:"w-[38px] h-[38px] mr-4",src:(null==(e=u(q))?void 0:e.appIcon)?null==(a=u(q))?void 0:a.appIcon:"https://img.medsci.cn/web/prod/img/user_icon.png",alt:"这是icon"},null,8,$),g("div",U,f(null==(t=u(q))?void 0:t.appName),1)])]})),default:x((()=>{var e;return[g("div",z,f(null==(e=u(q))?void 0:e.appDescription),1),g("div",F,[m(S,{onClick:i[0]||(i[0]=e=>K.value=!1)},{default:x((()=>[h("我知道了")])),_:1})])]})),_:1},8,["modelValue"])],4)])}}},[["__scopeId","data-v-b125b886"]]);export{H as default};
