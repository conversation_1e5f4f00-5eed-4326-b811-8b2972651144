import{a as e,_ as l}from"./Editor-6153d816.js";/* empty css                  *//* empty css                    *//* empty css                  *//* empty css                 */import{m as a}from"./index-c39944c3.js";import{s as t}from"./index-d5dd3055.js";/* empty css                   */import{_ as s}from"./_plugin-vue_export-helper-1b428a4d.js";import{r as o,O as n,o as i,d as r,e as u,j as d,f as p,g as m,q as c,m as f,k as v,S as x,t as g,F as y,z as h,i as b,h as j,s as V,a3 as _,C as S,E as k,ae as z,aH as w,aI as C}from"https://static.medsci.cn/ai-write/static/index-01c25d3e.js";/* empty css                  *//* empty css                  */const T={class:"flex h-full overflow-auto"},H={class:"w-[58%]"},M={class:"flex items-center bg-[#f7f8fa] p-2 rounded-md h-[52px] mb-2"},N={class:"flex items-center my-8"},U={class:"flex items-center mr-4"},q={class:"flex items-center mr-4"},L={class:"mr-2"},W=["innerHTML","onClick"],$=["innerHTML"],E={class:"flex-1 ml-4 box-right"},I=s({__name:"index",setup(s){const I=o("literature retrieval"),A=o(""),F=o(1),G=o(!0),O=o([]),P=o({}),B=o("请输入关键词或短句（中/英）"),D=n({pageNo:1,pageSize:20}),J=o(344),K=e=>{if(!I.value)return _.warning("请输入关键词或短句");1==e&&(D.pageNo=1);let l=O.value.map((e=>"cns"==e?{field:"cns",opt:2,vals:[e],val:"",synonymsWordVos:[]}:{field:"effect",opt:2,vals:[e],val:"",synonymsWordVos:[]}));a("review",{key:I.value,page:D.pageNo-1,size:D.pageSize,allMySentence:0,allMyGroupSentence:0,synonymsHistory:0,mulSearchConditions:l}).then((e=>{e&&e.data&&(P.value=e.data,P.value.content=t(P.value.content))}))},Q=()=>{F.value++};return i((()=>{K(),J.value=document.querySelector(".box-right").offsetWidth,window.onresize=()=>{J.value=document.querySelector(".box-right").offsetWidth}})),(a,t)=>{const s=S,o=k,n=z,i=w,_=C,R=e,X=l;return r(),u("div",T,[d("div",H,[d("div",M,[p(s,{class:"h-full !text-[24px]",modelValue:m(I),"onUpdate:modelValue":t[0]||(t[0]=e=>c(I)?I.value=e:null),placeholder:m(B),clearable:""},null,8,["modelValue","placeholder"]),p(o,{type:"primary",onClick:t[1]||(t[1]=e=>K(1))},{default:f((()=>t[7]||(t[7]=[v("查 询")]))),_:1})]),d("div",N,[d("div",U,[d("span",{class:x(["mr-2",m(G)?"text-[#409eff]":""])},"翻译",2),p(n,{modelValue:m(G),"onUpdate:modelValue":t[2]||(t[2]=e=>c(G)?G.value=e:null)},null,8,["modelValue"])]),d("div",q,[t[12]||(t[12]=d("span",{class:"mr-2"},"影响因子：",-1)),p(_,{modelValue:m(O),"onUpdate:modelValue":t[3]||(t[3]=e=>c(O)?O.value=e:null)},{default:f((()=>[p(i,{label:"3"},{default:f((()=>t[8]||(t[8]=[v(g("<3分"))]))),_:1}),p(i,{label:"5"},{default:f((()=>t[9]||(t[9]=[v("3-10分")]))),_:1}),p(i,{label:"10"},{default:f((()=>t[10]||(t[10]=[v(g(">10分"))]))),_:1}),p(i,{label:"cns"},{default:f((()=>t[11]||(t[11]=[v(g("CNS"))]))),_:1})])),_:1},8,["modelValue"])])]),d("div",null,[(r(!0),u(y,null,h(m(P).content,((e,l)=>(r(),u("div",{class:"flex mb-8",key:l},[d("div",L,g(l+1)+".",1),d("div",null,[d("div",{class:"text-[18px] text-[#5e5e5e] cursor-pointer html-value",innerHTML:e.text,onClick:a=>((e,l)=>{let a=`${e}.${l}`.replace(/\n/g,"");A.value+=`<p>${a}</p>`})(l+1,e.text)},null,8,W),m(G)?(r(),u("div",{key:0,class:"text-[16px] text-[#0a76f5] mt-4",innerHTML:e.translateText},null,8,$)):b("",!0)])])))),128))]),m(P)&&m(P).eleTotal?(r(),j(R,{key:0,class:"pb-10",total:m(P).eleTotal,page:m(D).pageNo,"onUpdate:page":t[4]||(t[4]=e=>m(D).pageNo=e),limit:m(D).pageSize,"onUpdate:limit":t[5]||(t[5]=e=>m(D).pageSize=e),onPagination:K},null,8,["total","page","limit"])):b("",!0)]),d("div",E,[(r(),j(X,{class:"h-[380px] fixed z-99",style:V({width:m(J)+"px"}),modelValue:m(A),"onUpdate:modelValue":t[6]||(t[6]=e=>c(A)?A.value=e:null),onClear:Q,key:m(F)},null,8,["style","modelValue"]))])])}}},[["__scopeId","data-v-300433dc"]]);export{I as default};
