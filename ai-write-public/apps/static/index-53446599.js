/* empty css                  *//* empty css                *//* empty css                 */import{s as e}from"./index-1613dbfb.js";import{r as a,aP as l,d as s,e as t,f as u,g as i,q as r,j as n,m as o,k as m,F as v,B as p,V as d,h as c,aQ as f,i as x,a7 as y,G as j,E as h,J as g}from"./index-59bb6979.js";import{s as k}from"./index-62001382.js";/* empty css                   */const w={class:"pt-10 overflow-auto h-full"},F={class:"flex justify-center my-10"},_={key:0},V=["innerHTML"],b={class:"flex justify-center mt-10"},A={__name:"index",setup(A){const E=a(""),J=a([]),S=a([]),z=a(5),C=()=>{if(a=E.value,/[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi.exec(a)||!E.value)return y.warning("请输入英文内容");var a;e({text:E.value}).then((e=>{e&&e&&e.data&&(J.value=e.data,J.value=k(J.value),z.value=5,H())}))},H=()=>{let e=[];5==z.value?(e=[0,5],z.value=3):3==z.value?(e=[5,8],z.value=2):2==z.value&&(e=[8,10],z.value=5),S.value=JSON.parse(JSON.stringify(J.value)).slice(e[0],e[1])};return(e,a)=>{const y=j,k=h,A=g,J=l("copy");return s(),t("div",w,[u(y,{modelValue:i(E),"onUpdate:modelValue":a[0]||(a[0]=e=>r(E)?E.value=e:null),rows:8,type:"textarea",placeholder:"请输入不超过250单词的段落","show-word-limit":"",resize:"none",maxlength:250},null,8,["modelValue"]),n("div",F,[u(k,{type:"primary",onClick:C},{default:o((()=>[m("改 写")])),_:1})]),i(S)&&i(S).length?(s(),t("div",_,[(s(!0),t(v,null,p(i(S),((e,a)=>(s(),t("div",{class:"flex justify-center mb-6 relative",key:a},[n("span",{innerHTML:e.textShow,class:"text-[18px]"},null,8,V),d((s(),c(A,{title:"复制",size:"16",color:"#909399",class:"cursor-pointer ml-2 absolute"},{default:o((()=>[u(i(f))])),_:2},1024)),[[J,e.text]])])))),128)),n("div",b,[u(k,{type:"primary",link:"",onClick:H},{default:o((()=>[m("换一换")])),_:1})])])):x("",!0)])}}};export{A as default};
