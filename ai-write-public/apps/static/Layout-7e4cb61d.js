/* empty css                  */import{_ as e,c as a}from"./index-4c3971f8.js";import{_ as t,a as i,u as n,r as l,b as s,w as o,o as p,n as c,c as d,d as r,e as m,f as u,g as _,h as v,i as g,j as f,t as h,k as b,l as x,m as y,p as I,q as w,s as A,v as E,E as j,x as T}from"./index-c6ba906c.js";import{g as S}from"./index-55777a8a.js";const V=[{name:"writingAssistant",path:"/writingAssistant",type:"医学写作",meta:{title:"论文",dify_app_uuid:"10c03431-1933-4f59-8848-e2e3ccb2c557",desc:"医学论文是医学领域的研究者撰写的，旨在探讨疾病病因、诊断、治疗、预防或医学理论、技术进展的学术性文章。它基于科学实验或临床观察，通过严谨的数据分析，提出新发现、新观点或验证已有理论，为医学实践提供科学依据和指导。",icon:S("AI写作助手.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-08ac8652.js")),["static/index-08ac8652.js","static/Editor-ae5ccb2e.js","static/index-c6ba906c.js","static/index-87571f09.css","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/el-tooltip-4ed993c7.js","static/index-ecf8041a.js","static/index-55777a8a.js","static/index-044dd070.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"intelligentPolishing",path:"/intelligentPolishing",type:"医学写作",meta:{title:"综述",dify_app_uuid:"4955c5cc-a366-45d8-8046-aa3044ff77bc",desc:"医学综述是对某一医学领域或专题在一定时期内研究成果的汇总和分析，旨在系统回顾研究进展，评价研究质量，探讨存在问题，预测未来方向，为研究者提供全面、深入的信息参考。",icon:S("AI智能润色.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-6320df2c.js")),["static/index-6320df2c.js","static/index-c6ba906c.js","static/index-87571f09.css","static/index-ecf8041a.js","static/index-84dd1b27.css","static/el-button-b4242b8c.css","static/el-divider-f4d3946e.css","static/el-icon-b1770749.css","static/el-input-bec20aed.css","static/el-message-5edaa45a.css"])},{name:"reviewer",path:"/reviewer",type:"医学写作",meta:{title:"研究方案",dify_app_uuid:"d7df5b59-280e-4dda-b0f2-700a12b0b1f9",desc:"医学研究方案是科研人员为探索医学问题而设计的详细计划，包括研究目的、假设、方法、样本选择、数据收集与分析步骤、预期结果及伦理考量等，确保研究过程科学、严谨、可重复。",icon:S("审稿人回复信.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-bfaaaa4c.js")),["static/index-bfaaaa4c.js","static/Editor-ae5ccb2e.js","static/index-c6ba906c.js","static/index-87571f09.css","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-ecf8041a.js","static/index-55777a8a.js","static/index-cc9df6ae.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"rewrite",path:"/rewrite",type:"医学写作",meta:{title:"软文",dify_app_uuid:"d1fc1b66-47ce-45cd-9ff9-ee49a2e44881",desc:"医学软文是以医疗、健康领域为背景，通过撰写具有吸引力和可读性的文章，间接推广医疗产品、服务或健康理念的一种营销手段。",icon:S("AI降重改写.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-88b75cef.js")),["static/index-88b75cef.js","static/index-ecf8041a.js","static/index-c6ba906c.js","static/index-87571f09.css","static/index-55777a8a.js","static/el-button-b4242b8c.css","static/el-icon-b1770749.css","static/el-input-bec20aed.css","static/el-message-5edaa45a.css"])},{name:"topTitle",path:"/topTitle",type:"医学写作",meta:{title:"指南共识",dify_app_uuid:"7252420d-c356-4bed-9e06-ce6c3bc01270",desc:"指南共识是指在医学领域中，基于系统评价的证据和专家经验，针对特定临床问题制定的指导性建议或推荐意见。",icon:S("高分title生成器.svg"),bg:S("题材和论文章节的写作语料.png")},component:()=>t((()=>import("./index-1274c66a.js")),["static/index-1274c66a.js","static/el-tooltip-4ed993c7.js","static/index-ecf8041a.js","static/index-c6ba906c.js","static/index-87571f09.css","static/index-55777a8a.js","static/index-19af116e.css","static/el-button-b4242b8c.css","static/el-popper-27830cce.css","static/el-divider-f4d3946e.css","static/el-icon-b1770749.css","static/el-input-bec20aed.css","static/el-message-5edaa45a.css"])},{name:"reviewSearch",path:"/reviewSearch",type:"医学写作",meta:{title:"病例报告",dify_app_uuid:"05544f18-9efb-42e4-ad5d-8ca78feaf56c",desc:"病例报告是详细记录并分析个别患者疾病发生、诊断、治疗及转归过程的医学文献，用于探讨罕见病、特殊临床表现或治疗经验。",icon:S("综述Review.svg"),bg:S("题材和论文章节的写作语料.png")},component:()=>t((()=>import("./index-26069221.js")),["static/index-26069221.js","static/Editor-ae5ccb2e.js","static/index-c6ba906c.js","static/index-87571f09.css","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-ecf8041a.js","static/index-55777a8a.js","static/index-224cdab4.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"reviewSearch",path:"/reviewSearch",type:"医学写作",meta:{title:"国自然基金写作",dify_app_uuid:"cc0c4388-8c02-4f1c-8a32-e0f3e84146c7",desc:"“国自然基金写作”是撰写国家自然科学基金申请书的过程，该过程需遵循科学基金的撰写要求和规范，涵盖项目名称、关键词、摘要、立项依据、研究目标、内容、方案及创新性等多个方面的撰写，旨在清晰、准确、有逻辑地展示研究项目的科学性、创新性和可行性，以获得基金资助。",icon:S("综述Review.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-26069221.js")),["static/index-26069221.js","static/Editor-ae5ccb2e.js","static/index-c6ba906c.js","static/index-87571f09.css","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-ecf8041a.js","static/index-55777a8a.js","static/index-224cdab4.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"reviewSearch",path:"/chat",type:"医学会话",meta:{title:"学术写作问答",dify_app_uuid:"c81251e7-9269-413b-95d3-0a4b88ce84b9",desc:"",icon:S("场景写作.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-26069221.js")),["static/index-26069221.js","static/Editor-ae5ccb2e.js","static/index-c6ba906c.js","static/index-87571f09.css","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-ecf8041a.js","static/index-55777a8a.js","static/index-224cdab4.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"reviewSearch",path:"/chat",type:"医学会话",meta:{title:"AI产品发布会",dify_app_uuid:"5ec1c9f5-a9a3-4697-bb66-71793e8375a6",desc:"",icon:S("场景写作.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-26069221.js")),["static/index-26069221.js","static/Editor-ae5ccb2e.js","static/index-c6ba906c.js","static/index-87571f09.css","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-ecf8041a.js","static/index-55777a8a.js","static/index-224cdab4.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])}],L={class:"flex-1 flex flex-col overflow-hidden"},O={class:"text-gray-500 mb-2"},P=["href"],R={class:"flex items-center my-2"},D=["src"],k={class:"text-xl font-bold text-dark-200 mr-4"},C={class:"flex-1 overflow-hidden"},N={class:"flex items-center my-2"},$=["src"],U={class:"text-xl font-bold text-dark-200"},z={class:"text-dark-500 mb-6"},F={class:"flex justify-center"},H=i({__name:"Layout",setup(t){var i;const S=n(),H=l([]),J=l(null),q=l(null),K=l(!1),Z=l(0),B=l(!1),{locale:G}=s(),M=navigator.browserLanguage||navigator.language,Q=(null==(i=S.params)?void 0:i.lang)||M,W=l();G.value=Q;const X=()=>{K.value=!0},Y=e=>{B.value=e};o((()=>S),(()=>{q.value=JSON.parse(sessionStorage.getItem("nodeInfo")),J.value.setCurrentKey(node.name)}),{deep:!0});const ee=l({appUuid:S.params.appUuid});return p((async()=>{W.value="https://ai.medon.com.cn",await void E(ee.value).then((e=>{q.value=e[0]})).catch((e=>{})),"zh-CN"==localStorage.getItem("ai_apps_lang")&&(B.value=!0),V.map((e=>(e.label=e.meta.title,{label:e.type,children:V.filter((a=>a.type==e.type))}))).forEach((e=>{H.value.find((a=>a.type===e.label))||H.value.push(e)})),sessionStorage.getItem("nodeInfo")?q.value=JSON.parse(sessionStorage.getItem("nodeInfo")):q.value=H.value[0].children[0],c((()=>{})),Z.value=document.body.clientHeight-56})),(t,i)=>{var n,l,s,o,p,c;const E=e,S=j,V=d("router-view"),H=T;return r(),m("div",null,[u(E,{onIsZHChange:Y}),_(B)?(r(),v(a,{key:0})):g("",!0),f("div",{class:"h-full flex p-6",style:A({height:`${_(Z)}px`})},[f("main",L,[f("div",null,[f("div",O,[f("a",{class:"cursor-pointer hover:text-[#5298FF]",href:_(W)},h(t.$t("tool.home")),9,P),b("/"+h((null==(n=_(q))?void 0:n.appType)?t.$t(`${_(x)[null==(l=_(q))?void 0:l.appType]}`):"")+"/"+h(null==(s=_(q))?void 0:s.appName),1)]),f("div",R,[f("img",{class:"w-[38px] h-[38px] mr-4",src:(null==(o=_(q))?void 0:o.appIcon)?null==(p=_(q))?void 0:p.appIcon:"https://img.medsci.cn/web/prod/img/user_icon.png",alt:"这是icon"},null,8,D),f("div",k,h(null==(c=_(q))?void 0:c.appName),1),u(S,{plain:"",size:"small",onClick:X},{default:y((()=>[b(h(t.$t("tool.intro")),1)])),_:1})])]),f("div",C,[u(V,null,{default:y((({Component:e})=>[(r(),v(I(e)))])),_:1})])]),u(H,{modelValue:_(K),"onUpdate:modelValue":i[1]||(i[1]=e=>w(K)?K.value=e:null),"show-close":!1,width:"500"},{header:y((()=>{var e,a,t;return[f("div",N,[f("img",{class:"w-[38px] h-[38px] mr-4",src:(null==(e=_(q))?void 0:e.appIcon)?null==(a=_(q))?void 0:a.appIcon:"https://img.medsci.cn/web/prod/img/user_icon.png",alt:"这是icon"},null,8,$),f("div",U,h(null==(t=_(q))?void 0:t.appName),1)])]})),default:y((()=>{var e;return[f("div",z,h(null==(e=_(q))?void 0:e.appDescription),1),f("div",F,[u(S,{onClick:i[0]||(i[0]=e=>K.value=!1)},{default:y((()=>[b("我知道了")])),_:1})])]})),_:1},8,["modelValue"])],4)])}}},[["__scopeId","data-v-b125b886"]]);export{H as default};
