/* empty css                  */import{_ as e,c as a}from"./index-da9220e4.js";import{_ as t,u as i,a as n,r as l,b as s,w as o,o as p,n as c,c as d,d as r,e as m,f as _,g as u,h as v,i as g,j as f,t as h,k as b,l as x,m as y,p as I,q as w,s as A,E,v as j}from"https://static.medsci.cn/ai-write/static/index-6e66f8c2.js";import{g as S}from"./index-f4235c23.js";import{_ as T}from"./_plugin-vue_export-helper-1b428a4d.js";const V=[{name:"writingAssistant",path:"/writingAssistant",type:"医学写作",meta:{title:"论文",dify_app_uuid:"10c03431-1933-4f59-8848-e2e3ccb2c557",desc:"医学论文是医学领域的研究者撰写的，旨在探讨疾病病因、诊断、治疗、预防或医学理论、技术进展的学术性文章。它基于科学实验或临床观察，通过严谨的数据分析，提出新发现、新观点或验证已有理论，为医学实践提供科学依据和指导。",icon:S("AI写作助手.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-6064e2d2.js")),["static/index-6064e2d2.js","static/Editor-aa184f35.js","static/index-6e66f8c2.js","static/index-3fe8369a.css","static/_plugin-vue_export-helper-1b428a4d.js","static/Editor-360f0cd5.css","static/el-button-ad5c4430.css","static/el-select-c042598c.css","static/el-popper-0763e0cf.css","static/el-tooltip-4ed993c7.js","static/index-d4cd0a81.js","static/index-f4235c23.js","static/index-c956f223.css","static/el-input-7173329a.css","static/el-checkbox-b9562d8d.css","static/el-switch-3d8c06e2.css","static/el-message-b945acb2.css"])},{name:"intelligentPolishing",path:"/intelligentPolishing",type:"医学写作",meta:{title:"综述",dify_app_uuid:"4955c5cc-a366-45d8-8046-aa3044ff77bc",desc:"医学综述是对某一医学领域或专题在一定时期内研究成果的汇总和分析，旨在系统回顾研究进展，评价研究质量，探讨存在问题，预测未来方向，为研究者提供全面、深入的信息参考。",icon:S("AI智能润色.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-ec1cff09.js")),["static/index-ec1cff09.js","static/index-6e66f8c2.js","static/index-3fe8369a.css","static/index-d4cd0a81.js","static/_plugin-vue_export-helper-1b428a4d.js","static/index-86b5b4cd.css","static/el-button-ad5c4430.css","static/el-divider-07810808.css","static/el-input-7173329a.css","static/el-message-b945acb2.css"])},{name:"reviewer",path:"/reviewer",type:"医学写作",meta:{title:"研究方案",dify_app_uuid:"d7df5b59-280e-4dda-b0f2-700a12b0b1f9",desc:"医学研究方案是科研人员为探索医学问题而设计的详细计划，包括研究目的、假设、方法、样本选择、数据收集与分析步骤、预期结果及伦理考量等，确保研究过程科学、严谨、可重复。",icon:S("审稿人回复信.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-dcd0a600.js")),["static/index-dcd0a600.js","static/Editor-aa184f35.js","static/index-6e66f8c2.js","static/index-3fe8369a.css","static/_plugin-vue_export-helper-1b428a4d.js","static/Editor-360f0cd5.css","static/el-button-ad5c4430.css","static/el-select-c042598c.css","static/el-popper-0763e0cf.css","static/index-d4cd0a81.js","static/index-f4235c23.js","static/index-3c395fe1.css","static/el-switch-3d8c06e2.css","static/el-input-7173329a.css","static/el-message-b945acb2.css"])},{name:"rewrite",path:"/rewrite",type:"医学写作",meta:{title:"软文",dify_app_uuid:"d1fc1b66-47ce-45cd-9ff9-ee49a2e44881",desc:"医学软文是以医疗、健康领域为背景，通过撰写具有吸引力和可读性的文章，间接推广医疗产品、服务或健康理念的一种营销手段。",icon:S("AI降重改写.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-512b4586.js")),["static/index-512b4586.js","static/index-d4cd0a81.js","static/index-6e66f8c2.js","static/index-3fe8369a.css","static/index-f4235c23.js","static/el-button-ad5c4430.css","static/el-input-7173329a.css","static/el-message-b945acb2.css"])},{name:"topTitle",path:"/topTitle",type:"医学写作",meta:{title:"指南共识",dify_app_uuid:"7252420d-c356-4bed-9e06-ce6c3bc01270",desc:"指南共识是指在医学领域中，基于系统评价的证据和专家经验，针对特定临床问题制定的指导性建议或推荐意见。",icon:S("高分title生成器.svg"),bg:S("题材和论文章节的写作语料.png")},component:()=>t((()=>import("./index-4c8d4bc1.js")),["static/index-4c8d4bc1.js","static/el-tooltip-4ed993c7.js","static/index-d4cd0a81.js","static/index-6e66f8c2.js","static/index-3fe8369a.css","static/index-f4235c23.js","static/_plugin-vue_export-helper-1b428a4d.js","static/index-57f6bb72.css","static/el-button-ad5c4430.css","static/el-popper-0763e0cf.css","static/el-divider-07810808.css","static/el-input-7173329a.css","static/el-message-b945acb2.css"])},{name:"reviewSearch",path:"/reviewSearch",type:"医学写作",meta:{title:"病例报告",dify_app_uuid:"05544f18-9efb-42e4-ad5d-8ca78feaf56c",desc:"病例报告是详细记录并分析个别患者疾病发生、诊断、治疗及转归过程的医学文献，用于探讨罕见病、特殊临床表现或治疗经验。",icon:S("综述Review.svg"),bg:S("题材和论文章节的写作语料.png")},component:()=>t((()=>import("./index-da30e358.js")),["static/index-da30e358.js","static/Editor-aa184f35.js","static/index-6e66f8c2.js","static/index-3fe8369a.css","static/_plugin-vue_export-helper-1b428a4d.js","static/Editor-360f0cd5.css","static/el-button-ad5c4430.css","static/el-select-c042598c.css","static/el-popper-0763e0cf.css","static/index-d4cd0a81.js","static/index-f4235c23.js","static/index-d08ce8a4.css","static/el-checkbox-b9562d8d.css","static/el-switch-3d8c06e2.css","static/el-input-7173329a.css","static/el-message-b945acb2.css"])},{name:"reviewSearch",path:"/reviewSearch",type:"医学写作",meta:{title:"国自然基金写作",dify_app_uuid:"cc0c4388-8c02-4f1c-8a32-e0f3e84146c7",desc:"“国自然基金写作”是撰写国家自然科学基金申请书的过程，该过程需遵循科学基金的撰写要求和规范，涵盖项目名称、关键词、摘要、立项依据、研究目标、内容、方案及创新性等多个方面的撰写，旨在清晰、准确、有逻辑地展示研究项目的科学性、创新性和可行性，以获得基金资助。",icon:S("综述Review.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-da30e358.js")),["static/index-da30e358.js","static/Editor-aa184f35.js","static/index-6e66f8c2.js","static/index-3fe8369a.css","static/_plugin-vue_export-helper-1b428a4d.js","static/Editor-360f0cd5.css","static/el-button-ad5c4430.css","static/el-select-c042598c.css","static/el-popper-0763e0cf.css","static/index-d4cd0a81.js","static/index-f4235c23.js","static/index-d08ce8a4.css","static/el-checkbox-b9562d8d.css","static/el-switch-3d8c06e2.css","static/el-input-7173329a.css","static/el-message-b945acb2.css"])},{name:"reviewSearch",path:"/chat",type:"医学会话",meta:{title:"学术写作问答",dify_app_uuid:"c81251e7-9269-413b-95d3-0a4b88ce84b9",desc:"",icon:S("场景写作.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-da30e358.js")),["static/index-da30e358.js","static/Editor-aa184f35.js","static/index-6e66f8c2.js","static/index-3fe8369a.css","static/_plugin-vue_export-helper-1b428a4d.js","static/Editor-360f0cd5.css","static/el-button-ad5c4430.css","static/el-select-c042598c.css","static/el-popper-0763e0cf.css","static/index-d4cd0a81.js","static/index-f4235c23.js","static/index-d08ce8a4.css","static/el-checkbox-b9562d8d.css","static/el-switch-3d8c06e2.css","static/el-input-7173329a.css","static/el-message-b945acb2.css"])},{name:"reviewSearch",path:"/chat",type:"医学会话",meta:{title:"AI产品发布会",dify_app_uuid:"5ec1c9f5-a9a3-4697-bb66-71793e8375a6",desc:"",icon:S("场景写作.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-da30e358.js")),["static/index-da30e358.js","static/Editor-aa184f35.js","static/index-6e66f8c2.js","static/index-3fe8369a.css","static/_plugin-vue_export-helper-1b428a4d.js","static/Editor-360f0cd5.css","static/el-button-ad5c4430.css","static/el-select-c042598c.css","static/el-popper-0763e0cf.css","static/index-d4cd0a81.js","static/index-f4235c23.js","static/index-d08ce8a4.css","static/el-checkbox-b9562d8d.css","static/el-switch-3d8c06e2.css","static/el-input-7173329a.css","static/el-message-b945acb2.css"])}],L={class:"flex-1 flex flex-col overflow-hidden"},O={class:"text-gray-500 mb-2"},P={class:"flex items-center my-2"},R=["src"],D={class:"text-xl font-bold text-dark-200 mr-4"},k={class:"flex-1 overflow-hidden"},C={class:"flex items-center my-2"},N=["src"],$={class:"text-xl font-bold text-dark-200"},z={class:"text-dark-500 mb-6"},F={class:"flex justify-center"},H=T({__name:"Layout",setup(t){var S;const T=i(),H=n(),J=l([]),q=l(null),K=l(null),U=l(!1),Z=l(0),B=l(!1),{locale:G}=s(),M=navigator.browserLanguage||navigator.language,Q=(null==(S=H.params)?void 0:S.lang)||M;G.value=Q;const W=()=>{U.value=!0},X=()=>{T.push("/")},Y=e=>{B.value=e};return o((()=>H),(()=>{K.value=JSON.parse(sessionStorage.getItem("nodeInfo")),q.value.setCurrentKey(node.name)}),{deep:!0}),p((()=>{"zh-CN"==localStorage.getItem("ai_apps_lang")&&(B.value=!0),V.map((e=>(e.label=e.meta.title,{label:e.type,children:V.filter((a=>a.type==e.type))}))).forEach((e=>{J.value.find((a=>a.type===e.label))||J.value.push(e)})),sessionStorage.getItem("nodeInfo")?K.value=JSON.parse(sessionStorage.getItem("nodeInfo")):K.value=J.value[0].children[0],c((()=>{})),Z.value=document.body.clientHeight-56})),(t,i)=>{var n,l,s,o;const p=e,c=E,S=d("router-view"),T=j;return r(),m("div",null,[_(p,{onIsZHChange:Y}),u(B)?(r(),v(a,{key:0})):g("",!0),f("div",{class:"h-full flex p-6",style:A({height:`${u(Z)}px`})},[f("main",L,[f("div",null,[f("div",O,[f("span",{class:"cursor-pointer hover:text-[#5298FF]",onClick:X},h(t.$t("tool.home")),1),b("/"+h(t.$t(`${u(x)[(null==(n=u(K))?void 0:n.appType)||""]}`))+"/"+h(null==(l=u(K))?void 0:l.appName),1)]),f("div",P,[f("img",{class:"w-[38px] h-[38px] mr-4",src:null==(s=u(K))?void 0:s.appIcon,alt:"这是icon"},null,8,R),f("div",D,h(null==(o=u(K))?void 0:o.appName),1),_(c,{plain:"",size:"small",onClick:W},{default:y((()=>[b(h(t.$t("tool.intro")),1)])),_:1})])]),f("div",k,[_(S,null,{default:y((({Component:e})=>[(r(),v(I(e)))])),_:1})])]),_(T,{modelValue:u(U),"onUpdate:modelValue":i[1]||(i[1]=e=>w(U)?U.value=e:null),"show-close":!1,width:"500"},{header:y((()=>{var e,a,t;return[f("div",C,[f("img",{class:"w-[38px] h-[38px] mr-4",src:(null==(e=u(K))?void 0:e.appIcon)?null==(a=u(K))?void 0:a.appIcon:"https://img.medsci.cn/web/prod/img/user_icon.png",alt:"这是icon"},null,8,N),f("div",$,h(null==(t=u(K))?void 0:t.appName),1)])]})),default:y((()=>{var e;return[f("div",z,h(null==(e=u(K))?void 0:e.appDescription),1),f("div",F,[_(c,{onClick:i[0]||(i[0]=e=>U.value=!1)},{default:y((()=>i[2]||(i[2]=[b("我知道了")]))),_:1})])]})),_:1},8,["modelValue"])],4)])}}},[["__scopeId","data-v-0dfd4dda"]]);export{H as default};
