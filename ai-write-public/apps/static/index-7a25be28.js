import{y as e,at as a,Z as s,ag as o,ah as n,au as t,av as l,d as i,e as r,j as c,t as d,i as u,F as m,z as g,ad as h,S as p,k as v}from"./index-b7023353.js";import{_ as w}from"./_plugin-vue_export-helper-1b428a4d.js";const I={class:"header ms-header-media"},f={class:"ms-header"},y={class:"wrapper"},k={class:"main-menu-placeholder wrapper clearfix",style:{height:"56px",display:"block !important"}},_={id:"main-menu",class:"ms-header-nav"},$={class:"header-top header-user",id:"user-info-header"},S={key:0,class:"change_lang"},M={class:"current_lang"},b={class:"ms-link"},C={class:"ms-dropdown-menu",ref:"menu"},T={class:"new-header-avator-pop",id:"new-header-avator"},L={class:"new-header-bottom",style:{padding:"0"}},x={class:"langUl"},A=["onClick"],U={class:"index-user-img_right"},j={href:"#"},D={class:"img-area"},N=["src"],q={class:"ms-dropdown-menu",ref:"menu1"},E={class:"new-header-avator-pop",id:"new-header-avator"},Z={class:"new-header-top"},z={class:"new-header-info"},H=["src"],B={class:"new-header-name"};const F=w({data:()=>({avatar:"",userInfo:null,isIncludeTool:!1,selectedLanguage:"",langs:[]}),watch:{userInfoCookie(e,a){}},computed:{baseUrl:()=>"https://www.medsci.cn/",userInfoCookie:()=>e.get("userInfo")},methods:{changeImg(){this.avatar="https://img.medsci.cn/web/img/user_icon.png"},getCookieUserInfo:()=>e.get("userInfo"),showMenu(){this.$refs.menu.style.display="block"},hideMenu(){this.$refs.menu.style.display="none"},showMenu1(){this.$refs.menu1.style.display="block"},hideMenu1(){this.$refs.menu1.style.display="none"},toggle(e){const a=this.$route.path,s=this.$route.params.lang;if(s){const o=a.replace(`/${s}`,"");this.$router.push(`/${e}${o}`)}else this.$router.push(`/${e}${a}`);window.localStorage.setItem("ai_apps_lang",e),this.selectedLanguage=e,this.$i18n.locale=e,this.$emit("getAppLang",e),"zh-CN"==localStorage.getItem("ai_apps_lang")?this.$emit("isZHChange",!0):this.$emit("isZHChange",!1),this.$refs.menu.style.display="none"},async logout(){e.remove("userInfo",{domain:".medon.com.cn"}),e.remove("userInfo",{domain:".medsci.cn"}),e.remove("userInfo",{domain:"localhost"}),localStorage.removeItem("conversation"),localStorage.removeItem("writeContent");let s=localStorage.getItem("socialType");s&&35==s||s&&36==s?a().then((()=>{localStorage.removeItem("hasuraToken"),localStorage.removeItem("yudaoToken"),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid"),location.reload()})).catch((e=>{})):(localStorage.removeItem("hasuraToken"),localStorage.removeItem("yudaoToken"),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid"),window.location.origin.includes("medsci.cn")?window.location.href="https://www.medsci.cn/sso_logout?redirectUrl="+window.location.href:window.location.href="https://portal-test.medon.com.cn/sso_logout?redirectUrl="+window.location.href)},async loginAccount(){let e=await this.getLocationData();e&&"中国"!=e?this.$router.push("/login"):window.addLoginDom()},medsciSearch(){let e=document.getElementById("medsciSearchKeyword").value;e?window.open(`https://www.medsci.cn/search?q=${e}`,"_blank"):window.open("https://search.medsci.cn/","_blank")},async getLocationData(){let e=s("current_location_country",1);return e||await o().then((e=>{n("current_location_country",e)})),e=s("current_location_country",1),e},getAppLangsData(){t().then((e=>{e.map((e=>{e.status||this.langs.push({name:e.value,value:e.remark})}))}))}},mounted(){var a,s;this.getAppLangsData(),this.userInfo=e.get("userInfo")?JSON.parse(e.get("userInfo")):null,this.isIncludeTool=window.location.href.includes("/tool"),setTimeout((()=>{this.selectedLanguage=l()}),0),this.avatar=(null==(a=this.userInfo)?void 0:a.avatar)?null==(s=this.userInfo)?void 0:s.avatar:"https://img.medsci.cn/web/img/user_icon.png"}},[["render",function(e,a,s,o,n,t){var l,v,w,F,G;return i(),r("div",I,[c("div",f,[c("div",y,[c("div",k,[a[10]||(a[10]=c("div",{class:"ms-header-img"},[c("img",{src:"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png",alt:""})],-1)),c("div",_,[c("div",$,[c("ul",null,[c("li",{class:"index-user-img index-user-img_left",onMouseover:a[0]||(a[0]=(...e)=>t.showMenu&&t.showMenu(...e)),onMouseout:a[1]||(a[1]=(...e)=>t.hideMenu&&t.hideMenu(...e)),onClick:a[2]||(a[2]=(...e)=>t.showMenu&&t.showMenu(...e))},[n.isIncludeTool?u("",!0):(i(),r("div",S,[c("span",M,d(null==(l=n.langs.filter((e=>e.value==n.selectedLanguage))[0])?void 0:l.name),1),c("span",b,d(e.$t("market.switch")),1)])),c("div",C,[c("div",T,[c("div",L,[c("div",x,[(i(!0),r(m,null,g(n.langs,(e=>(i(),r("p",{key:e,onClick:h((a=>t.toggle(e.value)),["stop"]),class:p({langItemSelected:e.value===n.selectedLanguage})},d(e.name),11,A)))),128))])])])],512)],32),(null==(v=n.userInfo)?void 0:v.userId)?(i(),r("li",{key:1,class:"index-user-img",onMouseover:a[7]||(a[7]=(...e)=>t.showMenu1&&t.showMenu1(...e)),onMouseout:a[8]||(a[8]=(...e)=>t.hideMenu1&&t.hideMenu1(...e))},[c("a",j,[c("div",D,[c("img",{src:n.avatar,onError:a[4]||(a[4]=(...e)=>t.changeImg&&t.changeImg(...e)),alt:""},null,40,N)])]),c("div",q,[c("div",E,[c("a",{class:"new-header-exit ms-statis","ms-statis":"logout",href:"#",onClick:a[5]||(a[5]=e=>t.logout())},d(e.$t("market.logout")),1),c("div",Z,[c("div",z,[c("img",{class:"new-header-avatar",src:n.avatar,onError:a[6]||(a[6]=(...e)=>t.changeImg&&t.changeImg(...e)),alt:""},null,40,H),c("div",B,[c("span",null,d((null==(w=n.userInfo)?void 0:w.realName)?null==(F=n.userInfo)?void 0:F.realName:null==(G=n.userInfo)?void 0:G.userName),1)])])])])],512)],32)):(i(),r(m,{key:0},[c("li",U,[c("a",{href:"javascript: void(0)",class:"ms-link",onClick:a[3]||(a[3]=(...e)=>t.loginAccount&&t.loginAccount(...e))},d(e.$t("market.login")),1)]),a[9]||(a[9]=c("li",null,null,-1))],64))])])])])])])])}],["__scopeId","data-v-d71848c2"]]),G={class:"assistant-container"};const J=w({name:"AssistantComponent",data:()=>({}),methods:{}},[["render",function(e,a,s,o,n,t){return i(),r("div",G,a[0]||(a[0]=[c("div",{class:"assistant-icon"},[c("img",{src:"/apps/static/kefu-ae1166e2.png",class:"fas fa-user-astronaut",alt:"客服"})],-1),c("div",{class:"qr-code"},[c("img",{src:"/apps/static/qrcode-4bdb4a15.png",alt:"QR Code"}),v(" 扫码添加小助手 ")],-1)]))}],["__scopeId","data-v-459f35cd"]]);export{F as _,J as c};
