/* empty css                  *//* empty css                 */import{s as e}from"./index-fc8d05ea.js";import{r as a,aP as l,d as s,e as t,f as u,g as r,p as i,j as n,m as o,k as p,F as v,y as m,V as d,h as c,aR as f,i as x,a6 as y,B as j,E as h,G as g}from"./index-e4f399cd.js";import{s as k}from"./index-992a10bb.js";/* empty css                   */const w={class:"pt-10 overflow-auto h-full"},F={class:"flex justify-center my-10"},_={key:0},V=["innerHTML"],b={class:"flex justify-center mt-10"},E={__name:"index",setup(E){const S=a(""),z=a([]),A=a([]),C=a(5),H=()=>{if(a=S.value,/[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi.exec(a)||!S.value)return y.warning("请输入英文内容");var a;e({text:S.value}).then((e=>{e&&e&&e.data&&(z.value=e.data,z.value=k(z.value),C.value=5,J())}))},J=()=>{let e=[];5==C.value?(e=[0,5],C.value=3):3==C.value?(e=[5,8],C.value=2):2==C.value&&(e=[8,10],C.value=5),A.value=JSON.parse(JSON.stringify(z.value)).slice(e[0],e[1])};return(e,a)=>{const y=j,k=h,E=g,z=l("copy");return s(),t("div",w,[u(y,{modelValue:r(S),"onUpdate:modelValue":a[0]||(a[0]=e=>i(S)?S.value=e:null),rows:8,type:"textarea",placeholder:"请输入不超过250单词的段落","show-word-limit":"",resize:"none",maxlength:250},null,8,["modelValue"]),n("div",F,[u(k,{type:"primary",onClick:H},{default:o((()=>a[1]||(a[1]=[p("改 写")]))),_:1})]),r(A)&&r(A).length?(s(),t("div",_,[(s(!0),t(v,null,m(r(A),((e,a)=>(s(),t("div",{class:"flex justify-center mb-6 relative",key:a},[n("span",{innerHTML:e.textShow,class:"text-[18px]"},null,8,V),d((s(),c(E,{title:"复制",size:"16",color:"#909399",class:"cursor-pointer ml-2 absolute"},{default:o((()=>[u(r(f))])),_:2},1024)),[[z,e.text]])])))),128)),n("div",b,[u(k,{type:"primary",link:"",onClick:J},{default:o((()=>a[2]||(a[2]=[p("换一换")]))),_:1})])])):x("",!0)])}}};export{E as default};
