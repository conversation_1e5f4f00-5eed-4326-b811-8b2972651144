/* empty css                  *//* empty css                     *//* empty css                 */import{b as e,a as l,u as o,r as a,N as s,x as t,o as i,az as n,d as r,e as c,j as u,t as d,f as m,m as p,k as g,aA as v,a3 as _,aF as f,aG as b,B as I,aC as h,E as k,aD as B,aE as y}from"https://static.medsci.cn/ai-write/static/index-f1efeeee.js";import{_ as x}from"./_plugin-vue_export-helper-1b428a4d.js";const w={class:"bg-background text-foreground antialiased min-h-screen flex items-center justify-center"},T={class:"cl-rootBox cl-signUp-root justify-center"},$={class:"cl-cardBox cl-signUp-start"},S={class:"cl-card cl-signUp-start"},C={class:"cl-header"},U={class:"cl-headerTitle"},j={class:"cl-headerSubtitle"},V={class:"cl-main"},N={class:"cl-socialButtonsRoot"},q={class:"cl-socialButtons"},A={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},z={class:"cl-socialButtonsBlockButton-d"},R={class:"cl-socialButtons"},L={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},O={class:"cl-socialButtonsBlockButton-d"},P={class:"cl-socialButtonsRoot"},G={class:"cl-internal-1pnppin"},J={class:"cl-internal-742eeh"},E={class:"cl-internal-2iusy0"},F={class:"cl-footer 🔒️ cl-internal-4x6jej"},W={class:"cl-footerAction cl-footerAction__signUp cl-internal-1rpdi70"},Z={class:"cl-footerActionText cl-internal-kyvqj0","data-localization-key":"signUp.start.actionText"},H=x({__name:"index",setup(x){var H;const{t:M}=e(),D=l(),Q=o(),K=D.params.socialType,X=D.query.authCode,Y=D.query.authState,ee=a(),le=a({email:"",password:"",emailCode:"",userName:""}),oe=a((null==location?void 0:location.origin.includes("medon.com.cn"))||(null==location?void 0:location.origin.includes("medsci.cn"))),ae=s({userName:[{required:!0,message:M("tool.username_cannot_be_empty"),trigger:"blur"}],password:[{required:!0,message:M("tool.password_cannot_be_empty"),trigger:"blur"},{min:8,max:20,message:M("tool.pwdLength"),trigger:"blur"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[a-zA-Z\d\W_]{8,20}$/,message:M("tool.password_includes_uppercase_lowercase_numbers_and_symbols"),trigger:"blur"}],emailCode:[{required:!0,message:M("tool.verification_code_cannot_be_empty"),trigger:"blur"},{min:6,max:6,message:M("tool.verification_code_must_be_6_digits"),trigger:"blur"}],email:[{required:!0,message:M("tool.email_address_cannot_be_empty"),trigger:"blur"},{type:"email",message:M("tool.please_enter_a_valid_email_address"),trigger:"blur"}]}),se=a(!1),te=a(60),ie=a(M("tool.send_verification_code")),ne=t.get("userInfo")?null==(H=JSON.parse(t.get("userInfo")))?void 0:H.userId:"",re=e=>{v(e).then((e=>{window.location.href=e}))},ce=()=>{if(!le.value.email)return void _.error(M("tool.email_does_not_exist"));let e={email:le.value.email,type:"RegisterCode"};f(e).then((e=>{e&&(()=>{se.value=!0,ie.value=`${te.value}${M("tool.retry_after_seconds")}`;let e=setInterval((()=>{te.value-=1,ie.value=`${te.value}${M("tool.retry_after_seconds")}`,te.value<=0&&(clearInterval(e),te.value=60,ie.value=M("tool.send_verification_code"),se.value=!1)}),1e3)})()}))};return i((()=>{ne?Q.push("/"):K&&X&&Y&&n(K,X,Y).then((e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),e.userInfo.userId=e.userInfo.openid,e.userInfo.plaintextUserId=e.userInfo.socialUserId,e.userInfo.token={accessToken:e.userInfo.openid,accessTokenExpireTime:63072e4,refreshToken:e.userInfo.openid},location.origin.includes(".medsci.cn")?t.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medsci.cn"}):location.origin.includes(".medon.com.cn")?t.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medon.com.cn"}):t.set("userInfo",JSON.stringify(e.userInfo),{expires:365}),Q.push("/"))}))})),(e,l)=>{const o=I,a=h,s=k,t=B,i=y;return r(),c("div",w,[u("div",T,[u("div",$,[u("div",S,[u("div",C,[u("div",null,[u("h1",U,d(e.$t("tool.create_account")),1),u("p",j,d(e.$t("tool.registration_greeting")),1)])]),u("div",V,[u("div",N,[u("div",q,[u("button",A,[u("span",z,[l[7]||(l[7]=u("span",null,[u("img",{src:"https://img.medsci.cn/202412/cc7c7687b4804460a85d46c629132724-vcHTUHMGIylQ.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1)),u("span",{class:"cl-socialButtonsBlockButtonText",onClick:l[0]||(l[0]=e=>re(35))},d(e.$t("tool.continue_with_google")),1)])])]),u("div",R,[u("button",L,[u("span",O,[l[8]||(l[8]=u("span",null,[u("img",{src:"https://img.medsci.cn/202412/f9bf95f463c04d3e801aa6e97ef3d4b8-OSsmrMWR677i.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1)),u("span",{class:"cl-socialButtonsBlockButtonText",onClick:l[1]||(l[1]=e=>re(36))},d(e.$t("tool.continue_with_facebook")),1)])])])]),l[11]||(l[11]=u("div",{class:"cl-dividerRow"},[u("div",{class:"cl-dividerLine"}),u("p",{class:"cl-dividerText"},"or"),u("div",{class:"cl-dividerLine"})],-1)),u("div",P,[m(t,{ref_key:"ruleFormRef",ref:ee,style:{"max-width":"600px"},model:le.value,rules:ae,"label-width":"auto",class:"demo-ruleForm","label-position":"left",size:e.formSize,"status-icon":""},{default:p((()=>[m(a,{label:e.$t("tool.username"),prop:"userName"},{default:p((()=>[m(o,{modelValue:le.value.userName,"onUpdate:modelValue":l[2]||(l[2]=e=>le.value.userName=e)},null,8,["modelValue"])])),_:1},8,["label"]),m(a,{label:e.$t("tool.email"),prop:"email"},{default:p((()=>[m(o,{modelValue:le.value.email,"onUpdate:modelValue":l[3]||(l[3]=e=>le.value.email=e)},null,8,["modelValue"])])),_:1},8,["label"]),m(a,{label:e.$t("tool.verification_code"),prop:"emailCode"},{default:p((()=>[m(o,{modelValue:le.value.emailCode,"onUpdate:modelValue":l[4]||(l[4]=e=>le.value.emailCode=e)},{append:p((()=>[m(s,{onClick:ce,disabled:se.value,type:"primary"},{default:p((()=>[g(d(ie.value),1)])),_:1},8,["disabled"])])),_:1},8,["modelValue"])])),_:1},8,["label"]),m(a,{label:e.$t("tool.password"),prop:"password",style:{"padding-bottom":"20px"}},{default:p((()=>[m(o,{modelValue:le.value.password,"onUpdate:modelValue":l[5]||(l[5]=e=>le.value.password=e),"show-password":"true"},null,8,["modelValue"])])),_:1},8,["label"]),m(a,null,{default:p((()=>[u("div",G,[l[10]||(l[10]=u("div",{id:"clerk-captcha",class:"cl-internal-3s7k9k"},null,-1)),u("div",J,[m(s,{class:"cl-formButtonPrimary cl-button 🔒️ cl-internal-ttumny",onClick:l[6]||(l[6]=e=>(async e=>{e&&await e.validate(((e,l)=>{e&&b(le.value).then((e=>{e&&(_({type:"success",message:"注册成功，即将跳转到登录页..."}),setTimeout((()=>{location.href=oe.value?"/apps/login":"login"}),1e3))}))}))})(ee.value))},{default:p((()=>[u("span",E,[g(d(e.$t("tool.continue")),1),l[9]||(l[9]=u("svg",{class:"cl-buttonArrowIcon 🔒️ cl-internal-1c4ikgf"},[u("path",{fill:"currentColor",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"m7.25 5-3.5-2.25v4.5L7.25 5Z"})],-1))])])),_:1})])])])),_:1})])),_:1},8,["model","rules","size"])])])]),u("div",F,[u("div",W,[u("span",Z,d(e.$t("tool.alreadyhaveanaccount")),1),m(i,{href:oe.value?"/apps/login":"/login",class:"cl-footerActionLink"},{default:p((()=>[g(d(e.$t("tool.signIn")),1)])),_:1},8,["href"])])])])])])}}},[["__scopeId","data-v-936ae8d6"]]);export{H as default};
