import{a as e,_ as l}from"./Editor-f0ec86f9.js";/* empty css                  *//* empty css                    *//* empty css                  *//* empty css                 */import{m as a}from"./index-de442592.js";import{s as t}from"./index-a67f975f.js";/* empty css                   */import{_ as s}from"./_plugin-vue_export-helper-1b428a4d.js";import{r as o,N as n,o as i,d as r,e as u,j as d,f as p,g as m,q as c,m as f,k as v,R as x,t as g,F as y,y as h,i as b,h as j,s as V,a3 as _,B as k,E as S,ae as w,aH as z,aI as N}from"https://static.medsci.cn/ai-write/static/index-7d44b454.js";/* empty css                  *//* empty css                  */const T={class:"flex h-full overflow-auto"},C={class:"w-[58%]"},H={class:"flex items-center bg-[#f7f8fa] p-2 rounded-md h-[52px] mb-2"},M={class:"flex items-center my-8"},U={class:"flex items-center mr-4"},q={class:"flex items-center mr-4"},L={class:"mr-2"},W=["innerHTML","onClick"],$=["innerHTML"],E={class:"flex-1 ml-4 box-right"},I=s({__name:"index",setup(s){const I=o("literature retrieval"),A=o(""),B=o(1),F=o(!0),G=o([]),P=o({}),R=o("请输入关键词或短句（中/英）"),D=n({pageNo:1,pageSize:20}),J=o(344),K=e=>{if(!I.value)return _.warning("请输入关键词或短句");1==e&&(D.pageNo=1);let l=G.value.map((e=>"cns"==e?{field:"cns",opt:2,vals:[e],val:"",synonymsWordVos:[]}:{field:"effect",opt:2,vals:[e],val:"",synonymsWordVos:[]}));a("review",{key:I.value,page:D.pageNo-1,size:D.pageSize,allMySentence:0,allMyGroupSentence:0,synonymsHistory:0,mulSearchConditions:l}).then((e=>{e&&e.data&&(P.value=e.data,P.value.content=t(P.value.content))}))},O=()=>{B.value++};return i((()=>{K(),J.value=document.querySelector(".box-right").offsetWidth,window.onresize=()=>{J.value=document.querySelector(".box-right").offsetWidth}})),(a,t)=>{const s=k,o=S,n=w,i=z,_=N,Q=e,X=l;return r(),u("div",T,[d("div",C,[d("div",H,[p(s,{class:"h-full !text-[24px]",modelValue:m(I),"onUpdate:modelValue":t[0]||(t[0]=e=>c(I)?I.value=e:null),placeholder:m(R),clearable:""},null,8,["modelValue","placeholder"]),p(o,{type:"primary",onClick:t[1]||(t[1]=e=>K(1))},{default:f((()=>t[7]||(t[7]=[v("查 询")]))),_:1})]),d("div",M,[d("div",U,[d("span",{class:x(["mr-2",m(F)?"text-[#409eff]":""])},"翻译",2),p(n,{modelValue:m(F),"onUpdate:modelValue":t[2]||(t[2]=e=>c(F)?F.value=e:null)},null,8,["modelValue"])]),d("div",q,[t[12]||(t[12]=d("span",{class:"mr-2"},"影响因子：",-1)),p(_,{modelValue:m(G),"onUpdate:modelValue":t[3]||(t[3]=e=>c(G)?G.value=e:null)},{default:f((()=>[p(i,{label:"3"},{default:f((()=>t[8]||(t[8]=[v(g("<3分"))]))),_:1}),p(i,{label:"5"},{default:f((()=>t[9]||(t[9]=[v("3-10分")]))),_:1}),p(i,{label:"10"},{default:f((()=>t[10]||(t[10]=[v(g(">10分"))]))),_:1}),p(i,{label:"cns"},{default:f((()=>t[11]||(t[11]=[v(g("CNS"))]))),_:1})])),_:1},8,["modelValue"])])]),d("div",null,[(r(!0),u(y,null,h(m(P).content,((e,l)=>(r(),u("div",{class:"flex mb-8",key:l},[d("div",L,g(l+1)+".",1),d("div",null,[d("div",{class:"text-[18px] text-[#5e5e5e] cursor-pointer html-value",innerHTML:e.text,onClick:a=>((e,l)=>{let a=`${e}.${l}`.replace(/\n/g,"");A.value+=`<p>${a}</p>`})(l+1,e.text)},null,8,W),m(F)?(r(),u("div",{key:0,class:"text-[16px] text-[#0a76f5] mt-4",innerHTML:e.translateText},null,8,$)):b("",!0)])])))),128))]),m(P)&&m(P).eleTotal?(r(),j(Q,{key:0,class:"pb-10",total:m(P).eleTotal,page:m(D).pageNo,"onUpdate:page":t[4]||(t[4]=e=>m(D).pageNo=e),limit:m(D).pageSize,"onUpdate:limit":t[5]||(t[5]=e=>m(D).pageSize=e),onPagination:K},null,8,["total","page","limit"])):b("",!0)]),d("div",E,[(r(),j(X,{class:"h-[380px] fixed z-99",style:V({width:m(J)+"px"}),modelValue:m(A),"onUpdate:modelValue":t[6]||(t[6]=e=>c(A)?A.value=e:null),onClear:O,key:m(B)},null,8,["style","modelValue"]))])])}}},[["__scopeId","data-v-300433dc"]]);export{I as default};
