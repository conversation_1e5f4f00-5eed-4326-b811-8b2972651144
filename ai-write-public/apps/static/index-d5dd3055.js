import"https://static.medsci.cn/ai-write/static/index-01c25d3e.js";const s=s=>new URL(Object.assign({"../assets/svg/AI写作助手.svg":"/apps/static/AI写作助手-2149caf6.svg","../assets/svg/AI智能润色.svg":"/apps/static/AI智能润色-24c508b1.svg","../assets/svg/AI降重改写.svg":"/apps/static/AI降重改写-a42f33d6.svg","../assets/svg/TCGA可视化.svg":"/apps/static/TCGA可视化-fc795b7b.svg","../assets/svg/Target.svg":"/apps/static/Target-81d29a6a.svg","../assets/svg/copy.png":"/apps/static/copy-50df1904.png","../assets/svg/loading.png":"/apps/static/loading-c174753b.png","../assets/svg/个案报道.svg":"/apps/static/个案报道-a9a0668e.svg","../assets/svg/中文版GEO.svg":"/apps/static/中文版GEO-e6610472.svg","../assets/svg/中文版Pubmed.svg":"/apps/static/中文版Pubmed-d33096a2.svg","../assets/svg/免疫治疗.svg":"/apps/static/免疫治疗-a878bd51.svg","../assets/svg/免疫评分.svg":"/apps/static/免疫评分-6d9119b4.svg","../assets/svg/包季.png":"/apps/static/包季-5770f926.png","../assets/svg/包年.png":"/apps/static/包年-7a835991.png","../assets/svg/包月.png":"/apps/static/包月-9f366f1b.png","../assets/svg/单细胞分析.svg":"/apps/static/单细胞分析-282a601e.svg","../assets/svg/国自然数分.svg":"/apps/static/国自然数分-62909a4d.svg","../assets/svg/国自然查询分析以及申请书写作.png":"/apps/static/国自然查询分析以及申请书写作-c9fdefa7.png","../assets/svg/图例写作.svg":"/apps/static/图例写作-0ef6a31c.svg","../assets/svg/场景写作.svg":"/apps/static/场景写作-f992ca71.svg","../assets/svg/基于AI的写作文本加工.png":"/apps/static/基于AI的写作文本加工-143ed4a6.png","../assets/svg/实验方案设计.svg":"/apps/static/实验方案设计-ccb16be8.svg","../assets/svg/审稿人回复信.svg":"/apps/static/审稿人回复信-dd8eaf43.svg","../assets/svg/批量生存分析.svg":"/apps/static/批量生存分析-c16d4317.svg","../assets/svg/搜索.svg":"/apps/static/搜索-276c35c0.svg","../assets/svg/机制图流程图绘制.svg":"/apps/static/机制图流程图绘制-a64d8c0e.svg","../assets/svg/泛癌分析.svg":"/apps/static/泛癌分析-db8f7f51.svg","../assets/svg/生信零代码.svg":"/apps/static/生信零代码-4d3df946.svg","../assets/svg/生物信息学公共数据库分析.png":"/apps/static/生物信息学公共数据库分析-2fd13650.png","../assets/svg/相关性分析.svg":"/apps/static/相关性分析-aa786fac.svg","../assets/svg/细胞动物模型查询.svg":"/apps/static/细胞动物模型查询-3c13e209.svg","../assets/svg/综述Review.svg":"/apps/static/综述Review-6eaff47f.svg","../assets/svg/语法纠错.svg":"/apps/static/语法纠错-38518fee.svg","../assets/svg/课题思路助手.svg":"/apps/static/课题思路助手-b8870ca9.svg","../assets/svg/题材和论文章节的写作语料.png":"/apps/static/题材和论文章节的写作语料-b0e8d171.png","../assets/svg/高分title生成器.svg":"/apps/static/高分title生成器-95eaf01d.svg","../assets/svg/高分选题.svg":"/apps/static/高分选题-7dc9c155.svg"})[`../assets/svg/${s}`],self.location).href,a=(s,a=5)=>{if(s&&s.length>0){if(s.length>5){let t=Math.ceil(s.length/a),g=[];for(let a=0;a<s.length;a+=t)g.push(s.slice(a,a+t));g.forEach((s=>{s.sort((()=>Math.random()-.5))}));return g.reduce(((s,a)=>s.concat(a)),[])}return s.sort((()=>Math.random()-.5))}return[]};export{s as g,a as s};
