/* empty css                  *//* empty css                */import{r as e,y as a,u as t,w as l,d as o,e as i,j as n,t as s,g as r,h as u,m as d,F as c,z as v,f as p,A as f,k as m,i as h,B as g,C as y,D as b,G as w,H as x,E as _,I as k,J as I,K as S,L as C,M as $,N as T,O as B,P as z,n as j,o as N,Q as q,R as A,S as E,T as O,U as V,V as R,a as M,b as U,W as L,X as H,Y as W,Z as D,$ as J,c as P,a0 as F,a1 as X,q as Z,a2 as Y,a3 as G,a4 as K,a5 as Q,a6 as ee,a7 as ae,a8 as te,a9 as le,aa as oe}from"./index-77941249.js";import{g as ie}from"./index-655b17c5.js";/* empty css                 *//* empty css                  *//* empty css                  */import{c as ne,r as se,g as re,s as ue,i as de,o as ce,a as ve,n as pe,m as fe,b as me,u as he,d as ge,e as ye,f as be,h as we,j as xe,k as _e,w as ke,l as Ie,p as Se,t as Ce,q as $e,v as Te,x as Be,y as ze,z as je,A as Ne,B as qe,C as Ae,D as Ee,E as Oe,F as Ve,G as Re,H as Me,I as Ue,J as Le,K as He,L as We,M as De,N as Je}from"./use-touch-42c36918.js";import{r as Pe,a as Fe,f as Xe}from"./use-route-2648172c.js";const Ze={class:"p-3 flex-1 rounded-md"},Ye={class:"text-[14px] font-bold mb-2 text-gray-600"},Ge={__name:"InputField",props:{type:{type:String,required:!0},label:{type:String,required:!0},value:{type:[String,Number],required:!0},required:{type:Boolean,required:!0},placeholder:{type:String,required:!0},max_length:{type:Number,required:!1},options:{type:Array,required:!1},fileVerify:{type:Array,required:!1,default:()=>[""]}},emits:["update:value"],setup(I,{expose:S,emit:C}){const $=e(a.get("userInfo")?JSON.parse(a.get("userInfo")):{}),T=t(),B=e([]),z=e({jpg:"image",jpeg:"image",png:"image",webp:"image",gif:"image",svg:"image",mp4:"video",mov:"video",mpeg:"video",mpga:"video",mp3:"audio",m4a:"audio",wav:"audio",webm:"audio",amr:"audio",txt:"document",markdown:"document",md:"document",mdx:"document",pdf:"document",html:"document",htm:"document",xlsx:"document",xls:"document",docx:"document",csv:"document",eml:"document",msg:"document",pptx:"document",xml:"document",epub:"document"}),j=e(""),N=I,q=N.type,A=N.fileVerify,E=N.label,O=N.required,V=N.max_length,R=N.options;"file"==q&&(j.value=null),"file-list"==q&&(j.value=[]);const M={image:[".jpg",".jpeg",".png",".webp",".gif",".svg"],video:[".mp4",".mov",".mpeg",".mpga"],audio:[".mp3",".m4a",".wav",".webm",".amr"],document:[".txt",".markdown",".md",".mdx",".pdf",".html",".htm",".xlsx",".xls",".docx",".csv",".eml",".msg",".pptx",".xml",".epub"]},U=()=>{let e="";return A.forEach(((a,t)=>{t<A.length-1?e+=M[a].join(",")+",":e+=M[a].join(",")})),e},L=C,H=(e,a,t)=>{},W=()=>{j.value=""},D=async e=>{const{file:a,onSuccess:t,onError:l}=e,o=new FormData;o.append("file",a),o.append("appId",T.params.uuid),o.append("user",$.value.userName);try{const e=await g(o);"file-list"==q?j.value.push({type:z.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id}):j.value={type:z.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id},t(e,a)}catch(i){l(i)}return!1};R&&R.length>0&&(j.value=R[0]);return S({updateMessage:()=>{R&&R.length>0?j.value=R[0]:"file"==q?(j.value=null,B.value=[]):"file-list"==q?(j.value=[],B.value=[]):j.value=""}}),l(j,(e=>{L("update:value",e)}),{immediate:!0,deep:!0}),(e,a)=>{const t=y,l=b,g=w,I=x,S=_,C=k;return o(),i("div",Ze,[n("div",Ye,s(r(E)),1),"paragraph"===r(q)||"text-input"===r(q)?(o(),u(t,{key:0,modelValue:j.value,"onUpdate:modelValue":a[0]||(a[0]=e=>j.value=e),type:"paragraph"===r(q)?"textarea":"text",rows:5,required:r(O),placeholder:`${r(E)}`,"show-word-limit":"",resize:"none",maxlength:r(V)},null,8,["modelValue","type","required","placeholder","maxlength"])):"number"===r(q)?(o(),u(t,{key:1,modelValue:j.value,"onUpdate:modelValue":a[1]||(a[1]=e=>j.value=e),modelModifiers:{number:!0},type:"number",required:r(O),placeholder:`${r(E)}`,resize:"none"},null,8,["modelValue","required","placeholder"])):"select"===r(q)?(o(),u(g,{key:2,modelValue:j.value,"onUpdate:modelValue":a[2]||(a[2]=e=>j.value=e),required:r(O),placeholder:`${r(E)}`},{default:d((()=>[(o(!0),i(c,null,v(r(R),(e=>(o(),u(l,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue","required","placeholder"])):"file"===r(q)||"file-list"===r(q)?(o(),u(C,{key:3,"file-list":B.value,"onUpdate:fileList":a[3]||(a[3]=e=>B.value=e),class:"upload-demo",multiple:"","show-file-list":"","on-preview":e.handlePreview,"on-remove":W,"before-remove":e.beforeRemove,limit:r(V),accept:U(),"auto-upload":!0,"on-Success":H,"http-request":D,"on-exceed":e.handleExceed},{default:d((()=>[p(S,{disabled:B.value.length==r(V)},{default:d((()=>[p(I,{class:"el-icon--upload",style:{"margin-right":"5px","font-size":"16px"}},{default:d((()=>[p(r(f))])),_:1}),m("从本地上传")])),_:1},8,["disabled"])])),_:1},8,["file-list","on-preview","before-remove","limit","accept","on-exceed"])):h("",!0)])}}},Ke=Array.isArray,Qe=e=>"string"==typeof e,ea=e=>null!==e&&"object"==typeof e,aa=/\B([A-Z])/g,ta=(e=>{const a=Object.create(null);return t=>a[t]||(a[t]=e(t))})((e=>e.replace(aa,"-$1").toLowerCase()));function la(e){if(Ke(e)){const a={};for(let t=0;t<e.length;t++){const l=e[t],o=Qe(l)?sa(l):la(l);if(o)for(const e in o)a[e]=o[e]}return a}if(Qe(e)||ea(e))return e}const oa=/;(?![^(]*\))/g,ia=/:([^]+)/,na=/\/\*[^]*?\*\//g;function sa(e){const a={};return e.replace(na,"").split(oa).forEach((e=>{if(e){const t=e.split(ia);t.length>1&&(a[t[0].trim()]=t[1].trim())}})),a}function ra(e){let a="";if(Qe(e))a=e;else if(Ke(e))for(let t=0;t<e.length;t++){const l=ra(e[t]);l&&(a+=l+" ")}else if(ea(e))for(const t in e)e[t]&&(a+=t+" ");return a.trim()}let ua=0;function da(){const e=I(),{name:a="unknown"}=(null==e?void 0:e.type)||{};return`${a}-${++ua}`}function ca(e,a){if(!de||!window.IntersectionObserver)return;const t=new IntersectionObserver((e=>{a(e[0].intersectionRatio>0)}),{root:document.body}),l=()=>{e.value&&t.unobserve(e.value)};C(l),$(l),ce((()=>{e.value&&t.observe(e.value)}))}const[va,pa]=ve("sticky");const fa=Se(T({name:va,props:{zIndex:pe,position:fe("top"),container:Object,offsetTop:me(0),offsetBottom:me(0)},emits:["scroll","change"],setup(a,{emit:t,slots:o}){const i=e(),n=he(i),s=B({fixed:!1,width:0,height:0,transform:0}),r=e(!1),u=z((()=>ge("top"===a.position?a.offsetTop:a.offsetBottom))),d=z((()=>{if(r.value)return;const{fixed:e,height:a,width:t}=s;return e?{width:`${t}px`,height:`${a}px`}:void 0})),c=z((()=>{if(!s.fixed||r.value)return;const e=ye(be(a.zIndex),{width:`${s.width}px`,height:`${s.height}px`,[a.position]:`${u.value}px`});return s.transform&&(e.transform=`translate3d(0, ${s.transform}px, 0)`),e})),v=()=>{if(!i.value||xe(i))return;const{container:e,position:l}=a,o=_e(i),n=re(window);if(s.width=o.width,s.height=o.height,"top"===l)if(e){const a=_e(e),t=a.bottom-u.value-s.height;s.fixed=u.value>o.top&&a.bottom>0,s.transform=t<0?t:0}else s.fixed=u.value>o.top;else{const{clientHeight:a}=document.documentElement;if(e){const t=_e(e),l=a-t.top-u.value-s.height;s.fixed=a-u.value<o.bottom&&a>t.top,s.transform=l<0?-l:0}else s.fixed=a-u.value<o.bottom}(e=>{t("scroll",{scrollTop:e,isFixed:s.fixed})})(n)};return l((()=>s.fixed),(e=>t("change",e))),we("scroll",v,{target:n,passive:!0}),ca(i,v),l([ke,Ie],(()=>{i.value&&!xe(i)&&s.fixed&&(r.value=!0,j((()=>{const e=_e(i);s.width=e.width,s.height=e.height,r.value=!1})))})),()=>{var e;return p("div",{ref:i,style:d.value},[p("div",{class:pa({fixed:s.fixed&&!r.value}),style:c.value},[null==(e=o.default)?void 0:e.call(o)])])}}})),[ma,ha]=ve("swipe"),ga={loop:Ce,width:pe,height:pe,vertical:Boolean,autoplay:me(0),duration:me(500),touchable:Ce,lazyRender:Boolean,initialSwipe:me(0),indicatorColor:String,showIndicators:Ce,stopPropagation:Ce},ya=Symbol(ma);const ba=Se(T({name:ma,props:ga,emits:["change","dragStart","dragEnd"],setup(a,{emit:t,slots:o}){const i=e(),n=e(),s=B({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let r=!1;const u=$e(),{children:d,linkChildren:c}=Te(ya),v=z((()=>d.length)),f=z((()=>s[a.vertical?"height":"width"])),m=z((()=>a.vertical?u.deltaY.value:u.deltaX.value)),h=z((()=>{if(s.rect){return(a.vertical?s.rect.height:s.rect.width)-f.value*v.value}return 0})),g=z((()=>f.value?Math.ceil(Math.abs(h.value)/f.value):v.value)),y=z((()=>v.value*f.value)),b=z((()=>(s.active+v.value)%v.value)),w=z((()=>{const e=a.vertical?"vertical":"horizontal";return u.direction.value===e})),x=z((()=>{const e={transitionDuration:`${s.swiping?0:a.duration}ms`,transform:`translate${a.vertical?"Y":"X"}(${+s.offset.toFixed(2)}px)`};if(f.value){const t=a.vertical?"height":"width",l=a.vertical?"width":"height";e[t]=`${y.value}px`,e[l]=a[l]?`${a[l]}px`:""}return e})),_=(e,t=0)=>{let l=e*f.value;a.loop||(l=Math.min(l,-h.value));let o=t-l;return a.loop||(o=Ae(o,h.value,0)),o},k=({pace:e=0,offset:l=0,emitChange:o})=>{if(v.value<=1)return;const{active:i}=s,n=(e=>{const{active:t}=s;return e?a.loop?Ae(t+e,-1,v.value):Ae(t+e,0,g.value):t})(e),r=_(n,l);if(a.loop){if(d[0]&&r!==h.value){const e=r<h.value;d[0].setOffset(e?y.value:0)}if(d[v.value-1]&&0!==r){const e=r>0;d[v.value-1].setOffset(e?-y.value:0)}}s.active=n,s.offset=r,o&&n!==i&&t("change",b.value)},I=()=>{s.swiping=!0,s.active<=-1?k({pace:v.value}):s.active>=v.value&&k({pace:-v.value})},S=()=>{I(),u.reset(),Ne((()=>{s.swiping=!1,k({pace:1,emitChange:!0})}))};let T;const A=()=>clearTimeout(T),E=()=>{A(),+a.autoplay>0&&v.value>1&&(T=setTimeout((()=>{S(),E()}),+a.autoplay))},O=(e=+a.initialSwipe)=>{if(!i.value)return;const t=()=>{var t,l;if(!xe(i)){const e={width:i.value.offsetWidth,height:i.value.offsetHeight};s.rect=e,s.width=+(null!=(t=a.width)?t:e.width),s.height=+(null!=(l=a.height)?l:e.height)}v.value&&-1===(e=Math.min(v.value-1,e))&&(e=v.value-1),s.active=e,s.swiping=!0,s.offset=_(e),d.forEach((e=>{e.setOffset(0)})),E()};xe(i)?j().then(t):t()},V=()=>O(s.active);let R;const M=e=>{!a.touchable||e.touches.length>1||(u.start(e),r=!1,R=Date.now(),A(),I())},U=()=>{if(!a.touchable||!s.swiping)return;const e=Date.now()-R,l=m.value/e;if((Math.abs(l)>.25||Math.abs(m.value)>f.value/2)&&w.value){const e=a.vertical?u.offsetY.value:u.offsetX.value;let t=0;t=a.loop?e>0?m.value>0?-1:1:0:-Math[m.value>0?"ceil":"floor"](m.value/f.value),k({pace:t,emitChange:!0})}else m.value&&k({pace:0});r=!1,s.swiping=!1,t("dragEnd",{index:b.value}),E()},L=(e,t)=>{const l=t===b.value,o=l?{backgroundColor:a.indicatorColor}:void 0;return p("i",{style:o,class:ha("indicator",{active:l})},null)};return Be({prev:()=>{I(),u.reset(),Ne((()=>{s.swiping=!1,k({pace:-1,emitChange:!0})}))},next:S,state:s,resize:V,swipeTo:(e,t={})=>{I(),u.reset(),Ne((()=>{let l;l=a.loop&&e===v.value?0===s.active?0:e:e%v.value,t.immediate?Ne((()=>{s.swiping=!1})):s.swiping=!1,k({pace:l-s.active,emitChange:!0})}))}}),c({size:f,props:a,count:v,activeIndicator:b}),l((()=>a.initialSwipe),(e=>O(+e))),l(v,(()=>O(s.active))),l((()=>a.autoplay),E),l([ke,Ie,()=>a.width,()=>a.height],V),l(ze(),(e=>{"visible"===e?E():A()})),N(O),q((()=>O(s.active))),je((()=>O(s.active))),C(A),$(A),we("touchmove",(e=>{if(a.touchable&&s.swiping&&(u.move(e),w.value)){!a.loop&&(0===s.active&&m.value>0||s.active===v.value-1&&m.value<0)||(qe(e,a.stopPropagation),k({offset:m.value}),r||(t("dragStart",{index:b.value}),r=!0))}}),{target:n}),()=>{var e;return p("div",{ref:i,class:ha()},[p("div",{ref:n,style:x.value,class:ha("track",{vertical:a.vertical}),onTouchstartPassive:M,onTouchend:U,onTouchcancel:U},[null==(e=o.default)?void 0:e.call(o)]),o.indicator?o.indicator({active:b.value,total:v.value}):a.showIndicators&&v.value>1?p("div",{class:ha("indicators",{vertical:a.vertical})},[Array(v.value).fill("").map(L)]):void 0])}}})),[wa,xa]=ve("tabs");var _a=T({name:wa,props:{count:Ee(Number),inited:Boolean,animated:Boolean,duration:Ee(pe),swipeable:Boolean,lazyRender:Boolean,currentIndex:Ee(Number)},emits:["change"],setup(a,{emit:t,slots:o}){const i=e(),n=e=>t("change",e),s=()=>{var e;const t=null==(e=o.default)?void 0:e.call(o);return a.animated||a.swipeable?p(ba,{ref:i,loop:!1,class:xa("track"),duration:1e3*+a.duration,touchable:a.swipeable,lazyRender:a.lazyRender,showIndicators:!1,onChange:n},{default:()=>[t]}):t},r=e=>{const t=i.value;t&&t.state.active!==e&&t.swipeTo(e,{immediate:!a.inited})};return l((()=>a.currentIndex),r),N((()=>{r(a.currentIndex)})),Be({swipeRef:i}),()=>p("div",{class:xa("content",{animated:a.animated||a.swipeable})},[s()])}});const[ka,Ia]=ve("tabs"),Sa={type:fe("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:me(0),duration:me(.3),animated:Boolean,ellipsis:Ce,swipeable:Boolean,scrollspy:Boolean,offsetTop:me(0),background:String,lazyRender:Ce,showHeader:Ce,lineWidth:pe,lineHeight:pe,beforeChange:Function,swipeThreshold:me(5),titleActiveColor:String,titleInactiveColor:String},Ca=Symbol(ka);var $a=T({name:ka,props:Sa,emits:["change","scroll","rendered","clickTab","update:active"],setup(a,{emit:t,slots:o}){let i,n,s,r,u;const d=e(),c=e(),v=e(),f=e(),m=da(),h=he(d),[g,y]=function(){const a=e([]),t=[];return S((()=>{a.value=[]})),[a,e=>(t[e]||(t[e]=t=>{a.value[e]=t}),t[e])]}(),{children:b,linkChildren:w}=Te(Ca),x=B({inited:!1,position:"",lineStyle:{},currentIndex:-1}),_=z((()=>b.length>+a.swipeThreshold||!a.ellipsis||a.shrink)),k=z((()=>({borderColor:a.color,background:a.background}))),I=(e,a)=>{var t;return null!=(t=e.name)?t:a},C=z((()=>{const e=b[x.currentIndex];if(e)return I(e,x.currentIndex)})),$=z((()=>ge(a.offsetTop))),T=z((()=>a.sticky?$.value+i:0)),N=e=>{const t=c.value,l=g.value;if(!(_.value&&t&&l&&l[x.currentIndex]))return;const o=l[x.currentIndex].$el,i=o.offsetLeft-(t.offsetWidth-o.offsetWidth)/2;r&&r(),r=function(e,a,t){let l,o=0;const i=e.scrollLeft,n=0===t?1:Math.round(1e3*t/16);let s=i;return function t(){s+=(a-i)/n,e.scrollLeft=s,++o<n&&(l=se(t))}(),function(){ne(l)}}(t,i,e?0:+a.duration)},A=()=>{const e=x.inited;j((()=>{const t=g.value;if(!t||!t[x.currentIndex]||"line"!==a.type||xe(d.value))return;const l=t[x.currentIndex].$el,{lineWidth:o,lineHeight:i}=a,n=l.offsetLeft+l.offsetWidth/2,s={width:Oe(o),backgroundColor:a.color,transform:`translateX(${n}px) translateX(-50%)`};if(e&&(s.transitionDuration=`${a.duration}s`),Ve(i)){const e=Oe(i);s.height=e,s.borderRadius=e}x.lineStyle=s}))},E=(e,l)=>{const o=(e=>{const a=e<x.currentIndex?-1:1;for(;e>=0&&e<b.length;){if(!b[e].disabled)return e;e+=a}})(e);if(!Ve(o))return;const i=b[o],n=I(i,o),r=null!==x.currentIndex;x.currentIndex!==o&&(x.currentIndex=o,l||N(),A()),n!==a.active&&(t("update:active",n),r&&t("change",n,i.title)),s&&!a.scrollspy&&Me(Math.ceil(Ue(d.value)-$.value))},O=(e,a)=>{const t=b.find(((a,t)=>I(a,t)===e)),l=t?b.indexOf(t):0;E(l,a)},V=(e=!1)=>{if(a.scrollspy){const t=b[x.currentIndex].$el;if(t&&h.value){const l=Ue(t,h.value)-T.value;n=!0,u&&u(),u=function(e,a,t,l){let o,i=re(e);const n=i<a,s=0===t?1:Math.round(1e3*t/16),r=(a-i)/s;return function t(){i+=r,(n&&i>a||!n&&i<a)&&(i=a),ue(e,i),n&&i<a||!n&&i>a?o=se(t):l&&(o=se(l))}(),function(){ne(o)}}(h.value,l,e?0:+a.duration,(()=>{n=!1}))}}},R=(e,l,o)=>{const{title:i,disabled:n}=b[l],s=I(b[l],l);n||(Le(a.beforeChange,{args:[s],done:()=>{E(l),V()}}),Pe(e)),t("clickTab",{name:s,title:i,event:o,disabled:n})},M=e=>{s=e.isFixed,t("scroll",e)},U=()=>{if("line"===a.type&&b.length)return p("div",{class:Ia("line"),style:x.lineStyle},null)},L=()=>{var e,t,l;const{type:i,border:n,sticky:s}=a,r=[p("div",{ref:s?void 0:v,class:[Ia("wrap"),{[Re]:"line"===i&&n}]},[p("div",{ref:c,role:"tablist",class:Ia("nav",[i,{shrink:a.shrink,complete:_.value}]),style:k.value,"aria-orientation":"horizontal"},[null==(e=o["nav-left"])?void 0:e.call(o),b.map((e=>e.renderTitle(R))),U(),null==(t=o["nav-right"])?void 0:t.call(o)])]),null==(l=o["nav-bottom"])?void 0:l.call(o)];return s?p("div",{ref:v},[r]):r},H=()=>{A(),j((()=>{var e,a;N(!0),null==(a=null==(e=f.value)?void 0:e.swipeRef.value)||a.resize()}))};l((()=>[a.color,a.duration,a.lineWidth,a.lineHeight]),A),l(ke,H),l((()=>a.active),(e=>{e!==C.value&&O(e)})),l((()=>b.length),(()=>{x.inited&&(O(a.active),A(),j((()=>{N(!0)})))}));return Be({resize:H,scrollTo:e=>{j((()=>{O(e),V(!0)}))}}),q(A),je(A),ce((()=>{O(a.active,!0),j((()=>{x.inited=!0,v.value&&(i=_e(v.value).height),N(!0)}))})),ca(d,A),we("scroll",(()=>{if(a.scrollspy&&!n){const e=(()=>{for(let e=0;e<b.length;e++){const{top:a}=_e(b[e].$el);if(a>T.value)return 0===e?0:e-1}return b.length-1})();E(e)}}),{target:h,passive:!0}),w({id:m,props:a,setLine:A,scrollable:_,onRendered:(e,a)=>t("rendered",e,a),currentName:C,setTitleRefs:y,scrollIntoView:N}),()=>p("div",{ref:d,class:Ia([a.type])},[a.showHeader?a.sticky?p(fa,{container:d.value,offsetTop:$.value,onScroll:M},{default:()=>[L()]}):L():null,p(_a,{ref:f,count:b.length,inited:x.inited,animated:a.animated,duration:a.duration,swipeable:a.swipeable,lazyRender:a.lazyRender,currentIndex:x.currentIndex,onChange:E},{default:()=>{var e;return[null==(e=o.default)?void 0:e.call(o)]}})])}});const Ta=Symbol(),[Ba,za]=ve("tab"),ja=T({name:Ba,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:pe,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:Ce},setup(e,{slots:a}){const t=z((()=>{const a={},{type:t,color:l,disabled:o,isActive:i,activeColor:n,inactiveColor:s}=e;l&&"card"===t&&(a.borderColor=l,o||(i?a.backgroundColor=l:a.color=l));const r=i?n:s;return r&&(a.color=r),a})),l=()=>{const t=p("span",{class:za("text",{ellipsis:!e.scrollable})},[a.title?a.title():e.title]);return e.dot||Ve(e.badge)&&""!==e.badge?p(He,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[t]}):t};return()=>p("div",{id:e.id,role:"tab",class:[za([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:t.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[l()])}}),[Na,qa]=ve("swipe-item");const Aa=Se(T({name:Na,setup(e,{slots:a}){let t;const l=B({offset:0,inited:!1,mounted:!1}),{parent:o,index:i}=We(ya);if(!o)return;const n=z((()=>{const e={},{vertical:a}=o.props;return o.size.value&&(e[a?"height":"width"]=`${o.size.value}px`),l.offset&&(e.transform=`translate${a?"Y":"X"}(${l.offset}px)`),e})),s=z((()=>{const{loop:e,lazyRender:a}=o.props;if(!a||t)return!0;if(!l.mounted)return!1;const n=o.activeIndicator.value,s=o.count.value-1,r=0===n&&e?s:n-1,u=n===s&&e?0:n+1;return t=i.value===n||i.value===r||i.value===u,t}));return N((()=>{j((()=>{l.mounted=!0}))})),Be({setOffset:e=>{l.offset=e}}),()=>{var e;return p("div",{class:qa(),style:n.value},[s.value?null==(e=a.default)?void 0:e.call(a):null])}}})),[Ea,Oa]=ve("tab");const Va=Se(T({name:Ea,props:ye({},Fe,{dot:Boolean,name:pe,badge:pe,title:String,disabled:Boolean,titleClass:De,titleStyle:[String,Object],showZeroBadge:Ce}),setup(a,{slots:t}){const o=da(),i=e(!1),n=I(),{parent:s,index:r}=We(Ca);if(!s)return;const u=()=>{var e;return null!=(e=a.name)?e:r.value},d=z((()=>{const e=u()===s.currentName.value;return e&&!i.value&&(i.value=!0,s.props.lazyRender&&j((()=>{s.onRendered(u(),a.title)}))),e})),c=e(""),v=e("");A((()=>{const{titleClass:e,titleStyle:t}=a;c.value=e?ra(e):"",v.value=t&&"string"!=typeof t?function(e){let a="";if(!e||Qe(e))return a;for(const t in e){const l=e[t];(Qe(l)||"number"==typeof l)&&(a+=`${t.startsWith("--")?t:ta(t)}:${l};`)}return a}(la(t)):t}));const f=e(!d.value);return l(d,(e=>{e?f.value=!1:Ne((()=>{f.value=!0}))})),l((()=>a.title),(()=>{s.setLine(),s.scrollIntoView()})),E(Ta,d),Be({id:o,renderTitle:e=>p(ja,R({key:o,id:`${s.id}-${r.value}`,ref:s.setTitleRefs(r.value),style:v.value,class:c.value,isActive:d.value,controls:o,scrollable:s.scrollable.value,activeColor:s.props.titleActiveColor,inactiveColor:s.props.titleInactiveColor,onClick:a=>e(n.proxy,r.value,a)},Je(s.props,["type","color","shrink"]),Je(a,["dot","badge","title","disabled","showZeroBadge"])),{title:t.title})}),()=>{var e;const a=`${s.id}-${r.value}`,{animated:l,swipeable:n,scrollspy:u,lazyRender:c}=s.props;if(!t.default&&!l)return;const v=u||d.value;if(l||n)return p(Aa,{id:o,role:"tabpanel",class:Oa("panel-wrapper",{inactive:f.value}),tabindex:d.value?0:-1,"aria-hidden":!d.value,"aria-labelledby":a,"data-allow-mismatch":"attribute"},{default:()=>{var e;return[p("div",{class:Oa("panel")},[null==(e=t.default)?void 0:e.call(t)])]}});const m=i.value||u||!c?null==(e=t.default)?void 0:e.call(t):null;return O(p("div",{id:o,role:"tabpanel",class:Oa("panel"),tabindex:v?0:-1,"aria-labelledby":a,"data-allow-mismatch":"attribute"},[m]),[[V,v]])}}})),Ra=Se($a),Ma={class:"bg-[#f3f8fa] p-2 overflow-auto",style:{height:"calc(var(--vh) * 100 - 160px)"}},Ua={class:"pc_container",style:{display:"flex"}},La={class:"bg-[#fff] m_bg",style:{"border-radius":"10px",width:"40%"}},Ha={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Wa={class:"pc_right bg-[#fff]"},Da={id:"typing-area"},Ja={key:0,class:"decContaniner nop bg-[#fff]"},Pa={key:0,class:"img_box"},Fa=["src"],Xa={key:1,class:"icon"},Za={class:"process_text label_width"},Ya={key:0,class:"process"},Ga={key:0,class:"img_box"},Ka=["src"],Qa={key:1,class:"icon"},et={class:"process"},at={class:"process_text"},tt=["src"],lt=["src"],ot={class:"mobile_container"},it={class:"p-3",style:{display:"flex","justify-content":"space-between"}},nt={class:"mobile_right"},st={id:"typing-area"},rt={key:0,class:"decContaniner nop bg-[#fff]"},ut={key:0,class:"img_box"},dt=["src"],ct={key:1,class:"icon"},vt={class:"process_text label_width"},pt=(e=>(le("data-v-dcba231f"),e=e(),oe(),e))((()=>n("div",null,[n("div",{class:"process"})],-1))),ft={key:0,class:"img_box"},mt=["src"],ht={key:1,class:"icon"},gt={class:"process"},yt={class:"process_text"},bt=M({__name:"index",setup(f){const g=ie("loading.png"),y=ie("copy.png"),b=B({}),w=t(),k={},I=e([]),S=e(a.get("userInfo")?JSON.parse(a.get("userInfo")):{}),C=e(null),{locale:$}=U(),T=L(),j=e(!1);let q=e("a"),A=e("");const E=e(""),O=e(null),V=e(null),R=e(["1","2"]),M=e(!1),le=e(!1);let oe;const ne=async()=>{var e;await Y({appId:w.params.uuid,user:S.value.userName,mode:null==(e=C.value)?void 0:e.mode,task_id:E.value}),setTimeout((()=>{$e.abort(),Te=!0,ge.value=[],M.value=!1,ye.value.length&&ye.value.forEach((e=>{e.status=!0})),Ae()}),0)};N((async()=>{var e,t;const l=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${l}px`);if(["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=w.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(t=w.params)?void 0:t.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),$.value=H(),a.get("userInfo"))Ue(),me();else{localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken");const e=W();e&&"zh-CN"!=e?T.push("/login"):window.addLoginDom()}})),l(A,(()=>{A.value&&(q.value="b")}));const se=()=>{w.params.uuid&&G({appId:w.params.uuid,user:S.value.userName}).then((e=>{(null==e?void 0:e.user_input_form)&&(I.value=e.user_input_form,e.user_input_form.forEach((e=>{const a=Object.keys(e)[0],t=e[a].variable;k[t]={label:e[a].label},b[t]=""})))}))},re=z((()=>!!I.value.length)),ue=z((()=>I.value.filter((e=>!0===e[Object.keys(e)[0]].required)).map((e=>e[Object.keys(e)[0]].variable)))),de=()=>{w.params.uuid&&K({appId:w.params.uuid,user:S.value.userName}).then((e=>{C.value={...e}}))},ce=e(!1),ve=e(!1),pe=e(!1),fe=e(!1),me=async()=>{var e,a;let t=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=S.value)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:w.params.uuid,userUuid:null==(a=S.value)?void 0:a.openid}];await D.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",t)},he=()=>{var e,a;if(0!=ue.value.length||(t=b,Object.values(t).some((e=>e)))){var t;for(let e in b)if(ue.value.includes(e)&&!b[e])return void Q({message:`${k[e].label}为必填项！`,type:"error"});(null==(e=C.value)?void 0:e.mode)&&(["advanced-chat","chat"].includes(null==(a=C.value)?void 0:a.mode)?Q({type:"success",message:"计划中，敬请期待..."}):"completion"==C.value.mode?qe():Ne())}else Q({message:"请输入您的问题。",type:"error"})},ge=e([]);var ye=e([]),be=e([]);const we=e(""),xe=e(0),_e=e(""),ke=e(!1);let Ie=e(0);const Se=e(!1),Ce=e(!1);let $e,Te=!1,Be=!1;l(ye,(()=>{ze()}),{deep:!0});const ze=()=>{Ie.value<ye.value.length&&(be.value.push(ye.value[Ie.value]),Ie.value++,setTimeout(ze,1e3))},je=()=>{xe.value<we.value.length?(ke.value=!0,_e.value+=we.value.charAt(xe.value),xe.value++,setTimeout(je,20)):(Ce.value=!1,ke.value=!1,pe.value=!0,Ae())},Ne=async()=>{le.value=!0,M.value=!0,A.value="",ye.value=[],be.value=[],Ie.value=0,_e.value="",we.value="",ge.value=[],Se.value=!1,Te=!1,xe.value=0,$e=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/workflows/run?locale=zh`;0,ce.value=!0,ve.value=!0,fe.value=!1,await Xe(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:w.params.uuid,user:S.value.userName,inputs:{...b,outputLanguage:b.outputLanguage?b.outputLanguage:"中文"==ee()?"简体中文":ee()},files:[],response_mode:"streaming",appUuid:w.params.appUuid}),onmessage(e){var a,t,l,o,i,n;if(e.data.trim())try{const s=JSON.parse(e.data);if(E.value=s.task_id,s.error)throw new Error(s.error);"智能体推理思维链"==(null==(a=null==s?void 0:s.data)?void 0:a.title)&&"node_finished"===s.event&&(Ce.value=!0,we.value=JSON.parse(s.data.outputs.text).text,je()),"node_started"!==s.event||Se.value||"开始"==(null==(t=null==s?void 0:s.data)?void 0:t.title)||ye.value.push({node_id:null==(l=null==s?void 0:s.data)?void 0:l.node_id,title:null==(o=null==s?void 0:s.data)?void 0:o.title,status:!1}),"node_finished"===s.event&&ye.value.forEach((e=>{var a;e.node_id==(null==(a=null==s?void 0:s.data)?void 0:a.node_id)&&(e.status=!0)})),"text_chunk"===s.event&&(j.value=!0,Se.value=!0,ge.value.push(null==(i=null==s?void 0:s.data)?void 0:i.text),Ce.value||Ae()),"workflow_started"===s.event&&(ce.value=!1),"workflow_finished"===s.event&&(Se.value=!0,M.value=!1,j.value||(ge.value.push(null==(n=null==s?void 0:s.data)?void 0:n.outputs.text),Ce.value||Ae()),Te=!0,q.value="b")}catch(s){Ve(s)}},onerror(e){Ve(e)},signal:$e.signal,openWhenHidden:!0})}catch(e){Ve()}},qe=async()=>{A.value="",ge.value=[],Ce.value=!1,Te=!1,$e=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/completion-messages?locale=zh`;0,ce.value=!0,ve.value=!0,fe.value=!1,await Xe(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:w.params.uuid,user:S.value.userName,inputs:{...b,outputLanguage:ee()},files:[],response_mode:"streaming",appUuid:w.params.appUuid}),onmessage(e){if(ce.value=!1,pe.value=!0,e.data.trim())try{const a=JSON.parse(e.data);if(E.value=a.task_id,a.error)throw new Error(a.error);"message"===a.event&&(ge.value.push(null==a?void 0:a.answer),Ce.value||Ae()),"message_end"===a.event&&(q.value="b",Te=!0)}catch(a){Ve(a)}},onerror(e){Ve(e)},signal:$e.signal,openWhenHidden:!0})}catch(e){Ve()}},Ae=()=>{if(0===ge.value.length)return Ce.value=!1,Be=!0,void Ee();Ce.value=!0;const e=ge.value.shift();Oe(e).then((()=>{Ae()}))},Ee=()=>{Be&&Te&&(ve.value=!1,pe.value=!1,fe.value=!0)},Oe=e=>new Promise((a=>{let t=0;oe=setInterval((()=>{if(t<e.length){A.value+=e[t++];const a=document.getElementsByClassName("pc_right");a[0].scrollTop=a[0].scrollHeight;const l=document.getElementsByClassName("mobile_right");l[0].scrollTop=l[0].scrollHeight}else clearInterval(oe),a()}),15)})),Ve=()=>{setTimeout((()=>{$e.abort()}),0),ce.value=!1,pe.value=!1,ve.value=!1,Ce.value=!1,Q.error("访问太火爆了！休息下，请稍后再试！"),A.value="访问太火爆了！休息下，请稍后再试！"},Re=async()=>{try{await navigator.clipboard.writeText(A.value),Q({type:"success",message:"复制成功"})}catch(e){Q(e)}},Me=()=>{for(let e in b)b[e]="";O.value.forEach((e=>{e.updateMessage()})),V.value.forEach((e=>{e.updateMessage()}))},Ue=()=>{if(localStorage.getItem("yudaoToken"))return se(),void de();const e=a.get("userInfo");if(e){const a=JSON.parse(e);try{J({userId:a.userId,userName:a.userName,realName:a.realName,avatar:a.avatar,plaintextUserId:a.plaintextUserId,mobile:a.mobile,email:a.email}).then((e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),se(),de())}))}catch(t){}}};return(e,a)=>{const t=_,l=x,f=ae,w=te,k=P("v-md-preview");return o(),i("div",Ma,[n("div",Ua,[r(re)?(o(),i(c,{key:0},[n("div",La,[(o(!0),i(c,null,v(r(I),((a,t)=>(o(),i("div",{class:"flex",key:t},[(o(!0),i(c,null,v(a,((a,t)=>(o(),u(Ge,{key:a.variable,type:t,label:e.$t(null==a?void 0:a.label),value:r(b)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>r(b)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRef",ref:O},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",Ha,[p(t,{onClick:Me},{default:d((()=>[m("Clear")])),_:1}),p(t,{onClick:he,loading:r(ve),type:"primary"},{default:d((()=>[m("Execute")])),_:1},8,["loading"])])]),n("div",Wa,[n("div",Da,[r(be).length>0||r(_e)||r(le)?(o(),i("div",Ja,[p(w,{modelValue:r(R),"onUpdate:modelValue":a[0]||(a[0]=e=>Z(R)?R.value=e:null)},{default:d((()=>[p(f,{name:"1"},{title:d((()=>[r(M)?(o(),i("div",Pa,[n("img",{src:r(g),alt:"loading"},null,8,Fa)])):(o(),i("div",Xa,[p(l,null,{default:d((()=>[p(r(F))])),_:1})])),m(s(e.$t("tool.execution_progress")),1)])),default:d((()=>[(o(!0),i(c,null,v(r(be),((a,t)=>(o(),i("div",{key:t,class:"process"},[n("div",Za,s(a.title),1),m("    "),n("span",{style:{color:"#36B15E"},class:X(a.status?"":"loading-text")},s(a.status?e.$t("tool.completed"):e.$t("tool.loading")),3)])))),128))])),_:1}),n("div",null,[r(_e)?(o(),i("div",Ya)):h("",!0)]),r(_e)?(o(),u(f,{key:0,name:"2"},{title:d((()=>[r(ke)?(o(),i("div",Ga,[n("img",{src:r(g),alt:"loading"},null,8,Ka)])):(o(),i("div",Qa,[p(l,null,{default:d((()=>[p(r(F))])),_:1})])),m(s(e.$t("tool.reasoning_process")),1)])),default:d((()=>[n("div",et,[n("div",at,s(r(_e)),1)])])),_:1})):h("",!0)])),_:1},8,["modelValue"])])):h("",!0),r(A)?(o(),u(k,{key:1,text:r(A),id:"previewMd"},null,8,["text"])):h("",!0),n("div",null,[r(pe)?(o(),i("img",{key:0,src:r(g),alt:"loading",class:"spinner"},null,8,tt)):h("",!0),r(pe)?(o(),i("span",{key:1,text:"",type:"primary",class:"stop_btn",onClick:ne},s(e.$t("tool.stopGeneration")),1)):h("",!0),r(fe)?(o(),i("img",{key:2,onClick:Re,src:r(y),alt:"",style:{width:"20px"},class:"copy"},null,8,lt)):h("",!0)])])])],64)):h("",!0)]),n("div",ot,[p(r(Ra),{active:r(q),shrink:"","line-width":"20"},{default:d((()=>[p(r(Va),{title:"输入",name:"a"},{default:d((()=>[(o(!0),i(c,null,v(r(I),((a,t)=>(o(),i("div",{class:"flex",key:t},[(o(!0),i(c,null,v(a,((a,t)=>(o(),u(Ge,{key:a.variable,type:t,label:e.$t(null==a?void 0:a.label),value:r(b)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>r(b)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRefs",ref:V},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",it,[p(t,{onClick:Me},{default:d((()=>[m("Clear")])),_:1}),p(t,{onClick:a[1]||(a[1]=e=>he()),loading:r(ve),type:"primary"},{default:d((()=>[m("Execute")])),_:1},8,["loading"])])])),_:1}),p(r(Va),{title:"结果",name:"b"},{default:d((()=>[n("div",nt,[n("div",st,[r(ye).length>0||r(_e)?(o(),i("div",rt,[p(w,{modelValue:r(R),"onUpdate:modelValue":a[2]||(a[2]=e=>Z(R)?R.value=e:null)},{default:d((()=>[p(f,{name:"1"},{title:d((()=>[r(M)?(o(),i("div",ut,[n("img",{src:r(g),alt:"loading"},null,8,dt)])):(o(),i("div",ct,[p(l,null,{default:d((()=>[p(r(F))])),_:1})])),m(s(e.$t("tool.execution_progress")),1)])),default:d((()=>[(o(!0),i(c,null,v(r(ye),((e,a)=>(o(),i("div",{key:a,class:"process"},[n("div",vt,s(e.title),1),m("    "),n("span",{style:{color:"#36B15E"},class:X(e.status?"":"loading-text")},s(e.status?"已完成":"加载中"),3)])))),128))])),_:1}),pt,r(_e)?(o(),u(f,{key:0,title:"推导过程",name:"2"},{title:d((()=>[r(ke)?(o(),i("div",ft,[n("img",{src:r(g),alt:"loading"},null,8,mt)])):(o(),i("div",ht,[p(l,null,{default:d((()=>[p(r(F))])),_:1})])),m(s(e.$t("tool.reasoning_process")),1)])),default:d((()=>[n("div",gt,[n("div",yt,s(r(_e)),1)])])),_:1})):h("",!0)])),_:1},8,["modelValue"])])):h("",!0),r(A)?(o(),u(k,{key:1,text:r(A),id:"previewMd"},null,8,["text"])):h("",!0)])])])),_:1})])),_:1},8,["active"])])])}}},[["__scopeId","data-v-dcba231f"]]);export{bt as default};
