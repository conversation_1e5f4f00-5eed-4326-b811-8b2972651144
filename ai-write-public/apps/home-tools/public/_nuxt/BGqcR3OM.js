import{D as i,ab as p,r as s,o as u,a0 as f,aL as _,aJ as d,t as m,v as I,x as g}from"./B7yib2Q0.js";import{s as v}from"./x_rD_Ya3.js";import{_ as y}from"./DlAUqK2U.js";const b={__name:"[payInfo]",setup(w){const{t:n}=i(),r=p(),o=s(),t=s("");u(async()=>{t.value=JSON.parse(decodeURIComponent(r.params.payInfo)),await l();const a=navigator.userAgent;if(a!=null){if(a.includes("MicroMessenger"))f.warning(n("tool.pleasescanwithalipay"));else if(a.includes("AlipayClient")){const e=await _(t.value);location.replace(e)}}});const c=()=>{location.replace(location.origin)},l=()=>{o.value=v(async()=>{var e;(await d((e=t.value)==null?void 0:e.piId)).payStatus==="PAID"&&(location.replace(location.origin),clearInterval(o.value))},2e3)};return(a,e)=>(I(),m("div",null,[g("button",{onClick:c},"返回首页")]))}},M=y(b,[["__scopeId","data-v-8b8a31de"]]);export{M as default};
