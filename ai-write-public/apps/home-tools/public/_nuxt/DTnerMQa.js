import{r as b,E as m,ab as O,C as P,D as K,J as Q,g,t as A,v as c,x as G,U as v,K as H,y as W,L as t,A as w,M as X,N as Y,z as V,B as Z,aR as ee,Q as te,aS as oe}from"./B7yib2Q0.js";const ae={class:"p-3 flex-1 rounded-md"},le={class:"text-[14px] font-bold mb-2 text-gray-600"},ue={__name:"InputField",props:{type:{type:String,required:!0},label:{type:String,required:!0},required:{type:Boolean,required:!0},placeholder:{type:String,required:!0},max_length:{type:Number,required:!1},options:{type:Array,required:!1},fileVerify:{type:Array,required:!1,default:()=>[""]},currentItem:{type:Object,required:!1}},emits:["update:value"],setup(B,{expose:U,emit:j}){var L;const R=b(m.get("userInfo")?JSON.parse(m.get("userInfo")):{});O(),P();const{locale:S}=K(),p=b([]),E=b({jpg:"image",jpeg:"image",png:"image",webp:"image",gif:"image",svg:"image",mp4:"video",mov:"video",mpeg:"video",mpga:"video",mp3:"audio",m4a:"audio",wav:"audio",webm:"audio",amr:"audio",txt:"document",markdown:"document",md:"document",mdx:"document",pdf:"document",html:"document",htm:"document",xlsx:"document",xls:"document",docx:"document",csv:"document",eml:"document",msg:"document",pptx:"document",xml:"document",epub:"document"}),u=B,a=b(((L=u.options)==null?void 0:L[0])||""),l=u.type,T=u.fileVerify,y=u.label,q=u.required,k=u.max_length,h=u.options;l=="file"&&(a.value=null),l=="file-list"&&(a.value=[]);const I={image:[".jpg",".jpeg",".png",".webp",".gif",".svg"],video:[".mp4",".mov",".mpeg",".mpga"],audio:[".mp3",".m4a",".wav",".webm",".amr"],document:[".txt",".markdown",".md",".mdx",".pdf",".html",".htm",".xlsx",".xls",".docx",".csv",".eml",".msg",".pptx",".xml",".epub"]},C=()=>{let o="";return T.forEach((e,n)=>{n<T.length-1?o+=I[e].join(",")+",":o+=I[e].join(",")}),o},N=j,x=async()=>{var o,e,n,f;if(!m.get("userInfo")){m.remove("yudaoToken",{domain:"ai.medsci.cn"}),m.remove("yudaoToken",{domain:"ai.medon.com.cn"}),m.remove("yudaoToken",{domain:".medsci.cn"}),m.remove("yudaoToken",{domain:".medon.com.cn"}),m.remove("yudaoToken",{domain:"localhost"}),localStorage.removeItem("hasuraToken");const s=te(S.value);return!s||s=="zh"?window.addLoginDom():location.href=location.origin+"/"+S.value+"/login",Array.from(document.getElementsByTagName("input")).forEach(i=>i.blur()),Array.from(document.getElementsByTagName("textarea")).forEach(i=>i.blur()),!1}return!((e=(o=u.currentItem)==null?void 0:o.appUser)!=null&&e.status)||((f=(n=u.currentItem)==null?void 0:n.appUser)==null?void 0:f.status)==2?(N("payShowStatus",!0),Array.from(document.getElementsByTagName("input")).forEach(s=>s.blur()),Array.from(document.getElementsByTagName("textarea")).forEach(s=>s.blur()),!1):!0},D=(o,e,n)=>{},F=()=>{a.value=""},z=o=>{},M=(o,e)=>{},$=(o,e)=>{},J=async o=>{var _;if(!await x())return!1;const{file:n,onSuccess:f,onError:s}=o,i=new FormData;i.append("file",n),i.append("appId",(_=u.currentItem)==null?void 0:_.dAppUuid),i.append("user",R.value.userName);try{const d=await oe(i);l=="file-list"?a.value.push({type:E.value[d.extension],transfer_method:"local_file",url:"",upload_file_id:d.id}):a.value={type:E.value[d.extension],transfer_method:"local_file",url:"",upload_file_id:d.id},f(d,n)}catch(d){s(d)}return!1};return U({updateMessage:()=>{h&&h.length>0?a.value=h[0]:l=="file"?(a.value=null,p.value=[]):l=="file-list"?(a.value=[],p.value=[]):a.value=""}}),Q(a,o=>{N("update:value",o)},{immediate:!0,deep:!0}),(o,e)=>{const n=g("el-input"),f=g("el-option"),s=g("el-select"),i=g("el-icon"),_=g("el-button"),d=g("el-upload");return c(),A("div",ae,[G("h6",le,W(t(y)),1),t(l)==="paragraph"||t(l)==="text-input"?(c(),v(n,{key:0,onFocus:x,modelValue:a.value,"onUpdate:modelValue":e[0]||(e[0]=r=>a.value=r),type:t(l)==="paragraph"?"textarea":"text",rows:5,required:t(q),placeholder:`${t(y)}`,"show-word-limit":"",resize:"none",maxlength:t(k)},null,8,["modelValue","type","required","placeholder","maxlength"])):t(l)==="number"?(c(),v(n,{key:1,modelValue:a.value,"onUpdate:modelValue":e[1]||(e[1]=r=>a.value=r),modelModifiers:{number:!0},onFocus:x,type:"number",required:t(q),placeholder:`${t(y)}`,resize:"none"},null,8,["modelValue","required","placeholder"])):t(l)==="select"?(c(),v(s,{key:2,onChange:x,modelValue:a.value,"onUpdate:modelValue":e[2]||(e[2]=r=>a.value=r),required:t(q),placeholder:`${t(y)}`},{default:w(()=>[(c(!0),A(X,null,Y(t(h),r=>(c(),v(f,{key:r,label:r,value:r},null,8,["label","value"]))),128))]),_:1},8,["modelValue","required","placeholder"])):t(l)==="file"||t(l)==="file-list"?(c(),v(d,{key:3,"file-list":p.value,"onUpdate:fileList":e[3]||(e[3]=r=>p.value=r),class:"upload-demo",multiple:"","show-file-list":"","on-preview":z,"on-remove":F,"before-remove":M,limit:t(k),accept:C(),"auto-upload":!0,"on-Success":D,"http-request":J,"on-exceed":$},{default:w(()=>[V(_,{disabled:t(l)==="file"?p.value.length==1:p.value.length==t(k)},{default:w(()=>[V(i,{class:"el-icon--upload",style:{"margin-right":"5px","font-size":"16px"}},{default:w(()=>[V(t(ee))]),_:1}),e[4]||(e[4]=Z("从本地上传"))]),_:1},8,["disabled"])]),_:1},8,["file-list","limit","accept"])):H("",!0)])}}};export{ue as default};
