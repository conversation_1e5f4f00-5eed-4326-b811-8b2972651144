import{_ as Je}from"./Clki7XNQ.js";import{C as ss,D as Pe,r as g,j as Oe,E as w,F as Re,G as qe,H as de,I as ts,o as Ce,e as Ge,J as as,t as f,v as r,x as e,K as C,L as E,y as c,z as y,A,M as we,N as be,O as Ke,P as ee,Q as Ne,R as ns,u as ls,S as he,T as Ee,g as W,U as J,V as Te,W as Q,B as ve,X as _e,Y as Fe,Z as De,_ as os,$ as is,a0 as Se,a1 as Be,a2 as cs,a3 as Ve,a4 as rs}from"./B7yib2Q0.js";import{u as Ze,a as ds,b as ps}from"./D8PktKVn.js";import{s as Ye}from"./x_rD_Ya3.js";import{_ as ke}from"./DlAUqK2U.js";import{c as us}from"./PbqxfYsz.js";import{g as je,p as ms,a as gs,P as fs,s as ze}from"./BTDtKNTY.js";import{h as X}from"./DMAjf-Z3.js";import{u as hs}from"./BvTBxqY-.js";import"./DPQy3_RL.js";const vs={class:"header ms-header-media"},_s={class:"ms-header"},ws={class:"wrapper"},bs={class:"main-menu-placeholder wrapper clearfix",style:{height:"56px",display:"block !important"}},ks={class:"ms-header-img"},ys=["href"],xs={id:"main-menu",class:"ms-header-nav"},$s={class:"header-top header-user",id:"user-info-header"},Is={key:0},As={class:"m_font change_lang m_none h-full"},Ts={class:"m_font change_lang pc_none"},Ss={key:1},Cs=["title"],Ns={class:"mr-2 px-md-4 cursor-pointer m_font"},Ms=["title"],Us={key:0,class:"change_lang"},Os={class:"current_lang"},Es={class:"ms-link"},Fs={class:"new-header-avator-pop",id:"new-header-avator"},Ds={class:"new-header-bottom",style:{padding:"0"}},Bs={key:0,class:"langUl"},Vs=["onClick"],js={key:2,class:"index-user-img_right"},zs={href:"#"},Ls={class:"img-area"},Hs=["src"],Js={class:"new-header-avator-pop",id:"new-header-avator"},Ps={class:"new-header-top"},Rs={class:"new-header-info"},qs=["src"],Gs={class:"new-header-name"},Ks={__name:"index",props:{subStatusDetail:{type:Object,default:()=>({})}},emits:["getAppLang","getAppLang","subScript"],async setup(x,{emit:d}){let I,h;const o=d;ss();const{setLocale:$,locale:T}=Pe(),V=g(x.subStatusDetail),N=g(""),O=g(null),j=g(!1),M=g(""),L=g([]),S=g(""),H=g(null),R=g(),F=g(),z=g();Oe(()=>"https://www.medsci.cn/");const v=Oe(()=>w.get("userInfo")),ne=l=>{N.value="https://img.medsci.cn/web/img/user_icon.png"},le=()=>R.value.style.display="block",ye=()=>R.value.style.display="none",xe=()=>F.value.style.display="block",$e=()=>F.value.style.display="none",pe=()=>{o("subScript")},oe=()=>/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry)/i.test(navigator.userAgent),Ie=l=>{const m=de("ai_apps_lang",{domain:".medon.com.cn",maxAge:31104e3});m.value=l,M.value=l,$(l),window.sessionStorage.setItem("redirectUrl",l!="zh"?location.origin+"/"+l:location.origin),R.value.style.display="none",z.value=oe()},se=async()=>{clearInterval(H.value),w.remove("userInfo",{domain:".medon.com.cn"}),w.remove("userInfo",{domain:".medsci.cn"}),w.remove("userInfo",{domain:"ai.medon.com.cn"}),w.remove("userInfo",{domain:"ai.medsci.cn"}),w.remove("userInfo",{domain:"localhost"}),localStorage.removeItem("conversation"),Object.keys(localStorage).forEach(m=>{m.includes("writeContent")&&localStorage.removeItem(m)});const l=localStorage.getItem("socialType");if(l&&(l==35||l==36))try{await ns(),ue(),location.reload()}catch(m){console.error("Logout failed:",m)}else{ue();const m=window.location.href,p=window.location.origin.includes("medsci.cn")?`https://www.medsci.cn/sso_logout?redirectUrl=${m}`:`https://portal-test.medon.com.cn/sso_logout?redirectUrl=${m}`;window.location.href=p}},ue=()=>{localStorage.removeItem("hasuraToken"),w.remove("yudaoToken",{domain:"ai.medsci.cn"}),w.remove("yudaoToken",{domain:"ai.medon.com.cn"}),w.remove("yudaoToken",{domain:".medsci.cn"}),w.remove("yudaoToken",{domain:".medon.com.cn"}),w.remove("yudaoToken",{domain:"localhost"}),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid")},me=()=>{var m;const l=Ne(T.value);!l||l==="zh"?(m=window.addLoginDom)==null||m.call(window):location.href=location.origin+"/"+T.value+"/login"},q=()=>{var m;const l=w.get("userInfo");l!==JSON.stringify(O.value)&&(O.value=l?JSON.parse(l):null,N.value=((m=O.value)==null?void 0:m.avatar)||"https://img.medsci.cn/web/img/user_icon.png")},{data:i}=([I,h]=Re(async()=>Ze("languages",async()=>{var Z;const l=qe(),m=de("userInfo"),p=de("ai_apps_lang"),te=ps(),ae=ds();O.value=m.value||null,N.value=((Z=O.value)==null?void 0:Z.avatar)||"https://img.medsci.cn/web/img/user_icon.png";const G=await ts(te);return L.value=G.filter(B=>!B.status).map(B=>({name:B.value,value:B.remark})),M.value=p.value,j.value=l.path.includes("/tool"),z.value=/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry)/i.test(ae["user-agent"]),[z.value,M.value,j.value,L.value,N.value,O.value]})),I=await I,h(),I);return z.value=oe(),M.value=(i==null?void 0:i.value)&&(i==null?void 0:i.value[1]),j.value=(i==null?void 0:i.value)&&(i==null?void 0:i.value[2]),L.value=(i==null?void 0:i.value)&&(i==null?void 0:i.value[3]),O.value=(i==null?void 0:i.value)&&(i==null?void 0:i.value[5]),N.value=(i==null?void 0:i.value)&&(i==null?void 0:i.value[4]),Ce(()=>{S.value="https://ai.medon.com.cn/"+T.value,H.value=Ye(q,1e3)}),Ge(()=>{clearInterval(H.value)}),as(v,l=>{q()}),(l,m)=>{var te,ae,G,Z,B,ge,s,n,u,t,U;const p=Je;return r(),f("div",vs,[e("div",_s,[e("div",ws,[e("div",bs,[e("div",ks,[e("a",{href:S.value,title:"梅斯小智"},m[1]||(m[1]=[e("img",{src:"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png",alt:""},null,-1)]),8,ys)]),e("div",xs,[e("div",$s,[e("ul",null,[E(T)=="zh"?(r(),f("li",Is,[e("div",As,[e("button",{type:"primary",onClick:pe,style:{"background-image":"linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%)"},class:"px-[15px] py-[4px] flex items-center h-[28px] rounded border-none text-xs text-[#614018]"},c(((te=V.value)==null?void 0:te.packageType)=="免费"?"升级订阅":((ae=V.value)==null?void 0:ae.packageType)=="连续包月"||((G=V.value)==null?void 0:G.packageType)=="连续包年"?"修改订阅":"订阅"),1)]),e("div",Ts,[e("button",{type:"primary",onClick:pe,style:{"background-image":"linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%)"},class:"px-[14px] py-[4px] rounded-[8px] flex items-center h-[28px] rounded border-none text-xs text-[#614018] whitespace-nowrap"},c(((Z=V.value)==null?void 0:Z.packageType)=="免费"?"升级订阅":((B=V.value)==null?void 0:B.packageType)=="连续包月"||((ge=V.value)==null?void 0:ge.packageType)=="连续包年"?"修改订阅":"订阅"),1)])])):C("",!0),z.value?C("",!0):(r(),f("li",Ss,[e("a",{href:"https://ai.medsci.cn/idoc",title:l.$t("tool.iDoc"),target:"_blank"},c(l.$t("tool.idocWebsite")),9,Cs)])),e("li",null,[e("div",Ns,[e("a",{href:"https://aisite.medsci.cn/",title:l.$t("tool.AINavigationSite"),target:"_blank"},c(l.$t("tool.AINavigationSite")),9,Ms)])]),e("li",{class:"index-user-img index-user-img_left",onMouseover:le,onMouseout:ye,onClick:le},[j.value?C("",!0):(r(),f("div",Us,[e("span",Os,c((s=L.value.filter(b=>b.value==M.value)[0])==null?void 0:s.name),1),e("span",Es,c(l.$t("market.switch")),1)])),e("div",{class:"ms-dropdown-menu",ref_key:"menu",ref:R},[e("div",Fs,[e("div",Ds,[y(p,null,{default:A(()=>[L.value.length>0?(r(),f("div",Bs,[(r(!0),f(we,null,be(L.value,b=>(r(),f("p",{key:b,onClick:ee(k=>Ie(b.value),["stop"]),class:Ke({langItemSelected:b.value===M.value})},c(b.name),11,Vs))),128))])):C("",!0)]),_:1})])])],512)],32),(n=O.value)!=null&&n.userId?(r(),f("li",{key:3,class:"index-user-img",onMouseover:xe,onMouseout:$e},[e("a",zs,[e("div",Ls,[e("img",{src:N.value?N.value+"?t="+Date.now():"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=",onError:ne,alt:""},null,40,Hs)])]),e("div",{class:"ms-dropdown-menu",ref_key:"menu1",ref:F},[e("div",Js,[e("a",{class:"new-header-exit ms-statis","ms-statis":"logout",href:"#",onClick:m[0]||(m[0]=b=>se())},c(l.$t("market.logout")),1),e("div",Ps,[e("div",Rs,[e("img",{class:"new-header-avatar",src:N.value?N.value+"?t="+Date.now():"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=",onError:ne,alt:""},null,40,qs),e("div",Gs,[e("span",null,c((u=O.value)!=null&&u.realName?(t=O.value)==null?void 0:t.realName:(U=O.value)==null?void 0:U.userName),1)])])])])],512)],32)):(r(),f("li",js,[e("a",{href:"javascript: void(0)",class:"ms-link",onClick:me},c(l.$t("market.login")),1)]))])])])])])])])}}},Zs=ke(Ks,[["__scopeId","data-v-cc7a7df6"]]),Ys={name:"FooterNavZH",data(){return{showFooter:!1}},head(){},computed:{},mounted(){},methods:{userFeedBack(){window.open("https://www.medsci.cn/message/list.do","_blank")}}},Ws={class:"bg-[#F7F7F7] bg"},Qs={key:0,id:"footer"},Xs={class:"footer-copyright1 ms-footer-copy w-footer-copy"},et={href:"https://www.medsci.cn/about/index.do?id=18",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"},st={href:"https://www.medsci.cn/about/index.do?id=9",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"};function tt(x,d,I,h,o,$){return r(),f("footer",Ws,[o.showFooter?(r(),f("div",Qs,d[0]||(d[0]=[e("div",{class:"wrapper mobile-b w-footer-wrapper",id:"foot"},[e("div",{class:"footer-widgets w-footer-widgets lets-do-4"},[e("div",{class:"widget-split item phone-hidden"},[e("div",{class:"widget ms-footer-img"},[e("div",null,[e("p",null,[e("a",{href:"https://www.medsci.cn",class:"ms-statis","ms-statis":"link"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/416a6450-b970-11ec-a1b8-6123b3ff61ea_MSLOGO1.png",alt:""})])]),e("p",{class:"bold w-footer-bold"}," 梅斯医学MedSci-临床医生发展平台 "),e("p",{class:"text-justify w-footer-des"}," 梅斯医学是面向医生的综合互联网平台，应用大数据和人工智能技术链接医生、患者、药械企业等，提供精准数字化医学传播解决方案，优化医疗生态，改善医疗质量，共创美好生活。 ")])])]),e("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[e("div",{class:"widget"},[e("h3",{class:"w-footer-h3"},"关于我们"),e("div",{class:"clearfix"},[e("ul",{class:"menu left"},[e("li",{class:"ms-link iconfont"},[e("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=9",class:"ms-statis","ms-statis":"link"},"关于我们")]),e("li",{class:"ms-link iconfont"},[e("a",{target:"_blank",href:"https://www.medsci.cn/recruit/job-list",class:"ms-statis","ms-statis":"link"},"加入我们")]),e("li",{class:"ms-link iconfont"},[e("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=21",class:"ms-statis","ms-statis":"link"},"版权合作")]),e("li",{class:"ms-link iconfont"},[e("a",{target:"_blank",href:"https://ir.medsci.cn/zh_cn/",class:"ms-statis","ms-statis":"link"},"投资者关系")]),e("li",{class:"ms-link iconfont"},[e("a",{target:"_blank",href:"http://medscihealthcare.com/",class:"ms-statis","ms-statis":"link"},"MedSci Healthcare")]),e("li",{class:"ms-link iconfont"},[e("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=19",class:"ms-statis","ms-statis":"link"},"友情链接")])])])])]),e("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[e("div",{class:"widget"},[e("h3",{class:"w-footer-h3"},"我们的业务"),e("div",{class:"clearfix"},[e("ul",{class:"menu left"},[e("li",{class:"ms-link iconfont"},[e("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"真实世界研究")]),e("li",{class:"ms-link iconfont"},[e("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"科研数智化")]),e("li",{class:"ms-link iconfont"},[e("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"数字化学术传播")])])])])]),e("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[e("div",{class:"widget"},[e("h3",{class:"w-footer-h3"},"我们的产品"),e("div",{class:"clearfix"},[e("ul",{class:"menu left"},[e("li",{class:"ms-link iconfont"},[e("a",{href:"https://www.medsci.cn/sci/index.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"期刊智能查询")]),e("li",{class:"ms-link iconfont"},[e("a",{href:"https://www.medsci.cn/sci/nsfc.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"国自然查询分析")]),e("li",{class:"ms-link iconfont"},[e("a",{href:"https://www.medsci.cn/guideline/search",target:"_blank",class:"ms-statis","ms-statis":"link"},"临床指南")]),e("li",{class:"ms-link iconfont"},[e("a",{href:"https://m.medsci.cn/scale",target:"_blank",class:"ms-statis","ms-statis":"link"},"医学公式计算")]),e("li",{class:"ms-link iconfont"},[e("a",{href:"https://dict.bioon.com/",target:"_blank",class:"ms-statis","ms-statis":"link"},"医药生物大词典")]),e("li",{class:"ms-link iconfont"},[e("a",{href:"https://class.medsci.cn/",target:"_blank",class:"ms-statis","ms-statis":"link"},"梅斯精品课")]),e("li",{class:"ms-link iconfont"},[e("a",{href:"https://open.medsci.cn/",target:"_blank",class:"ms-statis","ms-statis":"link"},"梅斯公开课")])])])])]),e("div",{class:"w-footer-right phone-hidden"},[e("div",{class:"widget"},[e("h3",{class:"w-footer-h3"},"新媒体矩阵"),e("div",{id:"footOwl",class:"owl-carousel"},[e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_gzh.png",alt:""}),e("span",null,"梅斯医学")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_zhongliu.png",alt:""}),e("span",null,"肿瘤新前沿")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_xueye.jpg",alt:""}),e("span",null,"血液新前沿")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_medsci_fengshi.jpg",alt:""}),e("span",null,"风湿新前沿")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/caaaa270-93a5-11ec-bca5-7f892b5df5d6_medsci_xqy.png",alt:""}),e("span",null,"呼吸新前沿")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_pifu.jpg",alt:""}),e("span",null,"皮肤新前沿")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/bce723f0-609d-11ed-b66b-937b834e3ef9_sjqianyan.jpg",alt:""}),e("span",null,"神经新前沿")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xiaohua.jpg",alt:""}),e("span",null,"消化新前沿")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xinxueguan.jpg",alt:""}),e("span",null,"心血管新前沿")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/dc22e340-957c-11ec-bca5-7f892b5df5d6_ms_shengwugu.png",alt:""}),e("span",null,"生物谷")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_app.png",alt:""}),e("span",null,"MedSci App")])])])])])],-1)]))):C("",!0),e("div",Xs,[e("p",null,[e("a",et,c(x.$t("market.privacyPolicy")),1),d[1]||(d[1]=e("span",{style:{margin:"0px 20px"}},"|",-1)),e("a",st,c(x.$t("market.termService")),1),d[2]||(d[2]=e("span",{style:{margin:"0px 20px"}},"|",-1)),d[3]||(d[3]=e("a",{href:"https://beian.miit.gov.cn/#/Integrated/index",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"},"备案号 沪ICP备14018916号-1",-1))])])])}const at=ke(Ys,[["render",tt],["__scopeId","data-v-fa4c9331"]]),nt={name:"FooterNavZH",data(){return{showFooter:!1}},head(){},computed:{},mounted(){},methods:{userFeedBack(){window.open("https://www.medsci.cn/message/list.do","_blank")}}},lt={class:"bg-[#F7F7F7] bg"},ot={key:0,id:"footer"},it={class:"footer-copyright1 ms-footer-copy w-footer-copy"},ct={href:"https://www.medsci.cn/about/index.do?id=18",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"},rt={href:"https://www.medsci.cn/about/index.do?id=9",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"};function dt(x,d,I,h,o,$){return r(),f("footer",lt,[o.showFooter?(r(),f("div",ot,d[0]||(d[0]=[e("div",{class:"wrapper mobile-b w-footer-wrapper",id:"foot"},[e("div",{class:"footer-widgets w-footer-widgets lets-do-4"},[e("div",{class:"widget-split item phone-hidden"},[e("div",{class:"widget ms-footer-img"},[e("div",null,[e("p",null,[e("a",{href:"https://www.medsci.cn",class:"ms-statis","ms-statis":"link"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/416a6450-b970-11ec-a1b8-6123b3ff61ea_MSLOGO1.png",alt:""})])]),e("p",{class:"bold w-footer-bold"}," 梅斯医学MedSci-临床医生发展平台 "),e("p",{class:"text-justify w-footer-des"}," 梅斯医学是面向医生的综合互联网平台，应用大数据和人工智能技术链接医生、患者、药械企业等，提供精准数字化医学传播解决方案，优化医疗生态，改善医疗质量，共创美好生活。 ")])])]),e("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[e("div",{class:"widget"},[e("h3",{class:"w-footer-h3"},"关于我们"),e("div",{class:"clearfix"},[e("ul",{class:"menu left"},[e("li",{class:"ms-link iconfont"},[e("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=9",class:"ms-statis","ms-statis":"link"},"关于我们")]),e("li",{class:"ms-link iconfont"},[e("a",{target:"_blank",href:"https://www.medsci.cn/recruit/job-list",class:"ms-statis","ms-statis":"link"},"加入我们")]),e("li",{class:"ms-link iconfont"},[e("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=21",class:"ms-statis","ms-statis":"link"},"版权合作")]),e("li",{class:"ms-link iconfont"},[e("a",{target:"_blank",href:"https://ir.medsci.cn/zh_cn/",class:"ms-statis","ms-statis":"link"},"投资者关系")]),e("li",{class:"ms-link iconfont"},[e("a",{target:"_blank",href:"http://medscihealthcare.com/",class:"ms-statis","ms-statis":"link"},"MedSci Healthcare")]),e("li",{class:"ms-link iconfont"},[e("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=19",class:"ms-statis","ms-statis":"link"},"友情链接")])])])])]),e("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[e("div",{class:"widget"},[e("h3",{class:"w-footer-h3"},"我们的业务"),e("div",{class:"clearfix"},[e("ul",{class:"menu left"},[e("li",{class:"ms-link iconfont"},[e("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"真实世界研究")]),e("li",{class:"ms-link iconfont"},[e("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"科研数智化")]),e("li",{class:"ms-link iconfont"},[e("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"数字化学术传播")])])])])]),e("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[e("div",{class:"widget"},[e("h3",{class:"w-footer-h3"},"我们的产品"),e("div",{class:"clearfix"},[e("ul",{class:"menu left"},[e("li",{class:"ms-link iconfont"},[e("a",{href:"https://www.medsci.cn/sci/index.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"期刊智能查询")]),e("li",{class:"ms-link iconfont"},[e("a",{href:"https://www.medsci.cn/sci/nsfc.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"国自然查询分析")]),e("li",{class:"ms-link iconfont"},[e("a",{href:"https://www.medsci.cn/guideline/search",target:"_blank",class:"ms-statis","ms-statis":"link"},"临床指南")]),e("li",{class:"ms-link iconfont"},[e("a",{href:"https://m.medsci.cn/scale",target:"_blank",class:"ms-statis","ms-statis":"link"},"医学公式计算")]),e("li",{class:"ms-link iconfont"},[e("a",{href:"https://dict.bioon.com/",target:"_blank",class:"ms-statis","ms-statis":"link"},"医药生物大词典")]),e("li",{class:"ms-link iconfont"},[e("a",{href:"https://class.medsci.cn/",target:"_blank",class:"ms-statis","ms-statis":"link"},"梅斯精品课")]),e("li",{class:"ms-link iconfont"},[e("a",{href:"https://open.medsci.cn/",target:"_blank",class:"ms-statis","ms-statis":"link"},"梅斯公开课")])])])])]),e("div",{class:"w-footer-right phone-hidden"},[e("div",{class:"widget"},[e("h3",{class:"w-footer-h3"},"新媒体矩阵"),e("div",{id:"footOwl",class:"owl-carousel"},[e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_gzh.png",alt:""}),e("span",null,"梅斯医学")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_zhongliu.png",alt:""}),e("span",null,"肿瘤新前沿")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_xueye.jpg",alt:""}),e("span",null,"血液新前沿")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_medsci_fengshi.jpg",alt:""}),e("span",null,"风湿新前沿")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/caaaa270-93a5-11ec-bca5-7f892b5df5d6_medsci_xqy.png",alt:""}),e("span",null,"呼吸新前沿")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_pifu.jpg",alt:""}),e("span",null,"皮肤新前沿")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/bce723f0-609d-11ed-b66b-937b834e3ef9_sjqianyan.jpg",alt:""}),e("span",null,"神经新前沿")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xiaohua.jpg",alt:""}),e("span",null,"消化新前沿")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xinxueguan.jpg",alt:""}),e("span",null,"心血管新前沿")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/dc22e340-957c-11ec-bca5-7f892b5df5d6_ms_shengwugu.png",alt:""}),e("span",null,"生物谷")]),e("div",{class:"item w-owl-item"},[e("img",{src:"https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_app.png",alt:""}),e("span",null,"MedSci App")])])])])])],-1)]))):C("",!0),e("div",it,[e("p",null,[e("a",ct,c(x.$t("market.privacyPolicy")),1),d[1]||(d[1]=e("span",{style:{margin:"0px 20px"}},"|",-1)),e("a",rt,c(x.$t("market.termService")),1)])])])}const pt=ke(nt,[["render",dt],["__scopeId","data-v-71657141"]]),ut="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%2029%2029'%20class='design-iconfont'%3e%3cg%20transform='rotate(-45%2015.36396167%206.36396076)'%20fill='%235298FF'%20fill-rule='evenodd'%3e%3cpath%20d='M9,0%20C13.9705627,0%2018,4.02943725%2018,9%20C18,13.7801691%2014.2733399,17.68993%209.56686501,17.9824374%20L9.69675669,15.9657586%20C13.235637,15.6160543%2016,12.6308529%2016,9%20C16,5.13400675%2012.8659932,2%209,2%20C5.13400675,2%202,5.13400675%202,9%20C2,11.7045844%203.53383568,14.0509224%205.77908446,15.2165912%20L4.77843352,16.9505064%20C1.93552483,15.437831%200,12.4449485%200,9%20C0,4.02943725%204.02943725,0%209,0%20Z'/%3e%3crect%20x='9'%20y='16'%20width='2'%20height='6'%20rx='1'/%3e%3cpath%20d='M4.75930317,16.943048%20C5.48230534,17.1582288%205.96481235,17.0672504%206.20682419,16.670113%20C6.49391901,16.1351207%206.37249799,15.4723489%205.77475988,15.2202576%20C5.28032363,15.0236511%204.94183806,15.5979146%204.75930317,16.943048%20Z'/%3e%3c/g%3e%3c/svg%3e";function mt(x){const d={};return x.forEach(I=>{const[h]=I.key.split("."),o=JSON.parse(I.value);d[h]||(d[h]={}),d[h]={...d[h],...o}}),d}function Le(x,d){const I=Date.now();localStorage.setItem(x+"_value",d),localStorage.setItem(x+"_timestamp",I)}function He(x,d){const I=x+"_value",h=x+"_timestamp",o=localStorage.getItem(I),$=localStorage.getItem(h);if(o!==null&&$!==null){const T=new Date($);return(new Date-T)/(1e3*60*60*24)>d?(localStorage.removeItem(I),localStorage.removeItem(h),null):o}else return null}function gt(){let x=He("current_langs_pack",7),d=He("current_langs_pack_umo",7);(!x||!d)&&fetch("https://ai.medon.com.cn/dev-api/ai-base/index/getConfigPage").then(h=>{if(!h.ok)throw new Error("Network response was not ok");return h.json()}).then(h=>{if(h.data.list.length!==0){x=JSON.stringify(mt(h.data.list)),Le("current_langs_pack",x);let $=h.data.list.filter(T=>T.value!="{}"&&T.key.includes(".dify")).reduce((T,D)=>(T[D.key.substr(0,D.key.indexOf("."))]||(T[D.key.substr(0,D.key.indexOf("."))]={}),T[D.key.substr(0,D.key.indexOf("."))]=JSON.parse(D.value),T),{});Le("current_langs_pack_umo",JSON.stringify($))}}).catch(h=>{console.error("Failed to fetch language config:",h)})}var P={};const ft={class:"bg-[#F9F9F9] overflow-auto h-full"},ht={class:"pt-[75px] text-white mb-[30px] font-bold"},vt={class:"flex justify-center"},_t={class:"content"},wt={class:"flex justify-center my-8 bg-[#F9F9F9]"},bt={class:"flex items-center"},kt=["onClick"],yt={class:"mr-2 px-4 cursor-pointer m_font"},xt={href:"https://aisite.medsci.cn/",target:"_blank"},$t={key:0,class:"menu-box flex flex-wrap justify-between"},It={class:"flex mb-1 card-item"},At={class:"flex",style:{width:"75%","align-items":"center"}},Tt=["src"],St=["title"],Ct=["href","onClick","title"],Nt=["innerHTML"],Mt={style:{width:"30%","text-align":"right","font-size":"14px"}},Ut=["href","onClick","title"],Ot=["title","innerHTML"],Et={class:"flex justify-between items-center"},Ft={class:"text-[#B0B0B0]"},Dt={key:0,class:"during_order"},Bt={key:1,class:"delay_order"},Vt={key:1,class:"tab_box"},jt={class:"menu-box flex flex-wrap justify-between"},zt={class:"flex mb-1 card-item"},Lt={class:"flex",style:{width:"75%","align-items":"center"}},Ht=["src"],Jt=["title"],Pt=["href","onClick","title"],Rt=["innerHTML"],qt={style:{width:"30%","text-align":"right"}},Gt=["href","onClick","title"],Kt=["innerHTML"],Zt={class:"flex justify-between items-center"},Yt={class:"text-[#B0B0B0]"},Wt={key:0,class:"during_order"},Qt={key:1,class:"delay_order"},Xt="faq.xAI",ea={__name:"index",async setup(x){let d,I;const{t:h,locale:o}=Pe(),$=de("ai_apps_lang",{domain:".medon.com.cn",maxAge:30*24*60*60*12});location!=null&&location.origin.includes("medon.com.cn")||(location==null||location.origin.includes("medsci.cn"));const T=je("基于AI的写作文本加工.png"),D=je("基于AI的写作文本加工In.png"),V=qe();ls();const N=g(""),O=g([{value:"我的应用",remark:"我的应用"},{value:"",remark:"全部"}]),j=g(!1),M=g(1),L=g(null),S=g(null),H=g("first"),R=g({}),F=g(!1),z=async s=>{if(!(s!=null&&s.dAppUuid)){Se({message:"请先至后台绑定应用实例",type:"warning"});return}await Be(s.appUuid,localStorage.getItem("openid")),sessionStorage.setItem("nodeInfo",JSON.stringify(s)),ne()&&s.appType=="写作"?ze({title:"提示",message:"写作类智能体请使用电脑web端打开，获取最佳使用体验。",confirmButtonColor:"#D7813F"}):(s.appType=="写作"&&localStorage.setItem("appWrite-"+s.appUuid,JSON.stringify({appUuid:s.appUuid,directoryMd:s.directoryMd})),window.open(s.url))},v=g({appType:"",socialUserId:"",appLang:"",order:2,isMine:2}),ne=()=>/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry)/i.test(navigator.userAgent),le=()=>{F.value=!1},ye=async()=>{var n,u;let s=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:"",refer:"",userAgen:"",time:new Date().getTime(),url:"",actionValue:"",userAction:"Exposure",actionCode:null,userId:(n=S.value)==null?void 0:n.userId,userToken:"",channel:"MedSci_xAI",appId:"",userUuid:(u=S.value)==null?void 0:u.openid}];await is.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",s)},xe=async()=>{o.value=="zh"&&(i.value=await Fe()),F.value=!0},$e=async(s,n)=>{var t;let u=Ne(o.value);if(!((t=S.value)!=null&&t.userId))!u||u=="zh"?window.addLoginDom():location.href=location.origin+"/"+o.value+"/login";else{const U={appUuid:n,priceId:s.priceId,monthNum:s.monthNum};let b=await cs(U);b&&(Se({type:"success",message:h("tool.sS")}),setTimeout(()=>{location.href=b},1e3))}},pe=s=>{var n;((n=s.appUser)==null?void 0:n.status)==1?me(s):B(s)},oe=s=>{if(!s){l.value=JSON.parse(JSON.stringify(q));return}let n=[];M.value==1?n=JSON.parse(JSON.stringify(q)):M.value!=0?n=JSON.parse(JSON.stringify(q)).filter(k=>k.appType===O.value[M.value].value):M.value==0&&(n=JSON.parse(JSON.stringify(l.value)));const u=s.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),t=new RegExp(u,"gi"),U=n.filter(k=>t.test(k.appName)||t.test(k.appDescription)||t.test(k.mapType)),b=k=>k&&k.replace(t,_=>`<span style="color: #409eff">${_}</span>`);l.value=U.map(k=>({...k,appName:b(k.appName),appDescription:b(k.appDescription),mapType:b(k.mapType)}))},Ie=()=>{const s=w.get("userInfo");s!==S.value&&(S.value=s?JSON.parse(s):""),S.value&&(w.get("yudaoToken")&&clearInterval(se),Z())};let se;Ce(()=>{se=Ye(Ie,1e3)}),Ge(()=>{se&&clearInterval(se)});const ue=async(s,n)=>{var t;let u=await Ne(o.value);if(M.value=s,N.value="",N.value&&oe(N.value),!((t=S.value)!=null&&t.userId)&&M.value==0){l.value=[],!u||u=="zh"?window.addLoginDom():location.href=location.origin+"/"+o.value+"/login";return}else M.value!=0?(v.value.isMine=2,v.value.order=2,n.remark=="全部"?v.value.appType="":v.value.appType=n.value,v.value.socialUserId=S.value.plaintextUserId):(H.value="first",v.value.appType="",v.value.isMine=1,v.value.order=1,v.value.socialUserId=S.value.plaintextUserId),G()},me=async s=>{if(!(s!=null&&s.dAppUuid)){Se({message:"请先至后台绑定应用实例",type:"warning"});return}Be(s.appUuid,localStorage.getItem("openid")),sessionStorage.setItem("nodeInfo",JSON.stringify(s));const n=(window.location.origin.includes("medsci.cn")||window.location.origin.includes("medon.com.cn"),window.location.origin);if(s.appType==="工具"){window.open(`${n}${o.value=="zh"?"":"/"+o.value}/tool/${s==null?void 0:s.appNameEn}`,"_blank");return}else if(s.appType==="问答"){window.open(`${n}${o.value=="zh"?"":"/"+o.value}/chat/${s==null?void 0:s.appNameEn}`,"_blank");return}else if(s.appType==="写作"){if(ne()){ze({title:"提示",message:"写作类智能体请使用电脑web端打开，获取最佳使用体验。",confirmButtonColor:"#D7813F"});return}await gt(),localStorage.setItem("appWrite-"+s.appUuid,JSON.stringify({appUuid:s.appUuid,directoryMd:s.directoryMd})),window.open(`${window.location.origin}${o.value=="zh"?"":"/"+o.value}/write/${s.appNameEn}`)}};let q=[];const i=g(),l=g(null),m=g(["https://img.medsci.cn/medsci-ai/bg1.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg2.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg3.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg4.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg5.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg6.png?imageMogr2/format/webp"]),{data:p}=([d,I]=Re(async()=>Ze("getAppList",async()=>{var k;const n=de("userInfo");(k=n.value)!=null&&k.userId&&(v.value.socialUserId=n.value.plaintextUserId),$.value=o.value,v.value.appLang=he(o.value),v.value.languages=o.value;let u;o.value=="zh"&&(u=await Fe(null));const t=await De(v.value,null),U=await os($.value,null);let b=Math.floor(Math.random()*6);return t?(t.forEach(_=>{_.mapType=Q[_.appType]}),t.forEach(_=>{_.appType==="工具"?_.url=`https://ai.medon.com.cn${o.value=="zh"?"":"/"+o.value}/tool/${_==null?void 0:_.appNameEn}`:_.appType==="问答"?_.url=`https://ai.medon.com.cn${o.value=="zh"?"":"/"+o.value}/chat/${_==null?void 0:_.appNameEn}`:_.appType==="写作"&&(_.url=`https://ai.medon.com.cn${o.value=="zh"?"":"/"+o.value}/write/${_==null?void 0:_.appNameEn}`)}),[t,U,b,u]):null},{server:!0})),d=await d,I(),d);q=JSON.parse(JSON.stringify((p==null?void 0:p.value)&&(p==null?void 0:p.value[0]))),l.value=(p==null?void 0:p.value)&&(p==null?void 0:p.value[0]),p!=null&&p.value&&O.value.push(...p==null?void 0:p.value[1]),L.value=m.value[1],i.value=(p==null?void 0:p.value)&&(p==null?void 0:p.value[3]),hs({title:`${X.title[$.value]}`,meta:[{name:"keywords",content:`${X.keywords[$.value]}`},{name:"description",content:`${X.description[$.value]}`},{property:"og:type",content:"website"},{property:"og:title",content:`${X.title[$.value]}`},{property:"og:description",content:`${X.description[$.value]}`},{property:"og:image",content:"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png"},{name:"twitter:card",content:"summary_large_image"},{name:"twitter:title",content:`${X.title[$.value]}`},{name:"twitter:description",content:`${X.description[$.value]}`},{name:"twitter:image",content:"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png"}]}),Ce(async()=>{var n;const s=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${s}px`),$.value=="zh"&&(j.value=!0),S.value=w.get("userInfo")?JSON.parse(w.get("userInfo")):null,S.value||(w.remove("yudaoToken",{domain:"ai.medsci.cn"}),w.remove("yudaoToken",{domain:"ai.medon.com.cn"}),w.remove("yudaoToken",{domain:".medsci.cn"}),w.remove("yudaoToken",{domain:".medon.com.cn"}),w.remove("yudaoToken",{domain:"localhost"}),localStorage.removeItem("hasuraToken")),V.query.lang?v.value.appLang=Ee[V.query.lang]:v.value.appLang=he(o.value),(n=S.value)!=null&&n.userId||(v.value.socialUserId=0,he(o.value)&&(v.value.appLang=he(o.value))),ye()});const te=s=>{j.value=s},ae=s=>{v.value.appLang=Ee[s],G()},G=()=>{De(v.value).then(s=>{var n,u;s.forEach(t=>{t.appType==="工具"?t.url=(P.VITE_MODE=="development"?"http://localhost:3000":P.VITE_MODE=="test"?"https://ai.medon.com.cn":P.VITE_MODE=="prd"?"https://ai.medsci.cn":"")+`${o.value=="zh"?"":"/"+o.value}/tool/${t==null?void 0:t.appNameEn}`:t.appType==="问答"?t.url=(P.VITE_MODE=="development"?"http://localhost:3000":P.VITE_MODE=="test"?"https://ai.medon.com.cn":P.VITE_MODE=="prd"?"https://ai.medsci.cn":"")+`${o.value=="zh"?"":"/"+o.value}/chat/${t==null?void 0:t.appNameEn}`:t.appType==="写作"&&(t.url=(P.VITE_MODE=="development"?"http://localhost:3000":P.VITE_MODE=="test"?"https://ai.medon.com.cn":P.VITE_MODE=="prd"?"https://ai.medsci.cn":"")+`${o.value=="zh"?"":"/"+o.value}/write/${t==null?void 0:t.appNameEn}`)}),l.value=s==null?void 0:s.map(t=>({...t,mapType:Q[t.appType]})),v.value.appType==""&&(q=[...l.value]),v.value.isMine==1&&(H.value=="first"&&(l.value=(n=l.value)==null?void 0:n.filter(t=>{var U;return((U=t.appUser)==null?void 0:U.status)==1})),H.value=="second"&&(l.value=(u=l.value)==null?void 0:u.filter(t=>{var U;return((U=t.appUser)==null?void 0:U.status)==2})))}).catch(s=>{})},Z=()=>{if(Ve.get("yudaoToken"))return;const n=w.get("userInfo");if(n){const u=JSON.parse(n);try{rs({userId:u.userId,userName:u.userName,realName:u.realName,avatar:u.avatar,plaintextUserId:u.plaintextUserId,mobile:u.mobile,email:u.email}).then(t=>{t!=null&&t.token?(Ve.set("yudaoToken",t.token),localStorage.setItem("hasuraToken",t.htoken),localStorage.setItem("openid",t.openid),localStorage.setItem("socialUserId",t.socialUserId),localStorage.setItem("socialType",t.socialType)):console.error("登录失败: 未返回 token")})}catch(t){}}},B=async s=>{R.value=s,F.value=!0},ge=()=>{G()};return(s,n)=>{const u=Zs,t=W("el-icon"),U=W("el-input"),b=W("el-button"),k=W("el-card"),_=W("el-tab-pane"),We=W("el-tabs"),Qe=at,Xe=pt,Me=Je,es=W("el-dialog");return r(),f("div",ft,[y(u,{onGetAppLang:ae,subStatusDetail:i.value,onIsZHChange:te,onSubScript:xe},null,8,["subStatusDetail"]),e("div",{class:"flex flex-col items-center h-[246px] relative min-w-[980px]",style:Te({background:`url(${L.value}) no-repeat center`,backgroundSize:"cover"})},[e("h1",ht,c(s.$t(Xt)),1),e("div",vt,[y(U,{class:"!w-[888px] !h-[54px]",modelValue:N.value,"onUpdate:modelValue":n[0]||(n[0]=a=>N.value=a),placeholder:s.$t("market.keywords"),clearable:"",onInput:oe},{prefix:A(()=>[y(t,{size:"24",class:"cursor-pointer mt-[2px]"},{default:A(()=>n[4]||(n[4]=[e("img",{class:"w-[24px] h-[24px]",src:ut,alt:""},null,-1)])),_:1})]),_:1},8,["modelValue","placeholder"])])],4),e("main",null,[e("div",_t,[e("div",wt,[e("div",bt,[(r(!0),f(we,null,be(O.value,(a,Y)=>(r(),f("div",{class:Ke(["mr-2 px-4 py-1 cursor-pointer m_font",M.value==Y?"bg-[#409eff] text-white rounded-4xl":""]),key:Y,onClick:K=>ue(Y,a)},c(s.$t(`${E(Q)[a.remark]}`)),11,kt))),128)),e("div",yt,[e("a",xt,c(s.$t("tool.AINavigationSite")),1)])])]),M.value!=0?(r(),f("div",$t,[(r(!0),f(we,null,be(l.value,(a,Y)=>(r(),J(k,{shadow:"hover",class:"max-w-[369px] cursor-pointer mb-[16px] !bg-[#FBFBFB] !border-0 cursor-item",style:Te({background:`url(${a.isInternalUser==1?E(D):E(T)}) no-repeat center`,backgroundSize:"cover",width:"calc(33.33% - 8px)"}),key:Y,onClick:K=>me(a)},{default:A(()=>{var K,ie,ce,re;return[e("div",It,[e("div",At,[e("img",{class:"w-[40px] h-[40px] block mr-2",style:{"border-radius":"10px"},src:a.appIcon,alt:"icon"},null,8,Tt),e("div",{class:"text-[16px] font-bold text-dark-200 two_lines",style:{width:"calc(100% - 40px)"},title:a.appName},[e("a",{href:a.url,onClick:ee(fe=>z(a),["stop","prevent"]),title:a.appName,target:"_blank"},[e("h6",{innerHTML:a.appName},null,8,Nt)],8,Ct)],8,St)]),e("div",Mt,[e("a",{href:a.url,onClick:ee(fe=>z(a),["stop","prevent"]),title:a.appName,target:"_blank"},[y(b,{style:{"--el-button-bg-color":"#fff"},size:"small",color:"#2F92EE",plain:"",round:""},{default:A(()=>[ve(c(s.$t("market.open")),1),y(t,null,{default:A(()=>[y(E(_e),{style:{"margin-left":"4px"}})]),_:1})]),_:1})],8,Ut)])]),e("div",{class:"textOverflowFour tex-[#838383] leading-[18px] h-[72px] mb-[12px]",title:a.appDescription,innerHTML:a.appDescription},null,8,Ot),e("div",Et,[e("div",Ft,c(s.$t(`${E(Q)[a.appType]}`)),1),((K=a.appUser)==null?void 0:K.status)==1?(r(),f("div",Dt,c(s.$t("market.subUntil"))+c((ie=a.appUser)==null?void 0:ie.expireAt)+c(s.$t("market.expiredOn")),1)):C("",!0),((ce=a.appUser)==null?void 0:ce.status)==2?(r(),f("div",Bt,c(s.$t("market.haveBeen"))+c((re=a.appUser)==null?void 0:re.expireAt)+c(s.$t("market.expiredOn")),1)):C("",!0)])]}),_:2},1032,["style","onClick"]))),128))])):(r(),f("div",Vt,[y(We,{modelValue:H.value,"onUpdate:modelValue":n[1]||(n[1]=a=>H.value=a),class:"demo-tabs",onTabChange:ge},{default:A(()=>[y(_,{label:s.$t("market.subscribed"),name:"first"},null,8,["label"]),y(_,{label:s.$t("market.expired"),name:"second"},null,8,["label"])]),_:1},8,["modelValue"]),e("div",jt,[(r(!0),f(we,null,be(l.value,(a,Y)=>(r(),J(k,{shadow:"hover",class:"max-w-[369px] cursor-pointer mb-[16px] !bg-[#FBFBFB] !border-0 cursor-item",style:Te({background:`url(${E(T)}) no-repeat center`,backgroundSize:"cover",width:"calc(33.33% - 8px)",maxHeight:"189.5px"}),key:Y,onClick:K=>pe(a)},{default:A(()=>{var K,ie,ce,re,fe,Ue;return[e("div",zt,[e("div",Lt,[e("img",{class:"w-[40px] h-[40px] block mr-2",style:{"border-radius":"10px"},src:a.appIcon,alt:"icon"},null,8,Ht),e("div",{class:"text-[16px] font-bold text-dark-200 two_lines",style:{width:"calc(100% - 40px)"},title:a.appName},[e("a",{href:a.url,onClick:ee(Ae=>z(a),["stop","prevent"]),title:a.appName,target:"_blank"},[e("h6",{innerHTML:a.appName},null,8,Rt)],8,Pt)],8,Jt)]),e("div",qt,[e("a",{href:a.url,onClick:ee(Ae=>z(a),["stop","prevent"]),title:a.appName,target:"_blank"},[((K=a.appUser)==null?void 0:K.status)==1?(r(),J(b,{key:0,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#2F92EE",plain:"",round:""},{default:A(()=>[ve(c(s.$t("market.open")),1),y(t,null,{default:A(()=>[y(E(_e),{style:{"margin-left":"4px"}})]),_:1})]),_:1})):C("",!0)],8,Gt),((ie=a.appUser)==null?void 0:ie.status)==2?(r(),J(b,{key:0,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#FF9A45",plain:"",round:"",onClick:ee(Ae=>B(a),["stop"])},{default:A(()=>[ve(c(s.$t("market.renew")),1),y(t,null,{default:A(()=>[y(E(_e),{style:{"margin-left":"4px"}})]),_:1})]),_:2},1032,["onClick"])):C("",!0),a.appUser?C("",!0):(r(),J(b,{key:1,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#FF9A45",plain:"",round:"",onClick:ee(Ae=>B(a),["stop"])},{default:A(()=>[ve(c(s.$t("market.subscribe")),1),y(t,null,{default:A(()=>[y(E(_e),{style:{"margin-left":"4px"}})]),_:1})]),_:2},1032,["onClick"]))])]),e("div",{class:"textOverflowFour tex-[#838383] leading-[18px] h-[72px] mb-[12px]",innerHTML:a.appDescription},null,8,Kt),e("div",Zt,[e("div",Yt,c(s.$t(`${E(Q)[a.appType]}`)),1),((ce=a.appUser)==null?void 0:ce.status)==1?(r(),f("div",Wt,c(s.$t("market.subUntil"))+c((re=a.appUser)==null?void 0:re.expireAt)+c(s.$t("market.expiredOn")),1)):C("",!0),((fe=a.appUser)==null?void 0:fe.status)==2?(r(),f("div",Qt,c(s.$t("market.haveBeen"))+c((Ue=a.appUser)==null?void 0:Ue.expireAt)+c(s.$t("market.expiredOn")),1)):C("",!0)])]}),_:2},1032,["style","onClick"]))),128))])]))])]),j.value?(r(),J(us,{key:0})):C("",!0),j.value?(r(),J(Qe,{key:1,class:"mobile_footer"})):C("",!0),j.value?C("",!0):(r(),J(Xe,{key:2,class:"mobile_footer"})),F.value?(r(),J(es,{key:3,modelValue:F.value,"onUpdate:modelValue":n[2]||(n[2]=a=>F.value=a),class:"payPC","show-close":!1},{default:A(()=>[y(Me,null,{default:A(()=>[y(ms,{userInfo:S.value,subStatusDetail:i.value,appTypes:E(Q),currentItem:R.value,onToAgreement:s.toAgreement,onClose:le,onSubscribe:$e},null,8,["userInfo","subStatusDetail","appTypes","currentItem","onToAgreement"])]),_:1})]),_:1},8,["modelValue"])):C("",!0),y(E(fs),{show:F.value,"onUpdate:show":n[3]||(n[3]=a=>F.value=a),round:"",closeable:"",class:"payMobile",position:"bottom",style:{height:"90%"}},{default:A(()=>[y(Me,null,{default:A(()=>[y(gs,{userInfo:S.value,subStatusDetail:i.value,appTypes:E(Q),currentItem:R.value,onToAgreement:s.toAgreement,onClose:le},null,8,["userInfo","subStatusDetail","appTypes","currentItem","onToAgreement"])]),_:1})]),_:1},8,["show"])])}}},pa=ke(ea,[["__scopeId","data-v-084a0b6a"]]);export{pa as default};
