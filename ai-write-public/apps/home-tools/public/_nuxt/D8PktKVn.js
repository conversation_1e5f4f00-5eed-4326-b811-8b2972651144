import{a as C,aX as u,r as D,aY as O,aZ as B,al as R,a_ as E,af as H,J as M,a$ as P,b0 as S,b1 as V,L as j}from"./B7yib2Q0.js";const q=t=>t==="defer"||t===!1;function T(...t){var g;const c=typeof t[t.length-1]=="string"?t.pop():void 0;typeof t[0]!="string"&&t.unshift(c);let[r,f,a={}]=t;if(typeof r!="string")throw new TypeError("[nuxt] [asyncData] key must be a string.");if(typeof f!="function")throw new TypeError("[nuxt] [asyncData] handler must be a function.");const e=C(),b=f,p=()=>u.value,w=()=>e.isHydrating?e.payload.data[r]:e.static.data[r];a.server??(a.server=!0),a.default??(a.default=p),a.getCachedData??(a.getCachedData=w),a.lazy??(a.lazy=!1),a.immediate??(a.immediate=!0),a.deep??(a.deep=u.deep),a.dedupe??(a.dedupe="cancel");const d=a.getCachedData(r,e),_=d!=null;if(!e._asyncData[r]||!a.immediate){(g=e.payload._errors)[r]??(g[r]=u.errorValue);const i=a.deep?D:O;e._asyncData[r]={data:i(_?d:a.default()),pending:D(!_),error:B(e.payload._errors,r),status:D("idle"),_default:a.default}}const s={...e._asyncData[r]};delete s._default,s.refresh=s.execute=(i={})=>{if(e._asyncDataPromises[r]){if(q(i.dedupe??a.dedupe))return e._asyncDataPromises[r];e._asyncDataPromises[r].cancelled=!0}if(i._initial||e.isHydrating&&i._initial!==!1){const l=i._initial?d:a.getCachedData(r,e);if(l!=null)return Promise.resolve(l)}s.pending.value=!0,s.status.value="pending";const o=new Promise((l,n)=>{try{l(b(e))}catch(y){n(y)}}).then(async l=>{if(o.cancelled)return e._asyncDataPromises[r];let n=l;a.transform&&(n=await a.transform(l)),a.pick&&(n=K(n,a.pick)),e.payload.data[r]=n,s.data.value=n,s.error.value=u.errorValue,s.status.value="success"}).catch(l=>{if(o.cancelled)return e._asyncDataPromises[r];s.error.value=V(l),s.data.value=j(a.default()),s.status.value="error"}).finally(()=>{o.cancelled||(s.pending.value=!1,delete e._asyncDataPromises[r])});return e._asyncDataPromises[r]=o,e._asyncDataPromises[r]},s.clear=()=>z(e,r);const h=()=>s.refresh({_initial:!0}),m=a.server!==!1&&e.payload.serverRendered;{const i=R();if(i&&m&&a.immediate&&!i.sp&&(i.sp=[]),i&&!i._nuxtOnBeforeMountCbs){i._nuxtOnBeforeMountCbs=[];const n=i._nuxtOnBeforeMountCbs;E(()=>{n.forEach(y=>{y()}),n.splice(0,n.length)}),H(()=>n.splice(0,n.length))}m&&e.isHydrating&&(s.error.value||d!=null)?(s.pending.value=!1,s.status.value=s.error.value?"error":"success"):i&&(e.payload.serverRendered&&e.isHydrating||a.lazy)&&a.immediate?i._nuxtOnBeforeMountCbs.push(h):a.immediate&&h();const o=S();if(a.watch){const n=M(a.watch,()=>s.refresh());o&&P(n)}const l=e.hook("app:data:refresh",async n=>{(!n||n.includes(r))&&await s.refresh()});o&&P(l)}const v=Promise.resolve(e._asyncDataPromises[r]).then(()=>s);return Object.assign(v,s),v}function z(t,c){c in t.payload.data&&(t.payload.data[c]=void 0),c in t.payload._errors&&(t.payload._errors[c]=u.errorValue),t._asyncData[c]&&(t._asyncData[c].data.value=void 0,t._asyncData[c].error.value=u.errorValue,t._asyncData[c].pending.value=!1,t._asyncData[c].status.value="idle"),c in t._asyncDataPromises&&(t._asyncDataPromises[c]&&(t._asyncDataPromises[c].cancelled=!0),t._asyncDataPromises[c]=void 0)}function K(t,c){const r={};for(const f of c)r[f]=t[f];return r}function F(t){}function I(t){return{}}export{I as a,F as b,T as u};
