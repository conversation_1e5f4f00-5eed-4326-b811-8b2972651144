import{D as L,G as j,u as O,r as d,a5 as P,E as p,o as E,a6 as F,a3 as G,g as _,t as J,v as M,x as o,y as r,z as l,A as i,B as b,a7 as D,a0 as $,a8 as W,a9 as Z}from"./B7yib2Q0.js";import{s as H}from"./x_rD_Ya3.js";import{_ as Q}from"./DlAUqK2U.js";const K={class:"bg-background text-foreground antialiased min-h-screen flex items-center justify-center"},X={class:"cl-rootBox cl-signUp-root justify-center"},Y={class:"cl-cardBox cl-signUp-start"},ee={class:"cl-card cl-signUp-start"},oe={class:"cl-header"},te={class:"cl-headerTitle"},se={class:"cl-headerSubtitle"},le={class:"cl-main"},ne={class:"cl-socialButtonsRoot"},ae={class:"cl-socialButtons"},ie={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},re={class:"cl-socialButtonsBlockButton-d"},ce={class:"cl-socialButtons"},de={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},ue={class:"cl-socialButtonsBlockButton-d"},me={class:"cl-socialButtonsRoot"},pe={class:"cl-internal-1pnppin"},_e={class:"cl-internal-742eeh"},ge={class:"cl-internal-2iusy0"},fe={class:"cl-footer 🔒️ cl-internal-4x6jej"},ve={class:"cl-footerAction cl-footerAction__signUp cl-internal-1rpdi70"},be={class:"cl-footerActionText cl-internal-kyvqj0","data-localization-key":"signUp.start.actionText"},ke={__name:"sign-up",setup(Ie){var T;const{t:s,locale:C}=L(),f=j();O();const k=f.params.socialType,I=f.query.authCode,h=f.query.authState,B=d(),y=d(),n=d({email:"",password:"",emailCode:"",userName:""}),S=d((location==null?void 0:location.origin.includes("medon.com.cn"))||(location==null?void 0:location.origin.includes("medsci.cn"))),x=P({userName:[{required:!0,message:s("tool.username_cannot_be_empty"),trigger:"blur"}],password:[{required:!0,message:s("tool.password_cannot_be_empty"),trigger:"blur"},{min:8,max:20,message:s("tool.pwdLength"),trigger:"blur"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[a-zA-Z\d\W_]{8,20}$/,message:s("tool.password_includes_uppercase_lowercase_numbers_and_symbols"),trigger:"blur"}],emailCode:[{required:!0,message:s("tool.verification_code_cannot_be_empty"),trigger:"blur"},{min:6,max:6,message:s("tool.verification_code_must_be_6_digits"),trigger:"blur"}],email:[{required:!0,message:s("tool.email_address_cannot_be_empty"),trigger:"blur"},{type:"email",message:s("tool.please_enter_a_valid_email_address"),trigger:"blur"}]}),v=d(!1),m=d(60),g=d(s("tool.send_verification_code")),V=p.get("userInfo")?(T=JSON.parse(p.get("userInfo")))==null?void 0:T.userId:"",w=e=>{D(e).then(t=>{window.location.href=t})},N=()=>{v.value=!0,g.value=`${m.value}${s("tool.retry_after_seconds")}`;let e=H(()=>{m.value-=1,g.value=`${m.value}${s("tool.retry_after_seconds")}`,m.value<=0&&(clearInterval(e),m.value=60,g.value=s("tool.send_verification_code"),v.value=!1)},1e3)},R=()=>{if(!n.value.email){$.error(s("tool.email_does_not_exist"));return}let e={email:n.value.email,type:"RegisterCode"};W(e).then(t=>{t&&N()})},A=async e=>{e&&await e.validate((t,u)=>{t&&Z(n.value).then(c=>{c&&($({type:"success",message:s("tool.registersuccess")}),setTimeout(()=>{location.href=S.value?"/apps/login":"login"},1e3))})})};return E(()=>{y.value=(location==null?void 0:location.origin)+"/"+C.value+"/login",V?location.href=location.origin+"/":k&&I&&h&&F(k,I,h).then(e=>{if(e!=null&&e.token&&(e!=null&&e.htoken)){G.set("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),e.userInfo.userId=e.userInfo.openid,e.userInfo.plaintextUserId=e.userInfo.socialUserId,e.userInfo.token={accessToken:e.userInfo.openid,accessTokenExpireTime:63072e4,refreshToken:e.userInfo.openid},location.origin.includes(".medsci.cn")?p.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medsci.cn"}):location.origin.includes(".medon.com.cn")?p.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medon.com.cn"}):p.set("userInfo",JSON.stringify(e.userInfo),{expires:365});const t=window.sessionStorage.getItem("redirectUrl");t?(window.location.href=t,window.sessionStorage.removeItem("redirectUrl")):location.href=location.origin+"/"}else console.error("登录失败: 未返回 token")})}),(e,t)=>{const u=_("el-input"),c=_("el-form-item"),U=_("el-button"),q=_("el-form"),z=_("el-link");return M(),J("div",K,[o("div",X,[o("div",Y,[o("div",ee,[o("div",oe,[o("div",null,[o("h1",te,r(e.$t("tool.create_account")),1),o("p",se,r(e.$t("tool.registration_greeting")),1)])]),o("div",le,[o("div",ne,[o("div",ae,[o("button",ie,[o("span",re,[t[7]||(t[7]=o("span",null,[o("img",{src:"https://img.medsci.cn/202412/cc7c7687b4804460a85d46c629132724-vcHTUHMGIylQ.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1)),o("span",{class:"cl-socialButtonsBlockButtonText",onClick:t[0]||(t[0]=a=>w(35))},r(e.$t("tool.continue_with_google")),1)])])]),o("div",ce,[o("button",de,[o("span",ue,[t[8]||(t[8]=o("span",null,[o("img",{src:"https://img.medsci.cn/202412/f9bf95f463c04d3e801aa6e97ef3d4b8-OSsmrMWR677i.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1)),o("span",{class:"cl-socialButtonsBlockButtonText",onClick:t[1]||(t[1]=a=>w(36))},r(e.$t("tool.continue_with_facebook")),1)])])])]),t[11]||(t[11]=o("div",{class:"cl-dividerRow"},[o("div",{class:"cl-dividerLine"}),o("p",{class:"cl-dividerText"},"or"),o("div",{class:"cl-dividerLine"})],-1)),o("div",me,[l(q,{ref_key:"ruleFormRef",ref:B,style:{"max-width":"600px"},model:n.value,rules:x,"label-width":"auto",class:"demo-ruleForm","label-position":"left",size:e.formSize,"status-icon":""},{default:i(()=>[l(c,{label:e.$t("tool.username"),prop:"userName"},{default:i(()=>[l(u,{modelValue:n.value.userName,"onUpdate:modelValue":t[2]||(t[2]=a=>n.value.userName=a)},null,8,["modelValue"])]),_:1},8,["label"]),l(c,{label:e.$t("tool.email"),prop:"email"},{default:i(()=>[l(u,{modelValue:n.value.email,"onUpdate:modelValue":t[3]||(t[3]=a=>n.value.email=a)},null,8,["modelValue"])]),_:1},8,["label"]),l(c,{label:e.$t("tool.verification_code"),prop:"emailCode"},{default:i(()=>[l(u,{modelValue:n.value.emailCode,"onUpdate:modelValue":t[4]||(t[4]=a=>n.value.emailCode=a)},{append:i(()=>[l(U,{onClick:R,disabled:v.value,type:"primary"},{default:i(()=>[b(r(g.value),1)]),_:1},8,["disabled"])]),_:1},8,["modelValue"])]),_:1},8,["label"]),l(c,{label:e.$t("tool.password"),prop:"password",style:{"padding-bottom":"20px"}},{default:i(()=>[l(u,{modelValue:n.value.password,"onUpdate:modelValue":t[5]||(t[5]=a=>n.value.password=a),"show-password":"true"},null,8,["modelValue"])]),_:1},8,["label"]),l(c,null,{default:i(()=>[o("div",pe,[t[10]||(t[10]=o("div",{id:"clerk-captcha",class:"cl-internal-3s7k9k"},null,-1)),o("div",_e,[l(U,{class:"cl-formButtonPrimary cl-button 🔒️ cl-internal-ttumny",onClick:t[6]||(t[6]=a=>A(B.value))},{default:i(()=>[o("span",ge,[b(r(e.$t("tool.continue")),1),t[9]||(t[9]=o("svg",{class:"cl-buttonArrowIcon 🔒️ cl-internal-1c4ikgf"},[o("path",{fill:"currentColor",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"m7.25 5-3.5-2.25v4.5L7.25 5Z"})],-1))])]),_:1})])])]),_:1})]),_:1},8,["model","rules","size"])])])]),o("div",fe,[o("div",ve,[o("span",be,r(e.$t("tool.alreadyhaveanaccount")),1),l(z,{href:y.value,class:"cl-footerActionLink"},{default:i(()=>[b(r(e.$t("tool.signIn")),1)]),_:1},8,["href"])])])])])])}}},we=Q(ke,[["__scopeId","data-v-5478f50f"]]);export{we as default};
