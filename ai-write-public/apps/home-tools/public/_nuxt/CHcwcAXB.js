const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./B7yib2Q0.js","./entry.BM64DQIl.css"])))=>i.map(i=>d[i]);
import{ab as R,r as v,j as d,F as V,t as l,v as a,x as r,ac as B,M as A,K as _,y as c,N as E,ad as F,ae as H}from"./B7yib2Q0.js";import{u as J,b as W}from"./D8PktKVn.js";import{u as Q}from"./BvTBxqY-.js";import{_ as Y}from"./DlAUqK2U.js";const z={class:"page-wrapper"},G={class:"main-container"},X={class:"article-container"},Z={key:0,class:"loading-container"},j={key:1,class:"error-container"},ee={class:"article-header"},te={class:"article-title"},se={class:"meta-info"},oe={key:0,class:"publish-time"},le={key:1,class:"author"},ae={key:2,class:"location"},re={key:0,class:"cover-image"},ne=["src","alt"],ie={key:1,class:"categories"},ce={key:2,class:"article-summary"},ue=["innerHTML"],de={key:4},ve={class:"sidebar"},_e={key:0,class:"sidebar-section tool-section"},pe={class:"tool-card"},ge={class:"tool-icon"},ye=["src"],he={class:"tool-info"},me={class:"tool-name"},fe={class:"sidebar-section"},ke={key:0,class:"loading-side"},we={key:1},Ae=["onClick"],be={class:"question-title"},Se={class:"question-meta"},Ee={key:0,class:"empty-data"},Oe={class:"sidebar-section"},$e={key:0,class:"loading-side"},De={key:1},Te=["onClick"],Ce={class:"related-article-title"},xe={class:"related-article-meta"},Ne={key:0,class:"empty-data"},Ie={__name:"[id]",async setup(Le){let f,O;const L=R(),s=v({}),i=v(null),$=v(!0),D=v(!1),T=v(!1),p=v([]),g=v([]),k=v(!0),w=v(!0),M=d(()=>{if(!s.value.content)return"";s.value.content.substring(0,50)+"";const e=s.value.content.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&ldquo;/g,'"').replace(/&rdquo;/g,'"').replace(/&hellip;/g,"...").replace(/&nbsp;/g," ");return e.substring(0,50)+"",e}),b=t=>{if(!t)return null;try{return typeof t=="string"?JSON.parse(t):t}catch(e){return console.error("解析SEO数据失败:",e),null}},S=d(()=>{var e;if(!((e=i.value)!=null&&e.seo_title))return s.value.title,s.value.title;const t=b(i.value.seo_title);return(t==null?void 0:t.primary)||s.value.title}),C=d(()=>{var e;if(!((e=i.value)!=null&&e.seo_description))return s.value.summary,s.value.summary;const t=b(i.value.seo_description);return(t==null?void 0:t.core)||s.value.summary}),y=d(()=>{var t;if(!((t=i.value)!=null&&t.seo_description))return[];try{const e=b(i.value.seo_description);return e==null||e.keywords,(Array.isArray(e==null?void 0:e.keywords)?e.keywords:[]).filter(o=>o!=="梅斯医学")}catch(e){return console.error("解析SEO关键词失败:",e),[]}}),q=d(()=>y.value.length>0?y.value.join(","):s.value.articleKeyword),{data:u}=([f,O]=V(async()=>J("articleData",async t=>{try{const e=L.params.id,n=W();let o;try{const h=(await F(async()=>{const{default:K}=await import("./B7yib2Q0.js").then(P=>P.b3);return{default:K}},__vite__mapDeps([0,1]),import.meta.url)).default,I=`${typeof window>"u"?"http://localhost:48081":""}/ai-base/index/snapshot/getArticleWithSEO`,m=await h.get(I,{params:{id:e}});if(m.status,m.status===200&&m.data&&m.data.code===0)o=m.data.data;else throw new Error("响应格式错误")}catch(h){console.error("直接请求失败，尝试API函数:",h.message),o=await H(e,n)}return o&&o.recentArticles?(o.recentArticles.length,p.value=o.recentArticles):console.error("响应中缺少recentArticles数据"),o&&o.relatedArticles?(o.relatedArticles.length,g.value=o.relatedArticles):console.error("响应中缺少relatedArticles数据"),k.value=!1,w.value=!1,o&&o.seo&&o.article?(T.value=!0,{seo:o.seo,articleData:o.article,recentArticles:o.recentArticles||[],relatedArticles:o.relatedArticles||[],error:!1}):(console.error("响应数据不符合预期结构"),{seo:null,articleData:null,recentArticles:[],relatedArticles:[],error:!0})}catch(e){return console.error("获取数据失败:",e.message),k.value=!1,w.value=!1,{seo:null,articleData:null,recentArticles:[],relatedArticles:[],error:!0}}})),f=await f,O(),f);u.value&&(u.value,i.value=u.value.seo,s.value=u.value.articleData||{},u.value.recentArticles&&u.value.recentArticles.length>0&&(p.value=u.value.recentArticles,p.value.length),u.value.relatedArticles&&u.value.relatedArticles.length>0&&(g.value=u.value.relatedArticles,g.value.length),D.value=u.value.error,$.value=!1,k.value=!1,w.value=!1,T.value=!0,i.value,Object.keys(s.value).length,p.value.length,g.value.length),Q({title:d(()=>S.value?`${S.value} - 梅斯医学`:"文章 - 梅斯医学"),meta:[{name:"description",content:d(()=>C.value||"")},{name:"keywords",content:d(()=>q.value||"")},{property:"og:title",content:d(()=>S.value||"")},{property:"og:description",content:d(()=>C.value||"")},{property:"og:image",content:d(()=>s.value.cover||"")},{property:"og:type",content:"article"}]});const x=t=>{if(!t)return"";const e=new Date(t);return`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`},N=t=>{if(!t)return;const e=window.location.origin;window.open(`${e}/article/${t}`,"_blank")},U=()=>{if(!i.value)return;const t=i.value,e=window.location.origin;t.app_type==="工具"?window.open(`${e}/tool/${t.app_name_en||t.app_name}`,"_blank"):t.app_type==="问答"?window.open(`${e}/chat/${t.app_name_en||t.app_name}`,"_blank"):t.app_type==="写作"&&(localStorage.setItem("appWrite-"+t.app_uuid,JSON.stringify({appUuid:t.app_uuid,directoryMd:t.directory_md||""})),window.open(`${window.location.origin}/write/${t.app_name_en||t.app_name}`))};return(t,e)=>(a(),l("div",z,[r("div",G,[r("div",X,[$.value?(a(),l("div",Z,"加载中...")):D.value?(a(),l("div",j,"无法加载文章")):(a(),l(A,{key:2},[r("div",ee,[r("h1",te,c(s.value.title),1),r("div",se,[s.value.publishedTimeString?(a(),l("span",oe,c(s.value.publishedTimeString),1)):_("",!0),s.value.createdName?(a(),l("span",le,c(s.value.createdName),1)):_("",!0),s.value.ipAttribution?(a(),l("span",ae,"发表于"+c(s.value.ipAttribution),1)):_("",!0)])]),s.value.cover?(a(),l("div",re,[r("img",{src:s.value.cover,alt:s.value.title},null,8,ne)])):_("",!0),y.value&&y.value.length>0?(a(),l("div",ie,[(a(!0),l(A,null,E(y.value,(n,o)=>(a(),l("span",{key:o,class:"category"},c(n),1))),128))])):_("",!0),s.value.summary?(a(),l("div",ce,c(s.value.summary),1)):_("",!0),s.value.content?(a(),l("div",{key:3,class:"article-content",innerHTML:M.value},null,8,ue)):(a(),l("pre",de,c(JSON.stringify(s.value,null,2)),1))],64))]),r("div",ve,[i.value?(a(),l("div",_e,[e[0]||(e[0]=r("div",{class:"section-title"},"工具使用",-1)),r("div",pe,[r("div",ge,[r("img",{src:i.value.app_icon||"https://www.medsci.cn/images/logo.png",alt:"工具图标"},null,8,ye)]),r("div",he,[r("div",me,c(i.value.app_name||"工具名称"),1),r("button",{onClick:U,class:"use-button"},"使用")])])])):_("",!0),r("div",fe,[e[1]||(e[1]=r("div",{class:"section-title"},"最新文章",-1)),k.value?(a(),l("div",ke,"加载中...")):(a(),l("div",we,[(a(!0),l(A,null,E(p.value.slice(0,3),(n,o)=>(a(),l("div",{key:o,class:"article-item",onClick:h=>N(n.uuid)},[r("div",be,c(n.title),1),r("div",Se,c(x(n.publishedTime||n.createdTime)),1)],8,Ae))),128)),!p.value||p.value.length===0?(a(),l("div",Ee," 暂无数据 ")):_("",!0)]))]),r("div",Oe,[e[2]||(e[2]=r("div",{class:"section-title"},"相关文章",-1)),w.value?(a(),l("div",$e,"加载中...")):(a(),l("div",De,[(a(!0),l(A,null,E(g.value.slice(0,3),(n,o)=>(a(),l("div",{key:o,class:"article-item",onClick:h=>N(n.uuid)},[r("div",Ce,c(n.title),1),r("div",xe,c(x(n.publishedTime||n.createdTime)),1)],8,Te))),128)),!g.value||g.value.length===0?(a(),l("div",Ne," 暂无数据 ")):_("",!0)]))])])]),e[3]||(e[3]=B('<div class="article-footer" data-v-69782924><div class="footer-bottom" data-v-69782924><div class="copyright" data-v-69782924> ©Copyright 2012-至今 梅斯（MedSci） </div><div class="license-info" data-v-69782924> 增值电信业务经营许可证 | 备案号 沪ICP备14018916号-1 | 互联网药品信息服务资格证书((沪)-非经营性-2020-0033) | 出版物经营许可证 </div><div class="license-info" data-v-69782924> 上海工商 | 上海网警网络110 | 网络社会征信网 | 违法和不良信息举报中心 | 信息举报中心 |违法举报：021-54485309 | 沪公网安备 31010402000380 </div><div class="footer-info" data-v-69782924> 本站旨在介绍医药健康研究进展和信息，不作为诊疗方案推荐。如需获得诊断或治疗方面指导，请前往正规医院就诊。 </div><div class="footer-info" data-v-69782924> 用户应遵守著作权法，尊重著作权人合法权益，不违法上传、存储并分享他人作品。投诉、举报、维权邮箱：<EMAIL>，或在此留言 </div></div></div>',1))]))}},Re=Y(Ie,[["__scopeId","data-v-69782924"]]);export{Re as default};
