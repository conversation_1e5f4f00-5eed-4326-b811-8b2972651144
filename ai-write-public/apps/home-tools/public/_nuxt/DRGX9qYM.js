const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DyyUxuRy.js","./B7yib2Q0.js","./entry.BM64DQIl.css","./vs2015.LLWOf3w5.css"])))=>i.map(i=>d[i]);
import{d as ce,r as f,j as le,o as ye,J as ie,af as pe,t as k,v as L,O as xe,L as p,x as n,K as re,ag as ee,ad as oe,M as fe,N as ge,y as D,ah as de,G as $e,u as we,D as Te,F as ke,z as ne,ai as he}from"./B7yib2Q0.js";import{_ as ue}from"./DlAUqK2U.js";import{u as _e,a as Le}from"./BvTBxqY-.js";import{u as Ce,b as Me}from"./D8PktKVn.js";const qe={key:0,class:"render-error"},Ie={key:1},Se=["innerHTML"],Ae={key:0,class:"typing-cursor"},Re=ce({__name:"index",props:{content:{},fontSize:{default:"base"},isTyping:{type:Boolean,default:!1}},setup(E){let v=null,S=null,C=null;const r=E,u=f(""),b=f(!1),g=new Map,x=f(""),y=f(!1);let A=null;const $={"☺":"☺️",":)":"😊",":-)":"😊",":(":"😢",":-(":"😢",":D":"😃",":-D":"😃",";)":"😉",";-)":"😉",":P":"😛",":-P":"😛",":p":"😛",":-p":"😛",":o":"😮",":-o":"😮",":O":"😱",":-O":"😱",":|":"😐",":-|":"😐",":*":"😘",":-*":"😘","<3":"❤️","</3":"💔","~":"～","。。。":"…","...":"…"},d=new Map,F=new Map;Object.entries($).forEach(([t,e])=>{if(t.match(/^[:\-\(\)\[\]<>3pPdDoO\|*]+$/))F.set(t,e);else if(t==="~")d.set(t,/~(?=\s|$)/g);else{const i=t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");d.set(t,new RegExp(i,"g"))}});const G=t=>{let e=t;return F.forEach((i,l)=>{e.includes(l)&&(e=e.split(l).join(i))}),d.forEach((i,l)=>{const w=$[l];e=e.replace(i,w)}),e},N=t=>{if(!t)return"";let e=t.trim();return e=e.replace(/(\n\s*){3,}(\|)/g,`

$2`),e=e.replace(/\s{10,}/g," "),e=e.replace(/\n{3,}/g,`

`).replace(/^\s*\n/gm,`
`).replace(/\n\s*$/gm,`
`).replace(/\n\s+\n/g,`

`).replace(/(\|.*\|)\n+/g,`$1
`).replace(/\n{2,}(\|)/g,`

$1`),e},T=le(()=>r.fontSize==="lg"?"text-base":r.fontSize==="base"?"text-sm":"text-xs"),Q=()=>{if(!window.mermaid)return;const t=window.mermaid;t.initialized||(t.initialize({startOnLoad:!1,theme:"default",securityLevel:"loose",fontFamily:"inherit"}),t.initialized=!0);const e=r.isTyping?".mermaid-container[data-mermaid]:not(.rendered):not(.rendering)":".mermaid-container[data-mermaid]:not(.rendered)",i=document.querySelectorAll(e);for(const l of i){const w=decodeURIComponent(l.getAttribute("data-mermaid")||""),m=l.id;r.isTyping&&l.classList.add("rendering"),l.classList.add("rendered");try{t.render(m,w).then(({svg:M})=>{l.innerHTML=M,r.isTyping&&l.classList.remove("rendering")}).catch(M=>{console.error("Mermaid render error:",M),l.innerHTML=`
          <div class="mermaid-error">
            <p>Failed to render diagram</p>
          </div>
        `,r.isTyping&&l.classList.remove("rendering")})}catch(M){console.error("Mermaid render error:",M),l.innerHTML=`
        <div class="mermaid-error">
          <p>Failed to render diagram</p>
        </div>
      `,r.isTyping&&l.classList.remove("rendering")}}},H=()=>{if(!window.katex)return;const t=window.katex,e=r.isTyping?".latex-block[data-latex]:not(.rendered):not(.rendering)":".latex-block[data-latex]:not(.rendered)",i=document.querySelectorAll(e);for(const l of i){const w=decodeURIComponent(l.getAttribute("data-latex")||"");r.isTyping&&l.classList.add("rendering"),l.classList.add("rendered");try{const m=t.renderToString(w.trim(),{displayMode:!0});l.innerHTML=m,r.isTyping&&l.classList.remove("rendering")}catch(m){console.error("LaTeX render error:",m),l.innerHTML=`
        <div class="latex-error">
          <p>LaTeX syntax error</p>
        </div>
      `,r.isTyping&&l.classList.remove("rendering")}}},O=async()=>{try{const t=await oe(()=>import("marked"),[],import.meta.url),e=await oe(()=>import("marked-highlight"),[],import.meta.url),i=await oe(()=>import("./DyyUxuRy.js"),__vite__mapDeps([0,1,2]),import.meta.url);return v=t.marked,S=e.markedHighlight,C=i.default,document.querySelector('link[href*="highlight.js"]')||await oe(()=>Promise.resolve({}),__vite__mapDeps([3]),import.meta.url),!0}catch(t){return console.error("Failed to load plugins:",t),!1}};let K=!1;const B=async()=>{try{if(!v&&!await O())return!1;if(K)return!0;v.use(S({langPrefix:"hljs language-",highlight(e,i){const l=C.getLanguage(i)?i:"plaintext";return C.highlight(e,{language:l}).value}})),v.setOptions({breaks:!0,gfm:!0,headerIds:!1,mangle:!1});const t=new v.Renderer;return t.code=(e,i)=>{if(i==="mermaid"){const m=`mermaid-${Math.random().toString(36).substring(2,11)}`;return`<div class="mermaid-container" data-mermaid="${encodeURIComponent(e.trim())}" id="${m}">
          <div class="mermaid-loading">Rendering diagram...</div>
        </div>`}if(i==="latex"||i==="tex")return`<div class="latex-block" data-latex="${encodeURIComponent(e.trim())}">
          <div class="latex-loading">Rendering LaTeX...</div>
        </div>`;const l=i||"plaintext",w=C.getLanguage(l)?C.highlight(e,{language:l}).value:C.highlightAuto(e).value;return`<pre class="hljs"><code class="hljs language-${l}">${w}</code></pre>`},v.setOptions({renderer:t}),K=!0,!0}catch(t){return console.error("Failed to setup marked plugins:",t),!1}},J=(t,e)=>`final_${btoa(encodeURIComponent(t)).slice(0,32)}`,j=async()=>{const t=r.content;if(r.isTyping)try{b.value=!1;let e=N(t);e=G(e),await B()?u.value=v.parse(e):u.value=R(e),ee(()=>{c()});return}catch(e){console.error("Failed to render typing content:",e),u.value=R(G(t));return}if(!y.value&&t!==x.value)try{y.value=!0,b.value=!1;const e=J(t,!1);if(g.has(e)){u.value=g.get(e),x.value=t,ee(()=>{c()});return}await U(t,e),x.value=t}catch(e){console.error("Failed to render content:",e),b.value=!0,u.value=r.content}finally{y.value=!1}},U=async(t,e)=>{let i=N(t);i=G(i);let l="";if(await B()?l=v.parse(i):l=R(i),!r.isTyping&&(g.set(e,l),g.size>100)){const w=g.keys().next().value;g.delete(w)}u.value=l,ee(()=>{c()})},c=()=>{const t=r.isTyping?10:0;window.requestIdleCallback&&!r.isTyping?window.requestIdleCallback(()=>{Q(),H()}):setTimeout(()=>{Q(),H()},t)},X=t=>{const e=/^\|(.+)\|\s*\n\|(\s*:?-+:?\s*\|)+\s*\n((\|.+\|\s*\n?)+)/gm;return t.replace(e,i=>{const l=i.trim().split(`
`).map(h=>h.trim());if(l.length<3)return i;const w=l[0],m=l[1],M=l.slice(2).filter(h=>h.trim()),q=w.split("|").map(h=>h.trim()).filter(h=>h),W=m.split("|").map(h=>h.trim()).filter(h=>h).map(h=>h.startsWith(":")&&h.endsWith(":")?"center":h.endsWith(":")?"right":"left"),Z=M.map(h=>h.split("|").map(V=>V.trim()).filter(V=>V)).filter(h=>h.length>0);let I='<div class="table-container overflow-x-auto my-6 rounded-lg border border-gray-200 shadow-sm">';return I+='<table class="min-w-full divide-y divide-gray-200">',q.length>0&&(I+='<thead class="bg-gray-50">',I+="<tr>",q.forEach((h,V)=>{const se=W[V]||"left";I+=`<th class="px-6 py-3 ${se==="center"?"text-center":se==="right"?"text-right":"text-left"} text-xs font-medium text-gray-500 uppercase tracking-wider">${h}</th>`}),I+="</tr>",I+="</thead>"),Z.length>0&&(I+='<tbody class="bg-white divide-y divide-gray-200">',Z.forEach((h,V)=>{const se=V%2===0?"bg-white":"bg-gray-50";I+=`<tr class="${se}">`,h.forEach((s,o)=>{const a=W[o]||"left";I+=`<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${a==="center"?"text-center":a==="right"?"text-right":"text-left"}">${s}</td>`}),I+="</tr>"}),I+="</tbody>"),I+="</table></div>",I})},R=t=>{let e=t;e=e.replace(/```(\w+)?\n?([\s\S]*?)```/g,(M,q,W)=>{if(q==="mermaid"){const Z=`mermaid-${Math.random().toString(36).substring(2,11)}`;return`<div class="mermaid-container" data-mermaid="${encodeURIComponent(W.trim())}" id="${Z}">
        <div class="mermaid-loading">Rendering diagram...</div>
      </div>`}return q==="latex"||q==="tex"?`<div class="latex-block" data-latex="${encodeURIComponent(W.trim())}">
        <div class="latex-loading">Rendering LaTeX...</div>
      </div>`:`<div class="code-block-wrapper my-4">
      <pre class="hljs bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
        <code class="hljs language-${q||"plaintext"}">${te(W.trim())}</code>
      </pre>
    </div>`}),e=e.replace(/`([^`\n]+)`/g,'<code class="inline-code bg-gray-100 text-gray-800 px-1.5 py-0.5 rounded text-sm font-mono">$1</code>'),e=e.replace(/^---+\s*$/gm,'<hr class="my-6 border-gray-300">'),e=e.replace(/^\*\*\*+\s*$/gm,'<hr class="my-6 border-gray-300">'),e=e.replace(/^#{6}\s+(.+)$/gm,'<h6 class="text-sm font-medium mt-4 mb-2 text-gray-700">$1</h6>'),e=e.replace(/^#{5}\s+(.+)$/gm,'<h5 class="text-base font-medium mt-4 mb-2 text-gray-700">$1</h5>'),e=e.replace(/^#{4}\s+(.+)$/gm,'<h4 class="text-lg font-medium mt-4 mb-2 text-gray-800">$1</h4>'),e=e.replace(/^#{3}\s+(.+)$/gm,'<h3 class="text-xl font-semibold mt-5 mb-3 text-gray-800">$1</h3>'),e=e.replace(/^#{2}\s+(.+)$/gm,'<h2 class="text-2xl font-semibold mt-6 mb-4 text-gray-900">$1</h2>'),e=e.replace(/^#{1}\s+(.+)$/gm,'<h1 class="text-3xl font-bold mt-8 mb-6 text-gray-900">$1</h1>'),e=e.replace(/^>\s*(.+)$/gm,(M,q)=>`<blockquote class="border-l-4 border-blue-400 pl-4 py-2 my-4 bg-blue-50 text-gray-700 italic">${q}</blockquote>`),e=e.replace(/\*\*\*(.+?)\*\*\*/g,'<strong class="font-bold"><em class="italic">$1</em></strong>'),e=e.replace(/___(.+?)___/g,'<strong class="font-bold"><em class="italic">$1</em></strong>'),e=e.replace(/\*\*(.+?)\*\*/g,'<strong class="font-semibold text-gray-900">$1</strong>'),e=e.replace(/__(.+?)__/g,'<strong class="font-semibold text-gray-900">$1</strong>'),e=e.replace(/\*(.+?)\*/g,'<em class="italic text-gray-700">$1</em>'),e=e.replace(/_(.+?)_/g,'<em class="italic text-gray-700">$1</em>'),e=e.replace(/~~(.+?)~~/g,'<del class="line-through text-gray-500 opacity-75">$1</del>'),e=e.replace(/\[([^\]]+)\]\(([^)]+)\)/g,'<a href="$2" class="text-blue-600 hover:text-blue-800 underline transition-colors" target="_blank" rel="noopener noreferrer">$1</a>'),e=e.replace(/!\[([^\]]*)\]\(([^)]+)\)/g,'<div class="image-container my-6 text-center"><img src="$2" alt="$1" class="max-w-full h-auto rounded-lg shadow-lg mx-auto"><div class="image-caption mt-2 text-sm text-gray-600 italic">$1</div></div>'),e=X(e),e=e.replace(/^\d+\.\s+(.+)$/gm,'<li class="mb-1">$1</li>'),e=e.replace(/^[\*\-\+]\s+(.+)$/gm,'<li class="mb-1">$1</li>'),e=e.replace(/(<li[^>]*>.*?<\/li>\s*)+/gs,M=>`<ul class="list-disc list-inside my-4 ml-6 space-y-1 text-gray-700">${M}</ul>`),e=e.replace(/(^|[^"])(https?:\/\/[^\s<>"]+)/g,'$1<a href="$2" class="text-blue-600 hover:text-blue-800 underline break-all" target="_blank" rel="noopener noreferrer">$2</a>');const i=e.split(`
`),l=[];let w=!1,m="";for(let M=0;M<i.length;M++){const q=i[M].trim();if(!q){w&&(l.push(`<p class="text-base leading-7 my-4 text-gray-700">${m.trim()}</p>`),m="",w=!1);continue}if(q.match(/^<(h[1-6]|div|blockquote|ul|ol|li|hr|pre|table|img)/)){w&&(l.push(`<p class="text-base leading-7 my-4 text-gray-700">${m.trim()}</p>`),m="",w=!1),l.push(q);continue}w?m+=" "+q:(m=q,w=!0)}return w&&m.trim()&&l.push(`<p class="text-base leading-7 my-4 text-gray-700">${m.trim()}</p>`),l.join(`
`)},te=t=>{const e=document.createElement("div");return e.textContent=t,e.innerHTML},P=()=>{A&&clearTimeout(A);const t=r.isTyping?10:50;A=setTimeout(()=>{j()},t)};return ye(async()=>{r.content&&await j()}),ie(()=>r.content,(t,e)=>{t!==e&&(r.isTyping?j():P())},{flush:"post",immediate:!1}),ie(()=>r.isTyping,(t,e)=>{e&&!t&&(x.value="",j())}),pe(()=>{A&&clearTimeout(A),g.clear()}),(t,e)=>(L(),k("div",{class:xe(`ai-response-container max-w-4xl ${p(T)}`)},[p(b)?(L(),k("div",qe," 渲染内容时发生错误，请检查内容格式。 ")):(L(),k("div",Ie,[n("div",{innerHTML:p(u)},null,8,Se),t.isTyping?(L(),k("span",Ae)):re("",!0)]))],2))}}),me=ue(Re,[["__scopeId","data-v-352912df"]]);function De(E){if(E===0)return"0 B";const v=1024,S=["B","KB","MB","GB"],C=Math.floor(Math.log(E)/Math.log(v));return parseFloat((E/Math.pow(v,C)).toFixed(2))+" "+S[C]}function Ee(E){switch(E){case"image":return"🖼️";case"document":return"📄";case"audio":return"🎵";case"video":return"🎬";default:return"📎"}}const He={key:0,class:"message-attachments mt-3"},Be={class:"attachments-list"},je={class:"attachment-icon"},ze={class:"attachment-info"},Fe=["title"],Ne={class:"attachment-meta"},Oe={class:"attachment-size"},Pe=["onClick"],Ue=ce({__name:"index",props:{attachments:{default:()=>[]}},setup(E){const v=S=>{window.open(S,"_blank")};return(S,C)=>S.attachments&&S.attachments.length>0?(L(),k("div",He,[n("div",Be,[(L(!0),k(fe,null,ge(S.attachments,(r,u)=>(L(),k("div",{key:u,class:"attachment-item"},[n("div",je,D(p(Ee)(r.type)),1),n("div",ze,[n("span",{class:"attachment-name",title:r.filename},D(r.filename),9,Fe),n("span",Ne,[n("span",Oe,D(p(De)(r.size)),1)])]),n("div",{class:"attachment-action",onClick:b=>v(r.url)},C[0]||(C[0]=[n("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[n("path",{d:"M3 19H21V21H3V19ZM13 13.1716L19.0711 7.1005L20.4853 8.51472L12 17L3.51472 8.51472L4.92893 7.1005L11 13.1716V2H13V13.1716Z"})],-1)]),8,Pe)]))),128))])])):re("",!0)}}),ve=ue(Ue,[["__scopeId","data-v-c2d62fc2"]]),Ve={key:0,class:"seo-qa-content","aria-hidden":"true",style:{position:"absolute",left:"-9999px",width:"1px",height:"1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)","white-space":"nowrap"}},Ge={class:"qa-conversation"},Qe={class:"qa-list"},Ke={key:0,class:"question"},Xe={key:1,class:"answer"},We=["innerHTML"],Je={class:"seo-metadata"},Ze=ce({__name:"SeoQaList",props:{caseId:{},caseTitle:{default:"案例对话"},qaData:{default:()=>[]}},setup(E){const v=E,S=r=>r?r.includes("<")&&r.includes(">")?r:r.replace(/\n/g,"<br>"):"",C=le(()=>!v.qaData||v.qaData.length===0?null:{"@context":"https://schema.org","@type":"QAPage",mainEntity:v.qaData.map((r,u)=>({"@type":"Question",name:r.query||`问题 ${u+1}`,text:r.query||`问题 ${u+1}`,answerCount:1,acceptedAnswer:{"@type":"Answer",text:r.answer||"暂无回答",author:{"@type":"Organization",name:"梅斯小智"}}}))});return _e({script:[{type:"application/ld+json",innerHTML:()=>C.value?JSON.stringify(C.value):""}]}),(r,u)=>r.qaData&&r.qaData.length>0?(L(),k("div",Ve,[n("div",Ge,[n("h1",null,D(r.caseTitle||"案例对话"),1),n("div",Qe,[(L(!0),k(fe,null,ge(r.qaData,(b,g)=>(L(),k("div",{key:`qa-${g}`,class:"qa-item"},[b.query?(L(),k("div",Ke,[n("h2",null,D(`问题 ${g+1}`),1),n("p",null,D(b.query),1)])):re("",!0),b.answer?(L(),k("div",Xe,[n("h3",null,D(`回答 ${g+1}`),1),n("div",{innerHTML:S(b.answer)},null,8,We)])):re("",!0)]))),128))]),n("div",Je,[n("p",null,"案例ID: "+D(r.caseId),1),u[0]||(u[0]=n("p",null,"医学AI智能对话，专业医疗问答，梅斯小智",-1)),u[1]||(u[1]=n("p",null,"关键词: 医学AI, 智能问答, 医疗咨询, 案例分析, 梅斯医学",-1))])])])):re("",!0)}}),Ye=ue(Ze,[["__scopeId","data-v-901223f0"]]);function et(E={}){const{bottomThreshold:v=50,scrollDuration:S=300,scrollBehavior:C="smooth",userScrollDebounce:r=150,debug:u=!1}=E,b=f(!0),g=f(!1),x=f(0),y=f(null);let A=null,$=null;const d=(...c)=>{u&&[...c]},F=()=>{if(!y.value)return!1;const c=y.value,X=c.scrollTop,R=c.scrollHeight,te=c.clientHeight,P=R-X-te,t=P<=v;return d("距离底部:",P,"是否接近:",t),t},G=(c=!1)=>{if(!y.value||!b.value&&!c)return;const X=y.value;d("滚动到底部, 强制:",c),X.scrollTo({top:X.scrollHeight,behavior:C})},N=()=>{if(!y.value)return;const c=y.value.scrollTop;Math.abs(c-x.value)>5&&(g.value=!0,c<x.value&&(b.value=!1,d("用户向上滚动，禁用自动滚动")),A&&clearTimeout(A),A=setTimeout(()=>{g.value=!1,F()&&(b.value=!0,d("用户滚动到底部，恢复自动滚动"))},r)),x.value=c},T=()=>{if(y.value){if(g.value){d("用户正在滚动，延迟自动滚动"),$&&clearTimeout($),$=setTimeout(T,100);return}if(!b.value)if(F())b.value=!0,d("检测到接近底部，恢复自动滚动");else return;G()}},Q=()=>{d("触摸开始"),g.value=!0},H=()=>{d("触摸结束"),setTimeout(()=>{g.value=!1,F()&&(b.value=!0,d("触摸结束后检测到底部，恢复自动滚动"))},r)},O=c=>{c.deltaY!==0&&(d("鼠标滚轮滚动"),N())},K=c=>{y.value&&B(),y.value=c,x.value=c.scrollTop,c.addEventListener("scroll",N,{passive:!0}),c.addEventListener("wheel",O,{passive:!0}),c.addEventListener("touchstart",Q,{passive:!0}),c.addEventListener("touchend",H,{passive:!0}),d("初始化滚动容器")},B=()=>{if(!y.value)return;const c=y.value;c.removeEventListener("scroll",N),c.removeEventListener("wheel",O),c.removeEventListener("touchstart",Q),c.removeEventListener("touchend",H),d("移除事件监听器")},J=()=>{b.value=!0,g.value=!1,d("强制启用自动滚动")},j=()=>{b.value=!1,d("禁用自动滚动")},U=()=>{B(),A&&(clearTimeout(A),A=null),$&&(clearTimeout($),$=null),d("清理资源")};return pe(()=>{U()}),{isAutoScrollEnabled:de(b),isUserScrolling:de(g),initScrollContainer:K,smartScroll:T,scrollToBottom:G,enableAutoScroll:J,disableAutoScroll:j,isNearBottom:F,cleanup:U,scrollContainer:de(y)}}const tt={class:"relative h-screen",style:{backgroundColor:"var(--bg-main)"}},st={class:"h-screen flex flex-col"},at={class:"md:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 px-4 py-3 shadow-sm"},nt={class:"flex items-center"},rt={class:"flex items-center flex-1"},lt={class:"w-8 h-8 bg-gradient-to-r rounded-full flex items-center justify-center mr-3"},ot=["href"],it=["src","alt"],ct={class:"text-lg font-semibold text-gray-800"},ut=["title"],dt={class:"hidden md:block bg-white border-b border-gray-200 px-6 py-4 shadow-sm"},mt={class:"max-w-4xl mx-auto"},pt={class:"flex items-center justify-between"},ft={class:"flex items-center space-x-3 w-[90%]"},gt={class:"flex flex-col items-center"},ht={class:"w-10 h-10 bg-gradient-to-r rounded-full flex items-center justify-center text-white font-medium mb-1"},vt=["href"],yt=["src","alt"],_t={class:"text-xs text-gray-600 font-medium text-center block w-full"},bt={class:"text-lg font-semibold text-gray-800"},xt={key:0,class:"text-sm"},$t={class:"text-blue-600 font-medium"},wt=["title"],Tt={class:"max-w-4xl mx-auto"},kt={key:0,class:"ml-auto max-w-2xl bg-gray-0 border border-gray-200 rounded-2xl rounded-br-md p-4 shadow-sm hover:shadow-md transition-all duration-200"},Lt={class:"text-gray-800 text-base leading-relaxed"},Ct={key:1,class:"mr-auto max-w-4xl"},Mt={class:"text-base text-gray-800 leading-relaxed prose prose-base max-w-none"},qt={key:0},It={key:1},St=ce({__name:"[caseId]",props:{currentAppUuid:{default:""}},async setup(E){let v,S;const C=$e(),r=we(),{t:u,locale:b}=Te(),g=le(()=>C.params.caseId),x=f([]),y=f(!1),A=f(!1),$=f(u("case.title")),d=f(!1),F=f(!1);f(null);const G=f(),N=f(),T=f(""),Q=f(!0),H=f([]),O=f(!1),K=f(0),B=f([]),J=f({}),j=f({}),{smartScroll:U,initScrollContainer:c,enableAutoScroll:X}=et({bottomThreshold:50,scrollBehavior:"smooth",userScrollDebounce:150,debug:!1}),R=f([]),te=f(""),P=f(b.value),{data:t}=([v,S]=ke(async()=>Ce(`case-seo-${g.value}`,async()=>{var s;try{const o=Me();g.value;const a=await he({articleId:"",encryptionId:g.value},o);if(a&&Array.isArray(a)&&a.length>0){const _=a[0];let z=[],ae="";if(_&&_.question&&(ae=_.question),T.value=_.userName,T.value,_&&_.answer){let Y=_.answer;if(typeof Y=="string")try{Y=JSON.parse(Y),Q.value=((s=Y[0])==null?void 0:s.demo_case)||!0}catch(be){return console.error("解析answer数据失败:",be),{qaItems:[],title:""}}Array.isArray(Y)&&Y.length>0&&(z=Y)}return z.length,{qaItems:z,title:ae}}return{qaItems:[],title:""}}catch(o){return console.error("服务端预取SEO数据失败:",o),{qaItems:[],title:""}}},{server:!0,default:()=>({qaItems:[],title:""})})),v=await v,S(),v);t.value&&(R.value=t.value.qaItems||[],te.value=t.value.title||"",t.value.title&&($.value=t.value.title));const e=()=>{r.back()},i=()=>{H.value.forEach(s=>clearTimeout(s)),H.value=[]},l=()=>{const s=P.value?`/${P.value=="zh-CN"?"zh":P.value}`:"",o=T.value||"";return`https://ai.medon.com.cn${s}/${o.replace(" ","-").toLowerCase()}`},w=(s,o)=>{J.value[s]="",j.value[s]=!0,X();let a=0;const _=0,z=()=>{if(a<o.length){J.value[s]+=o[a],a++,a%5===0&&ee(()=>{U()});const ae=setTimeout(z,_);H.value.push(ae)}else{j.value[s]=!1,ee(()=>{U()});const ae=setTimeout(()=>{V(s)},0);H.value.push(ae)}};z()},m=()=>{setTimeout(()=>{r.push(P.value?`/${P.value}`:"/")},3e3)},M=async()=>{if(!O.value){O.value=!0;try{const s=await he({articleId:"",encryptionId:g.value});if(s&&Array.isArray(s)&&s.length>0){A.value=!0;const o=s[0];o&&o.question&&($.value=o.question),q(s)}else m()}catch{m()}finally{O.value=!1}}},q=s=>{if(y.value)return;i(),x.value=[],y.value=!0;let o=[];try{if(s&&Array.isArray(s)&&s.length>0){const a=s[0];if(a&&a.answer){let _=a.answer;if(typeof _=="string")try{_=JSON.parse(_)}catch{m();return}if(Array.isArray(_)&&_.length>0)o=_;else{m();return}}else{m();return}}else{m();return}if(o.length===0){m();return}}catch{m();return}W(o)},W=s=>{y.value=!0;const o=[];s.forEach((a,_)=>{if(a.query&&a.query.trim()){const z={id:`user_${_}_${Date.now()}`,type:"user",content:a.query.trim().includes("【--Final content--】")?a.query.trim().split("【--Final content--】")[1]:a.query.trim(),timestamp:new Date,message_files:a.message_files||[]};o.push(z)}if(a.answer&&a.answer.trim()){const z={id:`assistant_${_}_${Date.now()}`,type:"assistant",content:a.answer.trim().includes("【--Final content--】")?a.answer.trim().split("【--Final content--】")[1]:a.answer.trim(),timestamp:new Date,isGenerating:!0};o.push(z)}}),Z(o)},Z=s=>{if(s.length===0){y.value=!1;return}K.value=0,B.value=s,x.value=[],I()},I=()=>{const s=K.value,o=B.value;if(s>=o.length){y.value=!1;return}const a=o[s];if(K.value++,x.value=[...x.value,a],ee(()=>{U()}),a.type==="user"){const _=setTimeout(()=>{I()},300);H.value.push(_)}else a.type==="assistant"&&!d.value&&w(a.id,a.content)},h=()=>{if(d.value)d.value=!1,B.value.length>0&&Z(B.value);else if(d.value=!0,F.value=!0,i(),y.value=!1,B.value.length>0){const s=B.value.map(o=>({...o,isGenerating:!1}));x.value=s}},V=s=>{x.value=x.value.map(a=>a.id===s?{...a,isGenerating:!1}:a);const o=setTimeout(()=>{I()},300);H.value.push(o)};ie(()=>g.value,s=>{if(!s){m();return}O.value||A.value&&x.value.length>0||(i(),y.value=!1,x.value=[],A.value=!1,d.value=!1,F.value=!1,M())},{immediate:!0}),ie(()=>x.value.length,()=>{ee(()=>{U()})}),ye(()=>{N.value&&c(N.value)}),pe(()=>{i(),O.value=!1}),le(()=>{var s;if(R.value&&R.value.length>0){const o=((s=R.value[0])==null?void 0:s.query)||"",a=u("case.description");return o?`${o} - ${a} - 梅斯小智医学AI智能对话案例`:`${a} - 梅斯小智医学AI智能对话案例`}return`${u("case.description")} - 梅斯小智医学AI智能对话案例`});const se=le(()=>{const s=`${u("case.caseAnalysis")},${u("case.medicalConsultation")},${u("case.intelligentQA")}`;if(R.value&&R.value.length>0){const a=R.value.map(_=>_.query).filter(Boolean).slice(0,1).join(", ");return a?`${a}, ${s}`:s}return s});return Le({title:()=>`${$.value} - ${T.value} ${u("case.intelligentDialogue")}`,description:()=>`${$.value}`,keywords:()=>se.value,ogTitle:()=>`${$.value} - ${T.value} ${u("case.intelligentDialogue")}`,ogDescription:()=>`${$.value}`,ogType:"article",ogUrl:()=>`https://ai.medon.com.cn/cases/${g.value}`,twitterCard:"summary",twitterTitle:`${$.value} - ${T.value} ${u("case.intelligentDialogue")}`,twitterDescription:()=>`${$.value}`}),_e({title:()=>`${$.value} - ${T.value} ${u("case.intelligentDialogue")}`}),(s,o)=>(L(),k("div",tt,[n("div",st,[n("div",at,[n("div",nt,[n("button",{onClick:e,class:"flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 transition-colors mr-3"},o[0]||(o[0]=[n("svg",{class:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[n("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),n("div",rt,[n("div",lt,[n("a",{href:l()},[n("img",{src:p(T)=="Novax Base"?"https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png":p(T).value=="ElavaX Base"?"https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png":"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png",alt:p(T),class:"w-5 h-5 rounded-full"},null,8,it)],8,ot)]),n("span",ct,D(p(T)),1)]),n("button",{onClick:h,class:"flex items-center justify-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-xl transition-colors duration-200",title:p(d)?s.$t("case.viewReplay"):s.$t("case.viewResults")},[n("span",null,D(p(d)?s.$t("case.viewReplay"):s.$t("case.viewResults")),1)],8,ut)])]),n("div",dt,[n("div",mt,[n("div",pt,[n("div",ft,[n("div",gt,[n("div",ht,[n("a",{href:l()},[n("img",{src:p(T)=="Novax Base"?"https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png":p(T).value=="ElavaX Base"?"https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png":"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png",alt:p(T),class:"w-6 h-6 rounded-full"},null,8,yt)],8,vt)]),n("span",_t,D(p(T)),1)]),n("div",null,[n("h1",bt,D(p($)),1),p(Q)?(L(),k("p",xt,[n("span",$t,D(s.$t("case.demo_case")),1)])):re("",!0)])]),n("button",{onClick:h,class:"flex items-center justify-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-xl transition-colors duration-200 shadow-sm hover:shadow-md",title:p(d)?s.$t("case.viewReplay"):s.$t("case.viewResults")},[n("span",null,D(p(d)?s.$t("case.viewReplay"):s.$t("case.viewResults")),1)],8,wt)])])]),n("div",{ref_key:"messagesContainer",ref:N,class:"flex-1 px-4 py-6 pb-48 overflow-y-auto bg-gray-50 pt-16 md:pt-6"},[n("div",Tt,[(L(!0),k(fe,null,ge(p(x),a=>(L(),k("div",{key:a.id,class:"mx-auto max-w-4xl relative group mb-6"},[a.type==="user"?(L(),k("div",kt,[n("div",Lt,[ne(me,{content:a.content,"font-size":"base"},null,8,["content"])]),ne(ve,{attachments:a.message_files},null,8,["attachments"])])):(L(),k("div",Ct,[n("div",Mt,[a.isGenerating&&!p(d)&&p(j)[a.id]?(L(),k("div",qt,[ne(me,{content:(p(J)[a.id]||"")+(p(j)[a.id]?"▊":""),"font-size":"lg","is-typing":!0},null,8,["content"])])):(L(),k("div",It,[ne(me,{content:a.content,"font-size":"lg"},null,8,["content"])]))]),ne(ve,{attachments:a.message_files},null,8,["attachments"])]))]))),128)),n("div",{ref_key:"messagesEndRef",ref:G},null,512)])],512)]),ne(Ye,{"case-id":p(g),"case-title":p(te)||p($),"qa-data":p(R)},null,8,["case-id","case-title","qa-data"])]))}}),Ht=ue(St,[["__scopeId","data-v-90f26e8a"]]);export{Ht as default};
