import axios from 'axios';
import cookie from 'js-cookie';
import { E as parseCookies } from '../_/nitro.mjs';
import { g as useCookie, i as useRequestEvent } from './server.mjs';

const domainMap = {
  ".medsci.cn": ".medsci.cn",
  ".medon.com.cn": ".medon.com.cn",
  "localhost": "localhost"
};
const Cookies = {
  /**
   * 设置带有 domain 和 expires 的 Cookie
   * @param name Cookie 名称
   * @param value Cookie 值（自动 JSON 序列化）
   * @param options 额外配置项
   */
  set: (name, value, options = {}) => {
    const { expires = 365, domain, ...restOptions } = options;
    let finalDomain = domain;
    if (!finalDomain) {
      const matchedDomain = Object.keys(domainMap).find((d) => (void 0).origin.includes(d));
      if (matchedDomain) {
        finalDomain = domainMap[matchedDomain];
      }
    }
    const finalOptions = {
      expires,
      ...finalDomain ? { domain: finalDomain } : {},
      ...restOptions
    };
    cookie.set(name, value, finalOptions);
  },
  /**
   * 获取 Cookie 并自动反序列化
   * @param name Cookie 名称
   * @returns Cookie 值
   */
  get: (name) => {
    const value = cookie.get(name);
    try {
      return value ? value : null;
    } catch {
      return value;
    }
  }
};

const serverRequestWrapper = async (config, event) => {
  if (!event) {
    throw new Error("Server request requires an event object");
  }
  return serverRequest(config, event);
};
const getParameters = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/v1/parameters`, method: "get", data },
    event
  );
};
const mainLogin = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/getAiWriteToken`, method: "post", data },
    event
  );
};
const getAppPrompt = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/appParams`, method: "post", data },
    event
  );
};
const getAppList = (data, event) => {
  return serverRequestWrapper(
    {
      url: `/ai-base/index/getAppList`,
      method: "post",
      data
    },
    event
  );
};
const getAppTypes = (languages, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/getAppTypes`, method: "get" },
    event
  );
};
const getAppLangs = (event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/getAppLangs`, method: "get" },
    event
  );
};
const getConfigPage = (languages, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/getConfigPage`, method: "get" },
    event
  );
};
const getAppClickNum = (appUuid, openid, event) => {
  return serverRequestWrapper(
    {
      url: `/ai-base/index/getAppClickNum?appUuid=${appUuid}&openid=${openid}`,
      method: "get"
    },
    event
  );
};
const createSubscription = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/appUser/createSubscription`, method: "post", data },
    event
  );
};
const login = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/login`, method: "post", data },
    event
  );
};
const register = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/register`, method: "post", data },
    event
  );
};
const sendEmailCode = (data, event) => {
  return serverRequestWrapper(
    {
      url: `/ai-base/index/sendEmailCode?email=${data.email}&type=${data.type}`,
      method: "post"
    },
    event
  );
};
const createAliSub = (data, event) => {
  return serverRequestWrapper(
    {
      url: `/ai-base/index/createAliSub?piId=${data.piId}&socialUserId=${data.socialUserId}&openid=${data.openid}&sessionId=${data.sessionId}`,
      method: "get"
    },
    event
  );
};
const upload = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/v1/files/upload`, method: "post", data },
    event
  );
};
const getAppByUuid = (appUuid, event) => {
  return serverRequestWrapper(
    {
      url: `/ai-base/index/getAppByUuid?appUuid=${appUuid}`,
      method: "get",
      noLoading: true
    },
    event
  );
};
const getArticleWithSEO = (id, event) => {
  return serverRequestWrapper(
    {
      url: `/ai-base/index/snapshot/getArticleWithSEO?id=${id}`,
      method: "get",
      noLoading: true
    },
    event
  );
};
const freeLimit = (event) => {
  return serverRequestWrapper(
    {
      url: `/ai-base/index/free-limit`,
      method: "get",
      noLoading: true
    },
    event
  );
};
const getPackageByKey = (event) => {
  return serverRequestWrapper(
    {
      url: `/ai-base/index/getPackageByKey`,
      method: "get",
      noLoading: true
    },
    event
  );
};
const cancelSubscription = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/appUser/cancelSubscription?appUuid=`, method: "post", data },
    event
  );
};
const qaList = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/qa-list`, method: "post", data },
    event
  );
};

const userId = "";
let baseURL = "https://ai.medon.com.cn";
{
  baseURL = "https://ai.medon.com.cn/dev-api";
}
const service = axios.create({
  baseURL,
  timeout: 1e3 * 60 * 2,
  // 2 分钟超时
  headers: { "Content-Type": "application/json;charset=utf-8" }
});
service.interceptors.request.use(
  (config) => {
    if (config.url.includes("v1/files/upload")) {
      config.headers["Content-Type"] = "multipart/form-data";
    }
    const deviceId = null;
    const token = Cookies.get("yudaoToken") || null;
    config.headers["Visitor-Code"] = deviceId;
    config.headers["User-Id"] = userId;
    if (config.url !== "/dev-api/ai-base/index/getAiWriteToken" && token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    if (config.method === "get") {
      config.params = config.data;
    }
    return config;
  },
  (error) => {
    console.error("Request Error:", error);
    return Promise.reject(error);
  }
);
service.interceptors.response.use(
  (response) => {
    const { status, data } = response;
    if (status === 200) {
      const code = data.code;
      switch (code) {
        case 0:
          if (data.data) {
            return Promise.resolve(data.data);
          } else {
            return Promise.reject("访问太火爆了！请稍后再试~");
          }
        case 401:
          return Promise.reject("未授权，请重新登录");
        default:
          console.error("Response Error:", data, code);
          return Promise.reject(data);
      }
    } else {
      return Promise.reject("访问太火爆了！请稍后再试~");
    }
  },
  (error) => {
    console.error("Response Error:", error);
    return Promise.reject(error);
  }
);
const serverRequest = async (config, event) => {
  if (!event) {
    throw new Error("Server request requires an event object");
  }
  const cookies2 = parseCookies(event);
  const userInfo = cookies2.userInfo ? JSON.parse(cookies2.userInfo) : {};
  const token = cookies2.yudaoToken || null;
  config.headers = config.headers || {};
  config.url = config.url.includes("?") ? config.url + "&locale=" + cookies2.ai_apps_lang : config.url + "?locale=" + cookies2.ai_apps_lang;
  if (!config.url.includes("getAiWriteToken")) {
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    } else {
      const yudaoToken = useCookie("yudaoToken", { domain: ".medon.com.cn", maxAge: 30 * 24 * 60 * 60 * 12 });
      const usericon = useCookie("userInfo");
      const event2 = useRequestEvent();
      const userInfoStr = JSON.stringify(usericon.value);
      if (userInfoStr) {
        const userInfo2 = JSON.parse(userInfoStr);
        const res = await mainLogin({
          userId: userInfo2.userId,
          userName: userInfo2.userName,
          realName: userInfo2.realName,
          avatar: userInfo2.avatar,
          plaintextUserId: userInfo2.plaintextUserId,
          mobile: userInfo2.mobile,
          email: userInfo2.email
        }, event2);
        yudaoToken.value = res == null ? void 0 : res.token;
        config.headers["Authorization"] = `Bearer ${res == null ? void 0 : res.token}`;
      }
    }
  }
  const serverUserId = (userInfo == null ? void 0 : userInfo.userId) || "";
  config.headers["User-Id"] = serverUserId;
  return service.request(config);
};
service.request;

export { Cookies as C, getPackageByKey as a, getAppList as b, getAppTypes as c, getAppClickNum as d, createSubscription as e, getArticleWithSEO as f, getAppLangs as g, getAppByUuid as h, getParameters as i, getAppPrompt as j, cancelSubscription as k, login as l, mainLogin as m, createAliSub as n, freeLimit as o, getConfigPage as p, qaList as q, register as r, sendEmailCode as s, upload as u };
//# sourceMappingURL=requestDify.mjs.map
