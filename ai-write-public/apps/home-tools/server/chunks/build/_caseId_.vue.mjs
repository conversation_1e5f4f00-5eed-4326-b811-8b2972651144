import { defineComponent, ref, computed, watch, mergeProps, unref, useSSRContext, readonly, withAsyncContext, nextTick } from 'vue';
import { ssrRender<PERSON>ttrs, ssrRenderList, ssrInterpolate, ssrRenderAttr, ssrRenderComponent } from 'vue/server-renderer';
import { _ as _export_sfc } from './_plugin-vue_export-helper.mjs';
import { u as useHead, a as useSeoMeta } from './v3.mjs';
import { q as qaList } from './requestDify.mjs';
import { f as useRoute, u as useRouter, e as useI18n, i as useRequestEvent } from './server.mjs';
import { u as useAsyncData } from './asyncData.mjs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import 'consola';
import 'node:path';
import 'node:crypto';
import 'unhead/server';
import 'unhead/plugins';
import 'unhead/utils';
import 'devalue';
import 'axios';
import 'js-cookie';
import 'vue-router';
import 'element-plus';

const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  props: {
    content: {},
    fontSize: { default: "base" },
    isTyping: { type: Boolean, default: false }
  },
  setup(__props) {
    const props = __props;
    const renderedContent = ref("");
    const hasError = ref(false);
    const lastProcessedContent = ref("");
    ref(false);
    let renderTimeout = null;
    const emojiMap = {
      "☺": "☺️",
      ":)": "😊",
      ":-)": "😊",
      ":(": "😢",
      ":-(": "😢",
      ":D": "😃",
      ":-D": "😃",
      ";)": "😉",
      ";-)": "😉",
      ":P": "😛",
      ":-P": "😛",
      ":p": "😛",
      ":-p": "😛",
      ":o": "😮",
      ":-o": "😮",
      ":O": "😱",
      ":-O": "😱",
      ":|": "😐",
      ":-|": "😐",
      ":*": "😘",
      ":-*": "😘",
      "<3": "❤️",
      "</3": "💔",
      "~": "～",
      "。。。": "…",
      "...": "…"
    };
    const emojiRegexMap = /* @__PURE__ */ new Map();
    const emojiSplitMap = /* @__PURE__ */ new Map();
    Object.entries(emojiMap).forEach(([textEmoji, emoji]) => {
      if (textEmoji.match(/^[:\-\(\)\[\]<>3pPdDoO\|*]+$/)) {
        emojiSplitMap.set(textEmoji, emoji);
      } else if (textEmoji === "~") {
        emojiRegexMap.set(textEmoji, /~(?=\s|$)/g);
      } else {
        const escapedText = textEmoji.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
        emojiRegexMap.set(textEmoji, new RegExp(escapedText, "g"));
      }
    });
    const fontSizeClass = computed(() => {
      return props.fontSize === "lg" ? "text-base" : props.fontSize === "base" ? "text-sm" : "text-xs";
    });
    const renderContent = async () => {
      {
        renderedContent.value = props.content;
        return;
      }
    };
    const debouncedRender = () => {
      if (renderTimeout) {
        clearTimeout(renderTimeout);
      }
      const delay = props.isTyping ? 10 : 50;
      renderTimeout = setTimeout(() => {
        renderContent();
      }, delay);
    };
    watch(() => props.content, (newContent, oldContent) => {
      if (newContent === oldContent) return;
      if (props.isTyping) {
        renderContent();
      } else {
        debouncedRender();
      }
    }, {
      flush: "post",
      // 对于打字机效果，使用同步更新以确保实时性
      immediate: false
    });
    watch(() => props.isTyping, (newIsTyping, oldIsTyping) => {
      if (oldIsTyping && !newIsTyping) {
        lastProcessedContent.value = "";
        renderContent();
      }
    });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({
        class: `ai-response-container max-w-4xl ${unref(fontSizeClass)}`
      }, _attrs))} data-v-352912df>`);
      if (unref(hasError)) {
        _push(`<div class="render-error" data-v-352912df> 渲染内容时发生错误，请检查内容格式。 </div>`);
      } else {
        _push(`<div data-v-352912df><div data-v-352912df>${unref(renderedContent) ?? ""}</div>`);
        if (_ctx.isTyping) {
          _push(`<span class="typing-cursor" data-v-352912df></span>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
      }
      _push(`</div>`);
    };
  }
});

const _sfc_setup$3 = _sfc_main$3.setup;
_sfc_main$3.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/AiResponseRenderer/index.vue");
  return _sfc_setup$3 ? _sfc_setup$3(props, ctx) : void 0;
};
const AiResponseRenderer = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["__scopeId", "data-v-352912df"]]);

function formatFileSize(bytes) {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}
function getFileIcon(type) {
  switch (type) {
    case "image":
      return "🖼️";
    case "document":
      return "📄";
    case "audio":
      return "🎵";
    case "video":
      return "🎬";
    default:
      return "📎";
  }
}

const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  props: {
    attachments: { default: () => [] }
  },
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      if (_ctx.attachments && _ctx.attachments.length > 0) {
        _push(`<div${ssrRenderAttrs(mergeProps({ class: "message-attachments mt-3" }, _attrs))} data-v-c2d62fc2><div class="attachments-list" data-v-c2d62fc2><!--[-->`);
        ssrRenderList(_ctx.attachments, (attachment, index) => {
          _push(`<div class="attachment-item" data-v-c2d62fc2><div class="attachment-icon" data-v-c2d62fc2>${ssrInterpolate(unref(getFileIcon)(attachment.type))}</div><div class="attachment-info" data-v-c2d62fc2><span class="attachment-name"${ssrRenderAttr("title", attachment.filename)} data-v-c2d62fc2>${ssrInterpolate(attachment.filename)}</span><span class="attachment-meta" data-v-c2d62fc2><span class="attachment-size" data-v-c2d62fc2>${ssrInterpolate(unref(formatFileSize)(attachment.size))}</span></span></div><div class="attachment-action" data-v-c2d62fc2><svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-c2d62fc2><path d="M3 19H21V21H3V19ZM13 13.1716L19.0711 7.1005L20.4853 8.51472L12 17L3.51472 8.51472L4.92893 7.1005L11 13.1716V2H13V13.1716Z" data-v-c2d62fc2></path></svg></div></div>`);
        });
        _push(`<!--]--></div></div>`);
      } else {
        _push(`<!---->`);
      }
    };
  }
});

const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/MessageAttachments/index.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const MessageAttachments = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["__scopeId", "data-v-c2d62fc2"]]);

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "SeoQaList",
  __ssrInlineRender: true,
  props: {
    caseId: {},
    caseTitle: { default: "案例对话" },
    qaData: { default: () => [] }
  },
  setup(__props) {
    const props = __props;
    const formatAnswer = (answer) => {
      if (!answer) return "";
      if (answer.includes("<") && answer.includes(">")) {
        return answer;
      }
      return answer.replace(/\n/g, "<br>");
    };
    const structuredData = computed(() => {
      if (!props.qaData || props.qaData.length === 0) return null;
      return {
        "@context": "https://schema.org",
        "@type": "QAPage",
        "mainEntity": props.qaData.map((item, index) => ({
          "@type": "Question",
          "name": item.query || `问题 ${index + 1}`,
          "text": item.query || `问题 ${index + 1}`,
          "answerCount": 1,
          "acceptedAnswer": {
            "@type": "Answer",
            "text": item.answer || "暂无回答",
            "author": {
              "@type": "Organization",
              "name": "梅斯小智"
            }
          }
        }))
      };
    });
    useHead({
      script: [
        {
          type: "application/ld+json",
          innerHTML: () => structuredData.value ? JSON.stringify(structuredData.value) : ""
        }
      ]
    });
    return (_ctx, _push, _parent, _attrs) => {
      if (_ctx.qaData && _ctx.qaData.length > 0) {
        _push(`<div${ssrRenderAttrs(mergeProps({
          class: "seo-qa-content",
          "aria-hidden": "true",
          style: { "position": "absolute", "left": "-9999px", "width": "1px", "height": "1px", "overflow": "hidden", "clip": "rect(0, 0, 0, 0)", "white-space": "nowrap" }
        }, _attrs))} data-v-901223f0><div class="qa-conversation" data-v-901223f0><h1 data-v-901223f0>${ssrInterpolate(_ctx.caseTitle || "案例对话")}</h1><div class="qa-list" data-v-901223f0><!--[-->`);
        ssrRenderList(_ctx.qaData, (item, index) => {
          _push(`<div class="qa-item" data-v-901223f0>`);
          if (item.query) {
            _push(`<div class="question" data-v-901223f0><h2 data-v-901223f0>${ssrInterpolate(`问题 ${index + 1}`)}</h2><p data-v-901223f0>${ssrInterpolate(item.query)}</p></div>`);
          } else {
            _push(`<!---->`);
          }
          if (item.answer) {
            _push(`<div class="answer" data-v-901223f0><h3 data-v-901223f0>${ssrInterpolate(`回答 ${index + 1}`)}</h3><div data-v-901223f0>${formatAnswer(item.answer) ?? ""}</div></div>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</div>`);
        });
        _push(`<!--]--></div><div class="seo-metadata" data-v-901223f0><p data-v-901223f0>案例ID: ${ssrInterpolate(_ctx.caseId)}</p><p data-v-901223f0>医学AI智能对话，专业医疗问答，梅斯小智</p><p data-v-901223f0>关键词: 医学AI, 智能问答, 医疗咨询, 案例分析, 梅斯医学</p></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
    };
  }
});

const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/SeoQaList.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const SeoQaList = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["__scopeId", "data-v-901223f0"]]);

function useSmartScroll(options = {}) {
  const {
    bottomThreshold = 50,
    scrollDuration = 300,
    scrollBehavior = "smooth",
    userScrollDebounce = 150,
    debug = false
  } = options;
  const isAutoScrollEnabled = ref(true);
  const isUserScrolling = ref(false);
  const lastScrollTop = ref(0);
  const scrollContainer = ref(null);
  let userScrollTimer = null;
  let autoScrollTimer = null;
  const log = (...args) => {
    if (debug) {
      /* @__PURE__ */ console.log("[SmartScroll]", ...args);
    }
  };
  const isNearBottom = () => {
    if (!scrollContainer.value) return false;
    const container = scrollContainer.value;
    const scrollTop = container.scrollTop;
    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
    const isNear = distanceFromBottom <= bottomThreshold;
    log("距离底部:", distanceFromBottom, "是否接近:", isNear);
    return isNear;
  };
  const scrollToBottom = (force = false) => {
    if (!scrollContainer.value) return;
    if (!isAutoScrollEnabled.value && !force) return;
    const container = scrollContainer.value;
    log("滚动到底部, 强制:", force);
    container.scrollTo({
      top: container.scrollHeight,
      behavior: scrollBehavior
    });
  };
  const handleUserScroll = () => {
    if (!scrollContainer.value) return;
    const currentScrollTop = scrollContainer.value.scrollTop;
    if (Math.abs(currentScrollTop - lastScrollTop.value) > 5) {
      isUserScrolling.value = true;
      if (currentScrollTop < lastScrollTop.value) {
        isAutoScrollEnabled.value = false;
        log("用户向上滚动，禁用自动滚动");
      }
      if (userScrollTimer) {
        clearTimeout(userScrollTimer);
      }
      userScrollTimer = setTimeout(() => {
        isUserScrolling.value = false;
        if (isNearBottom()) {
          isAutoScrollEnabled.value = true;
          log("用户滚动到底部，恢复自动滚动");
        }
      }, userScrollDebounce);
    }
    lastScrollTop.value = currentScrollTop;
  };
  const smartScroll = () => {
    if (!scrollContainer.value) return;
    if (isUserScrolling.value) {
      log("用户正在滚动，延迟自动滚动");
      if (autoScrollTimer) {
        clearTimeout(autoScrollTimer);
      }
      autoScrollTimer = setTimeout(smartScroll, 100);
      return;
    }
    if (!isAutoScrollEnabled.value) {
      if (isNearBottom()) {
        isAutoScrollEnabled.value = true;
        log("检测到接近底部，恢复自动滚动");
      } else {
        return;
      }
    }
    scrollToBottom();
  };
  const handleTouchStart = () => {
    log("触摸开始");
    isUserScrolling.value = true;
  };
  const handleTouchEnd = () => {
    log("触摸结束");
    setTimeout(() => {
      isUserScrolling.value = false;
      if (isNearBottom()) {
        isAutoScrollEnabled.value = true;
        log("触摸结束后检测到底部，恢复自动滚动");
      }
    }, userScrollDebounce);
  };
  const handleWheel = (event) => {
    if (event.deltaY !== 0) {
      log("鼠标滚轮滚动");
      handleUserScroll();
    }
  };
  const initScrollContainer = (element) => {
    if (scrollContainer.value) {
      removeEventListeners();
    }
    scrollContainer.value = element;
    lastScrollTop.value = element.scrollTop;
    element.addEventListener("scroll", handleUserScroll, { passive: true });
    element.addEventListener("wheel", handleWheel, { passive: true });
    element.addEventListener("touchstart", handleTouchStart, { passive: true });
    element.addEventListener("touchend", handleTouchEnd, { passive: true });
    log("初始化滚动容器");
  };
  const removeEventListeners = () => {
    if (!scrollContainer.value) return;
    const element = scrollContainer.value;
    element.removeEventListener("scroll", handleUserScroll);
    element.removeEventListener("wheel", handleWheel);
    element.removeEventListener("touchstart", handleTouchStart);
    element.removeEventListener("touchend", handleTouchEnd);
    log("移除事件监听器");
  };
  const enableAutoScroll = () => {
    isAutoScrollEnabled.value = true;
    isUserScrolling.value = false;
    log("强制启用自动滚动");
  };
  const disableAutoScroll = () => {
    isAutoScrollEnabled.value = false;
    log("禁用自动滚动");
  };
  const cleanup = () => {
    removeEventListeners();
    if (userScrollTimer) {
      clearTimeout(userScrollTimer);
      userScrollTimer = null;
    }
    if (autoScrollTimer) {
      clearTimeout(autoScrollTimer);
      autoScrollTimer = null;
    }
    log("清理资源");
  };
  return {
    // 状态
    isAutoScrollEnabled: readonly(isAutoScrollEnabled),
    isUserScrolling: readonly(isUserScrolling),
    // 方法
    initScrollContainer,
    smartScroll,
    scrollToBottom,
    enableAutoScroll,
    disableAutoScroll,
    isNearBottom,
    cleanup,
    // 内部状态（用于调试）
    scrollContainer: readonly(scrollContainer)
  };
}

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "[caseId]",
  __ssrInlineRender: true,
  props: {
    currentAppUuid: { default: "" }
  },
  async setup(__props) {
    let __temp, __restore;
    const route = useRoute();
    const router = useRouter();
    const { t, locale } = useI18n();
    const caseId = computed(() => route.params.caseId);
    const messages = ref([]);
    const isProcessing = ref(false);
    const hasInitialized = ref(false);
    const caseTitle = ref(t("case.title"));
    const isInstantMode = ref(false);
    const hasShownInstant = ref(false);
    ref(null);
    ref();
    ref();
    const appName = ref("");
    const demo_case = ref(true);
    const timersRef = ref([]);
    const isRequestingRef = ref(false);
    const currentMessageIndexRef = ref(0);
    const allMessagesRef = ref([]);
    const displayTexts = ref({});
    const typingStates = ref({});
    const {
      smartScroll,
      enableAutoScroll
    } = useSmartScroll({
      bottomThreshold: 50,
      scrollBehavior: "smooth",
      userScrollDebounce: 150,
      debug: false
      // 生产环境设为false
    });
    const seoQaData = ref([]);
    const seoTitle = ref("");
    const lang = ref(locale.value);
    const { data: seoData } = ([__temp, __restore] = withAsyncContext(async () => useAsyncData(
      `case-seo-${caseId.value}`,
      async () => {
        var _a;
        try {
          const event = useRequestEvent();
          /* @__PURE__ */ console.log("服务端预取SEO数据，案例ID:", caseId.value);
          const response = await qaList({
            articleId: "",
            encryptionId: caseId.value
          }, event);
          if (response && Array.isArray(response) && response.length > 0) {
            const firstItem = response[0];
            let qaItems = [];
            let title = "";
            if (firstItem && firstItem.question) {
              title = firstItem.question;
            }
            appName.value = firstItem.userName;
            /* @__PURE__ */ console.log("appName:", appName.value);
            if (firstItem && firstItem.answer) {
              let answerData = firstItem.answer;
              if (typeof answerData === "string") {
                try {
                  answerData = JSON.parse(answerData);
                  demo_case.value = ((_a = answerData[0]) == null ? void 0 : _a.demo_case) || true;
                } catch (parseError) {
                  console.error("解析answer数据失败:", parseError);
                  return { qaItems: [], title: "" };
                }
              }
              if (Array.isArray(answerData) && answerData.length > 0) {
                qaItems = answerData;
              }
            }
            /* @__PURE__ */ console.log("服务端预取成功，QA项目数量:", qaItems.length);
            return { qaItems, title };
          }
          return { qaItems: [], title: "" };
        } catch (error) {
          console.error("服务端预取SEO数据失败:", error);
          return { qaItems: [], title: "" };
        }
      },
      {
        server: true,
        // 确保只在服务端执行
        default: () => ({ qaItems: [], title: "" })
      }
    )), __temp = await __temp, __restore(), __temp);
    if (seoData.value) {
      seoQaData.value = seoData.value.qaItems || [];
      seoTitle.value = seoData.value.title || "";
      if (seoData.value.title) {
        caseTitle.value = seoData.value.title;
      }
    }
    const clearAllTimers = () => {
      timersRef.value.forEach((timer) => clearTimeout(timer));
      timersRef.value = [];
    };
    const getAppLink = () => {
      const langPath = lang.value ? `/${lang.value == "zh-CN" ? "zh" : lang.value}` : "";
      const appNameValue = appName.value || "";
      {
        return `https://ai.medon.com.cn${langPath}/${appNameValue.replace(" ", "-").toLowerCase()}`;
      }
    };
    const startTypewriter = (messageId, text) => {
      displayTexts.value[messageId] = "";
      typingStates.value[messageId] = true;
      enableAutoScroll();
      let currentIndex = 0;
      const speed = 0;
      const typeNextChar = () => {
        if (currentIndex < text.length) {
          displayTexts.value[messageId] += text[currentIndex];
          currentIndex++;
          if (currentIndex % 5 === 0) {
            nextTick(() => {
              smartScroll();
            });
          }
          const timer = setTimeout(typeNextChar, speed);
          timersRef.value.push(timer);
        } else {
          typingStates.value[messageId] = false;
          nextTick(() => {
            smartScroll();
          });
          const timer = setTimeout(() => {
            handleTypingComplete(messageId);
          }, 0);
          timersRef.value.push(timer);
        }
      };
      typeNextChar();
    };
    const handleCaseNotFound = () => {
      setTimeout(() => {
        router.push(lang.value ? `/${lang.value}` : "/");
      }, 3e3);
    };
    const fetchCaseData = async () => {
      if (isRequestingRef.value) return;
      isRequestingRef.value = true;
      try {
        const response = await qaList({
          articleId: "",
          encryptionId: caseId.value
        });
        if (response && Array.isArray(response) && response.length > 0) {
          hasInitialized.value = true;
          const firstItem = response[0];
          if (firstItem && firstItem.question) {
            caseTitle.value = firstItem.question;
          }
          generateMessagesFromCaseData(response);
        } else {
          handleCaseNotFound();
        }
      } catch (error) {
        handleCaseNotFound();
      } finally {
        isRequestingRef.value = false;
      }
    };
    const generateMessagesFromCaseData = (caseData) => {
      if (isProcessing.value) {
        return;
      }
      clearAllTimers();
      messages.value = [];
      isProcessing.value = true;
      let qaItems = [];
      try {
        if (caseData && Array.isArray(caseData) && caseData.length > 0) {
          const firstItem = caseData[0];
          if (firstItem && firstItem.answer) {
            let answerData = firstItem.answer;
            if (typeof answerData === "string") {
              try {
                answerData = JSON.parse(answerData);
              } catch (parseError) {
                handleCaseNotFound();
                return;
              }
            }
            if (Array.isArray(answerData) && answerData.length > 0) {
              qaItems = answerData;
            } else {
              handleCaseNotFound();
              return;
            }
          } else {
            handleCaseNotFound();
            return;
          }
        } else {
          handleCaseNotFound();
          return;
        }
        if (qaItems.length === 0) {
          handleCaseNotFound();
          return;
        }
      } catch (error) {
        handleCaseNotFound();
        return;
      }
      generateQAMessages(qaItems);
    };
    const generateQAMessages = (qaItems) => {
      isProcessing.value = true;
      const allMessages = [];
      qaItems.forEach((item, index) => {
        if (item.query && item.query.trim()) {
          const userMessage = {
            id: `user_${index}_${Date.now()}`,
            type: "user",
            content: item.query.trim().includes("【--Final content--】") ? item.query.trim().split("【--Final content--】")[1] : item.query.trim(),
            timestamp: /* @__PURE__ */ new Date(),
            message_files: item.message_files || []
          };
          allMessages.push(userMessage);
        }
        if (item.answer && item.answer.trim()) {
          const assistantMessage = {
            id: `assistant_${index}_${Date.now()}`,
            type: "assistant",
            content: item.answer.trim().includes("【--Final content--】") ? item.answer.trim().split("【--Final content--】")[1] : item.answer.trim(),
            timestamp: /* @__PURE__ */ new Date(),
            isGenerating: true
          };
          allMessages.push(assistantMessage);
        }
      });
      showMessagesSequentially(allMessages);
    };
    const showMessagesSequentially = (allMessages) => {
      if (allMessages.length === 0) {
        isProcessing.value = false;
        return;
      }
      currentMessageIndexRef.value = 0;
      allMessagesRef.value = allMessages;
      messages.value = [];
      showNextMessage();
    };
    const showNextMessage = () => {
      const currentIndex = currentMessageIndexRef.value;
      const allMessages = allMessagesRef.value;
      if (currentIndex >= allMessages.length) {
        isProcessing.value = false;
        return;
      }
      const nextMessage = allMessages[currentIndex];
      currentMessageIndexRef.value++;
      messages.value = [...messages.value, nextMessage];
      nextTick(() => {
        smartScroll();
      });
      if (nextMessage.type === "user") {
        const timer = setTimeout(() => {
          showNextMessage();
        }, 300);
        timersRef.value.push(timer);
      } else if (nextMessage.type === "assistant" && !isInstantMode.value) {
        startTypewriter(nextMessage.id, nextMessage.content);
      }
    };
    const handleTypingComplete = (messageId) => {
      messages.value = messages.value.map(
        (msg) => msg.id === messageId ? { ...msg, isGenerating: false } : msg
      );
      const timer = setTimeout(() => {
        showNextMessage();
      }, 300);
      timersRef.value.push(timer);
    };
    watch(() => caseId.value, (newCaseId) => {
      if (!newCaseId) {
        handleCaseNotFound();
        return;
      }
      if (isRequestingRef.value) {
        return;
      }
      if (hasInitialized.value && messages.value.length > 0) {
        return;
      }
      clearAllTimers();
      isProcessing.value = false;
      messages.value = [];
      hasInitialized.value = false;
      isInstantMode.value = false;
      hasShownInstant.value = false;
      fetchCaseData();
    }, { immediate: true });
    watch(() => messages.value.length, () => {
      nextTick(() => {
        smartScroll();
      });
    });
    computed(() => {
      var _a;
      if (seoQaData.value && seoQaData.value.length > 0) {
        const firstQuestion = ((_a = seoQaData.value[0]) == null ? void 0 : _a.query) || "";
        const baseDesc = t("case.description");
        return firstQuestion ? `${firstQuestion} - ${baseDesc} - 梅斯小智医学AI智能对话案例` : `${baseDesc} - 梅斯小智医学AI智能对话案例`;
      }
      return `${t("case.description")} - 梅斯小智医学AI智能对话案例`;
    });
    const seoKeywords = computed(() => {
      const baseKeywords = `${t("case.caseAnalysis")},${t("case.medicalConsultation")},${t("case.intelligentQA")}`;
      if (seoQaData.value && seoQaData.value.length > 0) {
        const questions = seoQaData.value.map((item) => item.query).filter(Boolean);
        const questionKeywords = questions.slice(0, 1).join(", ");
        return questionKeywords ? `${questionKeywords}, ${baseKeywords}` : baseKeywords;
      }
      return baseKeywords;
    });
    useSeoMeta({
      title: () => `${caseTitle.value} - ${appName.value} ${t("case.intelligentDialogue")}`,
      description: () => `${caseTitle.value}`,
      keywords: () => seoKeywords.value,
      ogTitle: () => `${caseTitle.value} - ${appName.value} ${t("case.intelligentDialogue")}`,
      ogDescription: () => `${caseTitle.value}`,
      ogType: "article",
      ogUrl: () => `https://ai.medon.com.cn/cases/${caseId.value}`,
      twitterCard: "summary",
      twitterTitle: `${caseTitle.value} - ${appName.value} ${t("case.intelligentDialogue")}`,
      twitterDescription: () => `${caseTitle.value}`
    });
    useHead({
      title: () => `${caseTitle.value} - ${appName.value} ${t("case.intelligentDialogue")}`
    });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({
        class: "relative h-screen",
        style: { backgroundColor: "var(--bg-main)" }
      }, _attrs))} data-v-90f26e8a><div class="h-screen flex flex-col" data-v-90f26e8a><div class="md:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 px-4 py-3 shadow-sm" data-v-90f26e8a><div class="flex items-center" data-v-90f26e8a><button class="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 transition-colors mr-3" data-v-90f26e8a><svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-90f26e8a><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" data-v-90f26e8a></path></svg></button><div class="flex items-center flex-1" data-v-90f26e8a><div class="w-8 h-8 bg-gradient-to-r rounded-full flex items-center justify-center mr-3" data-v-90f26e8a><a${ssrRenderAttr("href", getAppLink())} data-v-90f26e8a><img${ssrRenderAttr("src", unref(appName) == "Novax Base" ? "https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png" : unref(appName).value == "ElavaX Base" ? "https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png" : "https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png")}${ssrRenderAttr("alt", unref(appName))} class="w-5 h-5 rounded-full" data-v-90f26e8a></a></div><span class="text-lg font-semibold text-gray-800" data-v-90f26e8a>${ssrInterpolate(unref(appName))}</span></div><button class="flex items-center justify-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-xl transition-colors duration-200"${ssrRenderAttr("title", unref(isInstantMode) ? _ctx.$t("case.viewReplay") : _ctx.$t("case.viewResults"))} data-v-90f26e8a><span data-v-90f26e8a>${ssrInterpolate(unref(isInstantMode) ? _ctx.$t("case.viewReplay") : _ctx.$t("case.viewResults"))}</span></button></div></div><div class="hidden md:block bg-white border-b border-gray-200 px-6 py-4 shadow-sm" data-v-90f26e8a><div class="max-w-4xl mx-auto" data-v-90f26e8a><div class="flex items-center justify-between" data-v-90f26e8a><div class="flex items-center space-x-3 w-[90%]" data-v-90f26e8a><div class="flex flex-col items-center" data-v-90f26e8a><div class="w-10 h-10 bg-gradient-to-r rounded-full flex items-center justify-center text-white font-medium mb-1" data-v-90f26e8a><a${ssrRenderAttr("href", getAppLink())} data-v-90f26e8a><img${ssrRenderAttr("src", unref(appName) == "Novax Base" ? "https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png" : unref(appName).value == "ElavaX Base" ? "https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png" : "https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png")}${ssrRenderAttr("alt", unref(appName))} class="w-6 h-6 rounded-full" data-v-90f26e8a></a></div><span class="text-xs text-gray-600 font-medium text-center block w-full" data-v-90f26e8a>${ssrInterpolate(unref(appName))}</span></div><div data-v-90f26e8a><h1 class="text-lg font-semibold text-gray-800" data-v-90f26e8a>${ssrInterpolate(unref(caseTitle))}</h1>`);
      if (unref(demo_case)) {
        _push(`<p class="text-sm" data-v-90f26e8a><span class="text-blue-600 font-medium" data-v-90f26e8a>${ssrInterpolate(_ctx.$t("case.demo_case"))}</span></p>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div><button class="flex items-center justify-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-xl transition-colors duration-200 shadow-sm hover:shadow-md"${ssrRenderAttr("title", unref(isInstantMode) ? _ctx.$t("case.viewReplay") : _ctx.$t("case.viewResults"))} data-v-90f26e8a><span data-v-90f26e8a>${ssrInterpolate(unref(isInstantMode) ? _ctx.$t("case.viewReplay") : _ctx.$t("case.viewResults"))}</span></button></div></div></div><div class="flex-1 px-4 py-6 pb-48 overflow-y-auto bg-gray-50 pt-16 md:pt-6" data-v-90f26e8a><div class="max-w-4xl mx-auto" data-v-90f26e8a><!--[-->`);
      ssrRenderList(unref(messages), (message) => {
        _push(`<div class="mx-auto max-w-4xl relative group mb-6" data-v-90f26e8a>`);
        if (message.type === "user") {
          _push(`<div class="ml-auto max-w-2xl bg-gray-0 border border-gray-200 rounded-2xl rounded-br-md p-4 shadow-sm hover:shadow-md transition-all duration-200" data-v-90f26e8a><div class="text-gray-800 text-base leading-relaxed" data-v-90f26e8a>`);
          _push(ssrRenderComponent(AiResponseRenderer, {
            content: message.content,
            "font-size": "base"
          }, null, _parent));
          _push(`</div>`);
          _push(ssrRenderComponent(MessageAttachments, {
            attachments: message.message_files
          }, null, _parent));
          _push(`</div>`);
        } else {
          _push(`<div class="mr-auto max-w-4xl" data-v-90f26e8a><div class="text-base text-gray-800 leading-relaxed prose prose-base max-w-none" data-v-90f26e8a>`);
          if (message.isGenerating && !unref(isInstantMode) && unref(typingStates)[message.id]) {
            _push(`<div data-v-90f26e8a>`);
            _push(ssrRenderComponent(AiResponseRenderer, {
              content: (unref(displayTexts)[message.id] || "") + (unref(typingStates)[message.id] ? "▊" : ""),
              "font-size": "lg",
              "is-typing": true
            }, null, _parent));
            _push(`</div>`);
          } else {
            _push(`<div data-v-90f26e8a>`);
            _push(ssrRenderComponent(AiResponseRenderer, {
              content: message.content,
              "font-size": "lg"
            }, null, _parent));
            _push(`</div>`);
          }
          _push(`</div>`);
          _push(ssrRenderComponent(MessageAttachments, {
            attachments: message.message_files
          }, null, _parent));
          _push(`</div>`);
        }
        _push(`</div>`);
      });
      _push(`<!--]--><div data-v-90f26e8a></div></div></div></div>`);
      _push(ssrRenderComponent(SeoQaList, {
        "case-id": unref(caseId),
        "case-title": unref(seoTitle) || unref(caseTitle),
        "qa-data": unref(seoQaData)
      }, null, _parent));
      _push(`</div>`);
    };
  }
});

const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/cases/[caseId].vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const _caseId_ = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-90f26e8a"]]);

export { _caseId_ as default };
//# sourceMappingURL=_caseId_.vue.mjs.map
