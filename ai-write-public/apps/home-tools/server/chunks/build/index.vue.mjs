import { _ as __nuxt_component_0$1 } from './client-only.mjs';
import { ref, computed, withAsyncContext, watch, mergeProps, unref, useSSRContext, resolveComponent, withCtx, createVNode, createTextVNode, toDisplayString, withModifiers, createBlock, createCommentVNode, openBlock } from 'vue';
import { ssrRenderAttrs, ssrRenderStyle, ssrRenderAttr, ssrInterpolate, ssrRenderComponent, ssrRenderList, ssrRenderClass } from 'vue/server-renderer';
import { useRouter } from 'vue-router';
import cookie from 'js-cookie';
import { e as useI18n, f as useRoute, g as useCookie, i as useRequestEvent, k as useRequestHeaders, u as useRouter$1 } from './server.mjs';
import { g as getAppLangs, a as getPackageByKey, b as getAppList, c as getAppTypes, d as getAppClickNum, e as createSubscription } from './requestDify.mjs';
import { u as useAsyncData } from './asyncData.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper.mjs';
import { DArrowRight } from '@element-plus/icons-vue';
import { c as customerService } from './index.vue3.mjs';
import { g as getAssetsFile, p as pay, a as payMobile } from './index.mjs';
import { Popup, showDialog } from 'vant';
import { h as home } from './lang.mjs';
import { d as defaultLanguageName, a as appTypes, l as languages, g as getDefaultLanguageCode } from './commonJs.mjs';
import { ElMessage } from 'element-plus';
import { u as useHead } from './v3.mjs';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import 'consola';
import 'node:path';
import 'node:crypto';
import 'axios';
import './qrcode.png.mjs';
import './interval.mjs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'unhead/plugins';
import 'unhead/utils';
import 'devalue';

const _sfc_main$3 = {
  __name: "index",
  __ssrInlineRender: true,
  props: {
    subStatusDetail: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  emits: ["getAppLang", "getAppLang", "subScript"],
  async setup(__props, { emit: __emit }) {
    let __temp, __restore;
    useRouter();
    const { setLocale, locale } = useI18n();
    const props = __props;
    const subStatusDetail = ref(props.subStatusDetail);
    const avatar = ref("");
    const userInfo = ref(null);
    const isIncludeTool = ref(false);
    const selectedLanguage = ref("");
    const langs = ref([]);
    const hrefUrl = ref("");
    ref(null);
    ref();
    ref();
    const isMobiles = ref();
    computed(() => "https://www.medsci.cn/");
    const userInfoCookie = computed(() => cookie.get("userInfo"));
    const checkCookie = () => {
      var _a;
      const newVal = cookie.get("userInfo");
      if (newVal !== JSON.stringify(userInfo.value)) {
        userInfo.value = newVal ? JSON.parse(newVal) : null;
        avatar.value = ((_a = userInfo.value) == null ? void 0 : _a.avatar) || "https://img.medsci.cn/web/img/user_icon.png";
      }
    };
    const { data: res } = ([__temp, __restore] = withAsyncContext(async () => useAsyncData("languages", async () => {
      var _a;
      const route = useRoute();
      const cookie2 = useCookie("userInfo");
      const ai_apps_lang = useCookie("ai_apps_lang");
      const event = useRequestEvent();
      const header = useRequestHeaders();
      userInfo.value = cookie2.value || null;
      avatar.value = ((_a = userInfo.value) == null ? void 0 : _a.avatar) || "https://img.medsci.cn/web/img/user_icon.png";
      const res2 = await getAppLangs(event);
      langs.value = res2.filter((item) => !item.status).map((item) => ({ name: item.value, value: item.remark }));
      selectedLanguage.value = ai_apps_lang.value;
      isIncludeTool.value = route.path.includes("/tool");
      isMobiles.value = /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry)/i.test(header["user-agent"]);
      return [isMobiles.value, selectedLanguage.value, isIncludeTool.value, langs.value, avatar.value, userInfo.value];
    })), __temp = await __temp, __restore(), __temp);
    isMobiles.value = (res == null ? void 0 : res.value) && (res == null ? void 0 : res.value[0]);
    selectedLanguage.value = (res == null ? void 0 : res.value) && (res == null ? void 0 : res.value[1]);
    isIncludeTool.value = (res == null ? void 0 : res.value) && (res == null ? void 0 : res.value[2]);
    langs.value = (res == null ? void 0 : res.value) && (res == null ? void 0 : res.value[3]);
    userInfo.value = (res == null ? void 0 : res.value) && (res == null ? void 0 : res.value[5]);
    avatar.value = (res == null ? void 0 : res.value) && (res == null ? void 0 : res.value[4]);
    watch(userInfoCookie, (newVal) => {
      checkCookie();
    });
    return (_ctx, _push, _parent, _attrs) => {
      var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k;
      const _component_client_only = __nuxt_component_0$1;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "header ms-header-media" }, _attrs))} data-v-cc7a7df6><div class="ms-header" data-v-cc7a7df6><div class="wrapper" data-v-cc7a7df6><div class="main-menu-placeholder wrapper clearfix" style="${ssrRenderStyle({ "height": "56px", "display": "block !important" })}" data-v-cc7a7df6><div class="ms-header-img" data-v-cc7a7df6><a${ssrRenderAttr("href", hrefUrl.value)} title="梅斯小智" data-v-cc7a7df6><img src="https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png" alt="" data-v-cc7a7df6></a></div><div id="main-menu" class="ms-header-nav" data-v-cc7a7df6><div class="header-top header-user" id="user-info-header" data-v-cc7a7df6><ul data-v-cc7a7df6>`);
      if (unref(locale) == "zh") {
        _push(`<li data-v-cc7a7df6><div class="m_font change_lang m_none h-full" data-v-cc7a7df6><button type="primary" style="${ssrRenderStyle({ "background-image": "linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%)" })}" class="px-[15px] py-[4px] flex items-center h-[28px] rounded border-none text-xs text-[#614018]" data-v-cc7a7df6>${ssrInterpolate(((_a = subStatusDetail.value) == null ? void 0 : _a.packageType) == "免费" ? "升级订阅" : ((_b = subStatusDetail.value) == null ? void 0 : _b.packageType) == "连续包月" || ((_c = subStatusDetail.value) == null ? void 0 : _c.packageType) == "连续包年" ? "修改订阅" : "订阅")}</button></div><div class="m_font change_lang pc_none" data-v-cc7a7df6><button type="primary" style="${ssrRenderStyle({ "background-image": "linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%)" })}" class="px-[14px] py-[4px] rounded-[8px] flex items-center h-[28px] rounded border-none text-xs text-[#614018] whitespace-nowrap" data-v-cc7a7df6>${ssrInterpolate(((_d = subStatusDetail.value) == null ? void 0 : _d.packageType) == "免费" ? "升级订阅" : ((_e = subStatusDetail.value) == null ? void 0 : _e.packageType) == "连续包月" || ((_f = subStatusDetail.value) == null ? void 0 : _f.packageType) == "连续包年" ? "修改订阅" : "订阅")}</button></div></li>`);
      } else {
        _push(`<!---->`);
      }
      if (!isMobiles.value) {
        _push(`<li data-v-cc7a7df6><a href="https://ai.medsci.cn/idoc"${ssrRenderAttr("title", _ctx.$t("tool.iDoc"))} target="_blank" data-v-cc7a7df6>${ssrInterpolate(_ctx.$t("tool.idocWebsite"))}</a></li>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<li data-v-cc7a7df6><div class="mr-2 px-md-4 cursor-pointer m_font" data-v-cc7a7df6><a href="https://aisite.medsci.cn/"${ssrRenderAttr("title", _ctx.$t("tool.AINavigationSite"))} target="_blank" data-v-cc7a7df6>${ssrInterpolate(_ctx.$t("tool.AINavigationSite"))}</a></div></li><li class="index-user-img index-user-img_left" data-v-cc7a7df6>`);
      if (!isIncludeTool.value) {
        _push(`<div class="change_lang" data-v-cc7a7df6><span class="current_lang" data-v-cc7a7df6>${ssrInterpolate((_g = langs.value.filter((item) => item.value == selectedLanguage.value)[0]) == null ? void 0 : _g.name)}</span><span class="ms-link" data-v-cc7a7df6>${ssrInterpolate(_ctx.$t("market.switch"))}</span></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="ms-dropdown-menu" data-v-cc7a7df6><div class="new-header-avator-pop" id="new-header-avator" data-v-cc7a7df6><div class="new-header-bottom" style="${ssrRenderStyle({ "padding": "0" })}" data-v-cc7a7df6>`);
      _push(ssrRenderComponent(_component_client_only, null, {}, _parent));
      _push(`</div></div></div></li>`);
      if (!((_h = userInfo.value) == null ? void 0 : _h.userId)) {
        _push(`<li class="index-user-img_right" data-v-cc7a7df6><a href="javascript: void(0)" class="ms-link" data-v-cc7a7df6>${ssrInterpolate(_ctx.$t("market.login"))}</a></li>`);
      } else {
        _push(`<li class="index-user-img" data-v-cc7a7df6><a href="#" data-v-cc7a7df6><div class="img-area" data-v-cc7a7df6><img${ssrRenderAttr(
          "src",
          avatar.value ? avatar.value + "?t=" + Date.now() : "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
        )} alt="" data-v-cc7a7df6></div></a><div class="ms-dropdown-menu" data-v-cc7a7df6><div class="new-header-avator-pop" id="new-header-avator" data-v-cc7a7df6><a class="new-header-exit ms-statis" ms-statis="logout" href="#" data-v-cc7a7df6>${ssrInterpolate(_ctx.$t("market.logout"))}</a><div class="new-header-top" data-v-cc7a7df6><div class="new-header-info" data-v-cc7a7df6><img class="new-header-avatar"${ssrRenderAttr(
          "src",
          avatar.value ? avatar.value + "?t=" + Date.now() : "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
        )} alt="" data-v-cc7a7df6><div class="new-header-name" data-v-cc7a7df6><span data-v-cc7a7df6>${ssrInterpolate(((_i = userInfo.value) == null ? void 0 : _i.realName) ? (_j = userInfo.value) == null ? void 0 : _j.realName : (_k = userInfo.value) == null ? void 0 : _k.userName)}</span></div></div></div></div></div></li>`);
      }
      _push(`</ul></div></div></div></div></div></div>`);
    };
  }
};
const _sfc_setup$3 = _sfc_main$3.setup;
_sfc_main$3.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/headerNav/index.vue");
  return _sfc_setup$3 ? _sfc_setup$3(props, ctx) : void 0;
};
const __nuxt_component_0 = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["__scopeId", "data-v-cc7a7df6"]]);

const _sfc_main$2 = {
  name: "FooterNavZH",
  data() {
    return {
      showFooter: false
    };
  },
  head() {
  },
  computed: {},
  mounted() {
  },
  methods: {
    userFeedBack() {
      (void 0).open("https://www.medsci.cn/message/list.do", "_blank");
    }
  }
};
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<footer${ssrRenderAttrs(mergeProps({ class: "bg-[#F7F7F7] bg" }, _attrs))} data-v-fa4c9331>`);
  if ($data.showFooter) {
    _push(`<div id="footer" data-v-fa4c9331><div class="wrapper mobile-b w-footer-wrapper" id="foot" data-v-fa4c9331><div class="footer-widgets w-footer-widgets lets-do-4" data-v-fa4c9331><div class="widget-split item phone-hidden" data-v-fa4c9331><div class="widget ms-footer-img" data-v-fa4c9331><div data-v-fa4c9331><p data-v-fa4c9331><a href="https://www.medsci.cn" class="ms-statis" ms-statis="link" data-v-fa4c9331><img src="https://static.medsci.cn/public-image/ms-image/416a6450-b970-11ec-a1b8-6123b3ff61ea_MSLOGO1.png" alt="" data-v-fa4c9331></a></p><p class="bold w-footer-bold" data-v-fa4c9331> 梅斯医学MedSci-临床医生发展平台 </p><p class="text-justify w-footer-des" data-v-fa4c9331> 梅斯医学是面向医生的综合互联网平台，应用大数据和人工智能技术链接医生、患者、药械企业等，提供精准数字化医学传播解决方案，优化医疗生态，改善医疗质量，共创美好生活。 </p></div></div></div><div class="widget-split item ms-footer-item phone-hidden w-footer-item" data-v-fa4c9331><div class="widget" data-v-fa4c9331><h3 class="w-footer-h3" data-v-fa4c9331>关于我们</h3><div class="clearfix" data-v-fa4c9331><ul class="menu left" data-v-fa4c9331><li class="ms-link iconfont" data-v-fa4c9331><a target="_blank" href="https://www.medsci.cn/about/index.do?id=9" class="ms-statis" ms-statis="link" data-v-fa4c9331>关于我们</a></li><li class="ms-link iconfont" data-v-fa4c9331><a target="_blank" href="https://www.medsci.cn/recruit/job-list" class="ms-statis" ms-statis="link" data-v-fa4c9331>加入我们</a></li><li class="ms-link iconfont" data-v-fa4c9331><a target="_blank" href="https://www.medsci.cn/about/index.do?id=21" class="ms-statis" ms-statis="link" data-v-fa4c9331>版权合作</a></li><li class="ms-link iconfont" data-v-fa4c9331><a target="_blank" href="https://ir.medsci.cn/zh_cn/" class="ms-statis" ms-statis="link" data-v-fa4c9331>投资者关系</a></li><li class="ms-link iconfont" data-v-fa4c9331><a target="_blank" href="http://medscihealthcare.com/" class="ms-statis" ms-statis="link" data-v-fa4c9331>MedSci Healthcare</a></li><li class="ms-link iconfont" data-v-fa4c9331><a target="_blank" href="https://www.medsci.cn/about/index.do?id=19" class="ms-statis" ms-statis="link" data-v-fa4c9331>友情链接</a></li></ul></div></div></div><div class="widget-split item ms-footer-item phone-hidden w-footer-item" data-v-fa4c9331><div class="widget" data-v-fa4c9331><h3 class="w-footer-h3" data-v-fa4c9331>我们的业务</h3><div class="clearfix" data-v-fa4c9331><ul class="menu left" data-v-fa4c9331><li class="ms-link iconfont" data-v-fa4c9331><a href="https://www.medsci.cn/service/tree_list.do" target="_blank" class="ms-statis" ms-statis="link" data-v-fa4c9331>真实世界研究</a></li><li class="ms-link iconfont" data-v-fa4c9331><a href="https://www.medsci.cn/service/tree_list.do" target="_blank" class="ms-statis" ms-statis="link" data-v-fa4c9331>科研数智化</a></li><li class="ms-link iconfont" data-v-fa4c9331><a href="https://www.medsci.cn/service/tree_list.do" target="_blank" class="ms-statis" ms-statis="link" data-v-fa4c9331>数字化学术传播</a></li></ul></div></div></div><div class="widget-split item ms-footer-item phone-hidden w-footer-item" data-v-fa4c9331><div class="widget" data-v-fa4c9331><h3 class="w-footer-h3" data-v-fa4c9331>我们的产品</h3><div class="clearfix" data-v-fa4c9331><ul class="menu left" data-v-fa4c9331><li class="ms-link iconfont" data-v-fa4c9331><a href="https://www.medsci.cn/sci/index.do" target="_blank" class="ms-statis" ms-statis="link" data-v-fa4c9331>期刊智能查询</a></li><li class="ms-link iconfont" data-v-fa4c9331><a href="https://www.medsci.cn/sci/nsfc.do" target="_blank" class="ms-statis" ms-statis="link" data-v-fa4c9331>国自然查询分析</a></li><li class="ms-link iconfont" data-v-fa4c9331><a href="https://www.medsci.cn/guideline/search" target="_blank" class="ms-statis" ms-statis="link" data-v-fa4c9331>临床指南</a></li><li class="ms-link iconfont" data-v-fa4c9331><a href="https://m.medsci.cn/scale" target="_blank" class="ms-statis" ms-statis="link" data-v-fa4c9331>医学公式计算</a></li><li class="ms-link iconfont" data-v-fa4c9331><a href="https://dict.bioon.com/" target="_blank" class="ms-statis" ms-statis="link" data-v-fa4c9331>医药生物大词典</a></li><li class="ms-link iconfont" data-v-fa4c9331><a href="https://class.medsci.cn/" target="_blank" class="ms-statis" ms-statis="link" data-v-fa4c9331>梅斯精品课</a></li><li class="ms-link iconfont" data-v-fa4c9331><a href="https://open.medsci.cn/" target="_blank" class="ms-statis" ms-statis="link" data-v-fa4c9331>梅斯公开课</a></li></ul></div></div></div><div class="w-footer-right phone-hidden" data-v-fa4c9331><div class="widget" data-v-fa4c9331><h3 class="w-footer-h3" data-v-fa4c9331>新媒体矩阵</h3><div id="footOwl" class="owl-carousel" data-v-fa4c9331><div class="item w-owl-item" data-v-fa4c9331><img src="https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_gzh.png" alt="" data-v-fa4c9331><span data-v-fa4c9331>梅斯医学</span></div><div class="item w-owl-item" data-v-fa4c9331><img src="https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_zhongliu.png" alt="" data-v-fa4c9331><span data-v-fa4c9331>肿瘤新前沿</span></div><div class="item w-owl-item" data-v-fa4c9331><img src="https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_xueye.jpg" alt="" data-v-fa4c9331><span data-v-fa4c9331>血液新前沿</span></div><div class="item w-owl-item" data-v-fa4c9331><img src="https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_medsci_fengshi.jpg" alt="" data-v-fa4c9331><span data-v-fa4c9331>风湿新前沿</span></div><div class="item w-owl-item" data-v-fa4c9331><img src="https://static.medsci.cn/public-image/ms-image/caaaa270-93a5-11ec-bca5-7f892b5df5d6_medsci_xqy.png" alt="" data-v-fa4c9331><span data-v-fa4c9331>呼吸新前沿</span></div><div class="item w-owl-item" data-v-fa4c9331><img src="https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_pifu.jpg" alt="" data-v-fa4c9331><span data-v-fa4c9331>皮肤新前沿</span></div><div class="item w-owl-item" data-v-fa4c9331><img src="https://static.medsci.cn/public-image/ms-image/bce723f0-609d-11ed-b66b-937b834e3ef9_sjqianyan.jpg" alt="" data-v-fa4c9331><span data-v-fa4c9331>神经新前沿</span></div><div class="item w-owl-item" data-v-fa4c9331><img src="https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xiaohua.jpg" alt="" data-v-fa4c9331><span data-v-fa4c9331>消化新前沿</span></div><div class="item w-owl-item" data-v-fa4c9331><img src="https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xinxueguan.jpg" alt="" data-v-fa4c9331><span data-v-fa4c9331>心血管新前沿</span></div><div class="item w-owl-item" data-v-fa4c9331><img src="https://static.medsci.cn/public-image/ms-image/dc22e340-957c-11ec-bca5-7f892b5df5d6_ms_shengwugu.png" alt="" data-v-fa4c9331><span data-v-fa4c9331>生物谷</span></div><div class="item w-owl-item" data-v-fa4c9331><img src="https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_app.png" alt="" data-v-fa4c9331><span data-v-fa4c9331>MedSci App</span></div></div></div></div></div></div></div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`<div class="footer-copyright1 ms-footer-copy w-footer-copy" data-v-fa4c9331><p data-v-fa4c9331><a href="https://www.medsci.cn/about/index.do?id=18" target="_blank" class="ms-link ms-statis" ms-statis="link" data-v-fa4c9331>${ssrInterpolate(_ctx.$t("market.privacyPolicy"))}</a><span style="${ssrRenderStyle({ "margin": "0px 20px" })}" data-v-fa4c9331>|</span><a href="https://www.medsci.cn/about/index.do?id=9" target="_blank" class="ms-link ms-statis" ms-statis="link" data-v-fa4c9331>${ssrInterpolate(_ctx.$t("market.termService"))}</a><span style="${ssrRenderStyle({ "margin": "0px 20px" })}" data-v-fa4c9331>|</span><a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank" class="ms-link ms-statis" ms-statis="link" data-v-fa4c9331>备案号 沪ICP备14018916号-1</a></p></div></footer>`);
}
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/footerNavZH/index.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const __nuxt_component_1 = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["ssrRender", _sfc_ssrRender$1], ["__scopeId", "data-v-fa4c9331"]]);

const _sfc_main$1 = {
  name: "FooterNavZH",
  data() {
    return {
      showFooter: false
    };
  },
  head() {
  },
  computed: {},
  mounted() {
  },
  methods: {
    userFeedBack() {
      (void 0).open("https://www.medsci.cn/message/list.do", "_blank");
    }
  }
};
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<footer${ssrRenderAttrs(mergeProps({ class: "bg-[#F7F7F7] bg" }, _attrs))} data-v-71657141>`);
  if ($data.showFooter) {
    _push(`<div id="footer" data-v-71657141><div class="wrapper mobile-b w-footer-wrapper" id="foot" data-v-71657141><div class="footer-widgets w-footer-widgets lets-do-4" data-v-71657141><div class="widget-split item phone-hidden" data-v-71657141><div class="widget ms-footer-img" data-v-71657141><div data-v-71657141><p data-v-71657141><a href="https://www.medsci.cn" class="ms-statis" ms-statis="link" data-v-71657141><img src="https://static.medsci.cn/public-image/ms-image/416a6450-b970-11ec-a1b8-6123b3ff61ea_MSLOGO1.png" alt="" data-v-71657141></a></p><p class="bold w-footer-bold" data-v-71657141> 梅斯医学MedSci-临床医生发展平台 </p><p class="text-justify w-footer-des" data-v-71657141> 梅斯医学是面向医生的综合互联网平台，应用大数据和人工智能技术链接医生、患者、药械企业等，提供精准数字化医学传播解决方案，优化医疗生态，改善医疗质量，共创美好生活。 </p></div></div></div><div class="widget-split item ms-footer-item phone-hidden w-footer-item" data-v-71657141><div class="widget" data-v-71657141><h3 class="w-footer-h3" data-v-71657141>关于我们</h3><div class="clearfix" data-v-71657141><ul class="menu left" data-v-71657141><li class="ms-link iconfont" data-v-71657141><a target="_blank" href="https://www.medsci.cn/about/index.do?id=9" class="ms-statis" ms-statis="link" data-v-71657141>关于我们</a></li><li class="ms-link iconfont" data-v-71657141><a target="_blank" href="https://www.medsci.cn/recruit/job-list" class="ms-statis" ms-statis="link" data-v-71657141>加入我们</a></li><li class="ms-link iconfont" data-v-71657141><a target="_blank" href="https://www.medsci.cn/about/index.do?id=21" class="ms-statis" ms-statis="link" data-v-71657141>版权合作</a></li><li class="ms-link iconfont" data-v-71657141><a target="_blank" href="https://ir.medsci.cn/zh_cn/" class="ms-statis" ms-statis="link" data-v-71657141>投资者关系</a></li><li class="ms-link iconfont" data-v-71657141><a target="_blank" href="http://medscihealthcare.com/" class="ms-statis" ms-statis="link" data-v-71657141>MedSci Healthcare</a></li><li class="ms-link iconfont" data-v-71657141><a target="_blank" href="https://www.medsci.cn/about/index.do?id=19" class="ms-statis" ms-statis="link" data-v-71657141>友情链接</a></li></ul></div></div></div><div class="widget-split item ms-footer-item phone-hidden w-footer-item" data-v-71657141><div class="widget" data-v-71657141><h3 class="w-footer-h3" data-v-71657141>我们的业务</h3><div class="clearfix" data-v-71657141><ul class="menu left" data-v-71657141><li class="ms-link iconfont" data-v-71657141><a href="https://www.medsci.cn/service/tree_list.do" target="_blank" class="ms-statis" ms-statis="link" data-v-71657141>真实世界研究</a></li><li class="ms-link iconfont" data-v-71657141><a href="https://www.medsci.cn/service/tree_list.do" target="_blank" class="ms-statis" ms-statis="link" data-v-71657141>科研数智化</a></li><li class="ms-link iconfont" data-v-71657141><a href="https://www.medsci.cn/service/tree_list.do" target="_blank" class="ms-statis" ms-statis="link" data-v-71657141>数字化学术传播</a></li></ul></div></div></div><div class="widget-split item ms-footer-item phone-hidden w-footer-item" data-v-71657141><div class="widget" data-v-71657141><h3 class="w-footer-h3" data-v-71657141>我们的产品</h3><div class="clearfix" data-v-71657141><ul class="menu left" data-v-71657141><li class="ms-link iconfont" data-v-71657141><a href="https://www.medsci.cn/sci/index.do" target="_blank" class="ms-statis" ms-statis="link" data-v-71657141>期刊智能查询</a></li><li class="ms-link iconfont" data-v-71657141><a href="https://www.medsci.cn/sci/nsfc.do" target="_blank" class="ms-statis" ms-statis="link" data-v-71657141>国自然查询分析</a></li><li class="ms-link iconfont" data-v-71657141><a href="https://www.medsci.cn/guideline/search" target="_blank" class="ms-statis" ms-statis="link" data-v-71657141>临床指南</a></li><li class="ms-link iconfont" data-v-71657141><a href="https://m.medsci.cn/scale" target="_blank" class="ms-statis" ms-statis="link" data-v-71657141>医学公式计算</a></li><li class="ms-link iconfont" data-v-71657141><a href="https://dict.bioon.com/" target="_blank" class="ms-statis" ms-statis="link" data-v-71657141>医药生物大词典</a></li><li class="ms-link iconfont" data-v-71657141><a href="https://class.medsci.cn/" target="_blank" class="ms-statis" ms-statis="link" data-v-71657141>梅斯精品课</a></li><li class="ms-link iconfont" data-v-71657141><a href="https://open.medsci.cn/" target="_blank" class="ms-statis" ms-statis="link" data-v-71657141>梅斯公开课</a></li></ul></div></div></div><div class="w-footer-right phone-hidden" data-v-71657141><div class="widget" data-v-71657141><h3 class="w-footer-h3" data-v-71657141>新媒体矩阵</h3><div id="footOwl" class="owl-carousel" data-v-71657141><div class="item w-owl-item" data-v-71657141><img src="https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_gzh.png" alt="" data-v-71657141><span data-v-71657141>梅斯医学</span></div><div class="item w-owl-item" data-v-71657141><img src="https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_zhongliu.png" alt="" data-v-71657141><span data-v-71657141>肿瘤新前沿</span></div><div class="item w-owl-item" data-v-71657141><img src="https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_xueye.jpg" alt="" data-v-71657141><span data-v-71657141>血液新前沿</span></div><div class="item w-owl-item" data-v-71657141><img src="https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_medsci_fengshi.jpg" alt="" data-v-71657141><span data-v-71657141>风湿新前沿</span></div><div class="item w-owl-item" data-v-71657141><img src="https://static.medsci.cn/public-image/ms-image/caaaa270-93a5-11ec-bca5-7f892b5df5d6_medsci_xqy.png" alt="" data-v-71657141><span data-v-71657141>呼吸新前沿</span></div><div class="item w-owl-item" data-v-71657141><img src="https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_pifu.jpg" alt="" data-v-71657141><span data-v-71657141>皮肤新前沿</span></div><div class="item w-owl-item" data-v-71657141><img src="https://static.medsci.cn/public-image/ms-image/bce723f0-609d-11ed-b66b-937b834e3ef9_sjqianyan.jpg" alt="" data-v-71657141><span data-v-71657141>神经新前沿</span></div><div class="item w-owl-item" data-v-71657141><img src="https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xiaohua.jpg" alt="" data-v-71657141><span data-v-71657141>消化新前沿</span></div><div class="item w-owl-item" data-v-71657141><img src="https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xinxueguan.jpg" alt="" data-v-71657141><span data-v-71657141>心血管新前沿</span></div><div class="item w-owl-item" data-v-71657141><img src="https://static.medsci.cn/public-image/ms-image/dc22e340-957c-11ec-bca5-7f892b5df5d6_ms_shengwugu.png" alt="" data-v-71657141><span data-v-71657141>生物谷</span></div><div class="item w-owl-item" data-v-71657141><img src="https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_app.png" alt="" data-v-71657141><span data-v-71657141>MedSci App</span></div></div></div></div></div></div></div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`<div class="footer-copyright1 ms-footer-copy w-footer-copy" data-v-71657141><p data-v-71657141><a href="https://www.medsci.cn/about/index.do?id=18" target="_blank" class="ms-link ms-statis" ms-statis="link" data-v-71657141>${ssrInterpolate(_ctx.$t("market.privacyPolicy"))}</a><span style="${ssrRenderStyle({ "margin": "0px 20px" })}" data-v-71657141>|</span><a href="https://www.medsci.cn/about/index.do?id=9" target="_blank" class="ms-link ms-statis" ms-statis="link" data-v-71657141>${ssrInterpolate(_ctx.$t("market.termService"))}</a></p></div></footer>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/footerNav/index.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const __nuxt_component_2 = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender], ["__scopeId", "data-v-71657141"]]);

const _imports_0 = "data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%2029%2029'%20class='design-iconfont'%3e%3cg%20transform='rotate(-45%2015.36396167%206.36396076)'%20fill='%235298FF'%20fill-rule='evenodd'%3e%3cpath%20d='M9,0%20C13.9705627,0%2018,4.02943725%2018,9%20C18,13.7801691%2014.2733399,17.68993%209.56686501,17.9824374%20L9.69675669,15.9657586%20C13.235637,15.6160543%2016,12.6308529%2016,9%20C16,5.13400675%2012.8659932,2%209,2%20C5.13400675,2%202,5.13400675%202,9%20C2,11.7045844%203.53383568,14.0509224%205.77908446,15.2165912%20L4.77843352,16.9505064%20C1.93552483,15.437831%200,12.4449485%200,9%20C0,4.02943725%204.02943725,0%209,0%20Z'/%3e%3crect%20x='9'%20y='16'%20width='2'%20height='6'%20rx='1'/%3e%3cpath%20d='M4.75930317,16.943048%20C5.48230534,17.1582288%205.96481235,17.0672504%206.20682419,16.670113%20C6.49391901,16.1351207%206.37249799,15.4723489%205.77475988,15.2202576%20C5.28032363,15.0236511%204.94183806,15.5979146%204.75930317,16.943048%20Z'/%3e%3c/g%3e%3c/svg%3e";

function transformArrayToObject(array) {
  const result = {};
  array.forEach((item) => {
    const [langCode] = item.key.split(".");
    const value = JSON.parse(item.value);
    if (!result[langCode]) {
      result[langCode] = {};
    }
    result[langCode] = { ...result[langCode], ...value };
  });
  return result;
}
function setItemWithTimestamp(key, value) {
  const timestamp = Date.now();
  localStorage.setItem(key + "_value", value);
  localStorage.setItem(key + "_timestamp", timestamp);
}
function getItemWithTimestampCheck(key, daysThreshold) {
  const valueKey = key + "_value";
  const timestampKey = key + "_timestamp";
  const storedValue = localStorage.getItem(valueKey);
  const storedTimestamp = localStorage.getItem(timestampKey);
  if (storedValue !== null && storedTimestamp !== null) {
    const storedDate = new Date(storedTimestamp);
    const currentDate = /* @__PURE__ */ new Date();
    const timeDifference = currentDate - storedDate;
    const differenceInDays = timeDifference / (1e3 * 60 * 60 * 24);
    if (differenceInDays > daysThreshold) {
      localStorage.removeItem(valueKey);
      localStorage.removeItem(timestampKey);
      return null;
    } else {
      return storedValue;
    }
  } else {
    return null;
  }
}
function fetchLanguageConfig() {
  let langsStr = getItemWithTimestampCheck("current_langs_pack", 7);
  let umoData = getItemWithTimestampCheck("current_langs_pack_umo", 7);
  if (!langsStr || !umoData) {
    const apiUrl = "https://ai.medon.com.cn/dev-api/ai-base/index/getConfigPage";
    fetch(apiUrl).then((response) => {
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      return response.json();
    }).then((data) => {
      if (data.data.list.length !== 0) {
        langsStr = JSON.stringify(transformArrayToObject(data.data.list));
        setItemWithTimestamp("current_langs_pack", langsStr);
        let filteredData = data.data.list.filter((e) => e.value != "{}" && e.key.includes(".dify"));
        let umoData2 = filteredData.reduce((acc, item) => {
          if (!acc[item.key.substr(0, item.key.indexOf("."))]) {
            acc[item.key.substr(0, item.key.indexOf("."))] = {};
          }
          acc[item.key.substr(0, item.key.indexOf("."))] = JSON.parse(item.value);
          return acc;
        }, {});
        setItemWithTimestamp("current_langs_pack_umo", JSON.stringify(umoData2));
      }
    }).catch((error) => {
      console.error("Failed to fetch language config:", error);
    });
  }
}

const TITLE = "faq.xAI";
const _sfc_main = {
  __name: "index",
  __ssrInlineRender: true,
  async setup(__props) {
    let __temp, __restore;
    const { t, locale } = useI18n();
    const ai_apps_lang = useCookie("ai_apps_lang", { domain: ".medon.com.cn", maxAge: 30 * 24 * 60 * 60 * 12 });
    const bg = getAssetsFile("基于AI的写作文本加工.png");
    const bg1 = getAssetsFile("基于AI的写作文本加工In.png");
    useRoute();
    useRouter$1();
    const inputValue = ref("");
    const typeList = ref([
      {
        value: "我的应用",
        remark: "我的应用"
      },
      {
        value: "",
        remark: "全部"
      }
    ]);
    const isZH = ref(false);
    const typeActive = ref(1);
    const bgImg = ref(null);
    const userInfo = ref(null);
    const activeTabName = ref("first");
    const currentItem = ref({});
    const payShow = ref(false);
    const defaultA = async (item) => {
      if (!(item == null ? void 0 : item.dAppUuid)) {
        ElMessage({
          message: "请先至后台绑定应用实例",
          type: "warning"
        });
        return;
      }
      await getAppClickNum(item.appUuid, localStorage.getItem("openid"));
      sessionStorage.setItem("nodeInfo", JSON.stringify(item));
      if (isMobile() && item.appType == "写作") {
        showDialog({ title: "提示", message: "写作类智能体请使用电脑web端打开，获取最佳使用体验。", confirmButtonColor: "#D7813F" });
      } else {
        if (item.appType == "写作") {
          localStorage.setItem(
            "appWrite-" + item.appUuid,
            JSON.stringify({
              appUuid: item.appUuid,
              directoryMd: item.directoryMd
            })
          );
        }
        (void 0).open(item.url);
      }
    };
    const params = ref({
      appType: "",
      socialUserId: "",
      //三方用户ID，如用户主站ID,登录时传
      appLang: "",
      //语言
      order: 2,
      //1使用频率/订阅热度，2订阅到期/点击热度
      isMine: 2
      // 1我的，2其他
    });
    const isMobile = () => {
      return /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry)/i.test((void 0).userAgent);
    };
    const close = () => {
      payShow.value = false;
    };
    const subScript = async () => {
      if (locale.value == "zh") {
        subStatusDetail.value = await getPackageByKey();
        payShow.value = true;
      } else {
        payShow.value = true;
      }
    };
    const subscribe = async (item, appUuid) => {
      var _a;
      let language = getDefaultLanguageCode(locale.value);
      if (!((_a = userInfo.value) == null ? void 0 : _a.userId)) {
        if (!language || language == "zh") {
          (void 0).addLoginDom();
        } else {
          (void 0).href = (void 0).origin + "/" + locale.value + "/login";
        }
      } else {
        const subscriptionParams = {
          appUuid,
          priceId: item.priceId,
          monthNum: item.monthNum
        };
        let res = await createSubscription(subscriptionParams);
        if (res) {
          ElMessage({
            type: "success",
            message: t("tool.sS")
          });
          setTimeout(() => {
            (void 0).href = res;
          }, 1e3);
        }
      }
    };
    const handleClick = (item) => {
      var _a;
      if (((_a = item.appUser) == null ? void 0 : _a.status) == 1) {
        goToPage(item);
      } else {
        handleOrder(item);
      }
    };
    const handleInputChange = (val) => {
      if (!val) {
        menuList.value = JSON.parse(JSON.stringify(allData));
        return;
      }
      let list = [];
      if (typeActive.value == 1) {
        list = JSON.parse(JSON.stringify(allData));
      } else if (typeActive.value != 0) {
        list = JSON.parse(JSON.stringify(allData)).filter(
          (item) => item.appType === typeList.value[typeActive.value].value
        );
      } else if (typeActive.value == 0) {
        list = JSON.parse(JSON.stringify(menuList.value));
      }
      const escapedVal = val.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
      const reg = new RegExp(escapedVal, "gi");
      const highlightList = list.filter((item) => {
        return reg.test(item.appName) || reg.test(item.appDescription) || reg.test(item.mapType);
      });
      const highlightText = (text) => {
        if (!text) return text;
        return text.replace(
          reg,
          (match) => `<span style="color: #409eff">${match}</span>`
        );
      };
      menuList.value = highlightList.map((item) => ({
        ...item,
        appName: highlightText(item.appName),
        appDescription: highlightText(item.appDescription),
        mapType: highlightText(item.mapType)
      }));
    };
    const goToPage = async (item) => {
      if (!(item == null ? void 0 : item.dAppUuid)) {
        ElMessage({
          message: "请先至后台绑定应用实例",
          type: "warning"
        });
        return;
      }
      getAppClickNum(item.appUuid, localStorage.getItem("openid"));
      sessionStorage.setItem("nodeInfo", JSON.stringify(item));
      const currentUrl = (void 0).location.origin.includes("medsci.cn") || (void 0).location.origin.includes("medon.com.cn") ? (void 0).location.origin : (void 0).location.origin;
      if (item.appType === "工具") {
        (void 0).open(
          `${currentUrl}${locale.value == "zh" ? "" : "/" + locale.value}/tool/${item == null ? void 0 : item.appNameEn}`,
          "_blank"
        );
        return;
      } else if (item.appType === "问答") {
        (void 0).open(
          `${currentUrl}${locale.value == "zh" ? "" : "/" + locale.value}/chat/${item == null ? void 0 : item.appNameEn}`,
          "_blank"
        );
        return;
      } else if (item.appType === "写作") {
        if (isMobile()) {
          showDialog({ title: "提示", message: "写作类智能体请使用电脑web端打开，获取最佳使用体验。", confirmButtonColor: "#D7813F" });
          return;
        }
        await fetchLanguageConfig();
        localStorage.setItem(
          "appWrite-" + item.appUuid,
          JSON.stringify({
            appUuid: item.appUuid,
            directoryMd: item.directoryMd
          })
        );
        (void 0).open(
          `${(void 0).location.origin}${locale.value == "zh" ? "" : "/" + locale.value}/write/${item.appNameEn}`
        );
      }
    };
    let allData = [];
    const subStatusDetail = ref();
    const menuList = ref(null);
    const bgImgs = ref([
      "https://img.medsci.cn/medsci-ai/bg1.png?imageMogr2/format/webp",
      "https://img.medsci.cn/medsci-ai/bg2.png?imageMogr2/format/webp",
      "https://img.medsci.cn/medsci-ai/bg3.png?imageMogr2/format/webp",
      "https://img.medsci.cn/medsci-ai/bg4.png?imageMogr2/format/webp",
      "https://img.medsci.cn/medsci-ai/bg5.png?imageMogr2/format/webp",
      "https://img.medsci.cn/medsci-ai/bg6.png?imageMogr2/format/webp"
    ]);
    const { data: appListData } = ([__temp, __restore] = withAsyncContext(async () => useAsyncData("getAppList", async () => {
      var _a;
      const event = useRequestEvent();
      const userInfo2 = useCookie("userInfo");
      if ((_a = userInfo2.value) == null ? void 0 : _a.userId) {
        params.value.socialUserId = userInfo2.value.plaintextUserId;
      }
      ai_apps_lang.value = locale.value;
      params.value.appLang = defaultLanguageName(locale.value);
      params.value.languages = locale.value;
      let PackageByKey;
      if (locale.value == "zh") {
        PackageByKey = await getPackageByKey(event);
      }
      const res = await getAppList(params.value, event);
      const res1 = await getAppTypes(ai_apps_lang.value, event);
      let index2 = Math.floor(Math.random() * 6);
      if (res) {
        res.forEach((item) => {
          item.mapType = appTypes[item.appType];
        });
        res.forEach((item) => {
          if (item.appType === "工具") {
            item.url = `https://ai.medon.com.cn${locale.value == "zh" ? "" : "/" + locale.value}/tool/${item == null ? void 0 : item.appNameEn}`;
          } else if (item.appType === "问答") {
            item.url = `https://ai.medon.com.cn${locale.value == "zh" ? "" : "/" + locale.value}/chat/${item == null ? void 0 : item.appNameEn}`;
          } else if (item.appType === "写作") {
            item.url = `https://ai.medon.com.cn${locale.value == "zh" ? "" : "/" + locale.value}/write/${item == null ? void 0 : item.appNameEn}`;
          }
        });
        return [res, res1, index2, PackageByKey];
      }
      return null;
    }, { server: true })), __temp = await __temp, __restore(), __temp);
    allData = JSON.parse(JSON.stringify((appListData == null ? void 0 : appListData.value) && (appListData == null ? void 0 : appListData.value[0])));
    menuList.value = (appListData == null ? void 0 : appListData.value) && (appListData == null ? void 0 : appListData.value[0]);
    (appListData == null ? void 0 : appListData.value) && typeList.value.push(...appListData == null ? void 0 : appListData.value[1]);
    bgImg.value = bgImgs.value[1];
    subStatusDetail.value = (appListData == null ? void 0 : appListData.value) && (appListData == null ? void 0 : appListData.value[3]);
    useHead({
      title: `${home["title"][ai_apps_lang.value]}`,
      meta: [
        {
          name: "keywords",
          content: `${home["keywords"][ai_apps_lang.value]}`
        },
        {
          name: "description",
          content: `${home["description"][ai_apps_lang.value]}`
        },
        { property: "og:type", content: "website" },
        { property: "og:title", content: `${home["title"][ai_apps_lang.value]}` },
        { property: "og:description", content: `${home["description"][ai_apps_lang.value]}` },
        { property: "og:image", content: "https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png" },
        { name: "twitter:card", content: "summary_large_image" },
        // 注意：根据常见用法推断 content 为 'summary_large_image'
        { name: "twitter:title", content: `${home["title"][ai_apps_lang.value]}` },
        { name: "twitter:description", content: `${home["description"][ai_apps_lang.value]}` },
        { name: "twitter:image", content: "https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png" }
      ]
    });
    const isZHChange = (val) => {
      isZH.value = val;
    };
    const getAppLang = (val) => {
      params.value.appLang = languages[val];
      getAppListData();
    };
    const getAppListData = () => {
      getAppList(params.value).then((res) => {
        var _a, _b;
        res.forEach((item) => {
          if (item.appType === "工具") {
            item.url = (process.env.VITE_MODE == "development" ? "http://localhost:3000" : process.env.VITE_MODE == "test" ? "https://ai.medon.com.cn" : process.env.VITE_MODE == "prd" ? "https://ai.medsci.cn" : "") + `${locale.value == "zh" ? "" : "/" + locale.value}/tool/${item == null ? void 0 : item.appNameEn}`;
          } else if (item.appType === "问答") {
            item.url = (process.env.VITE_MODE == "development" ? "http://localhost:3000" : process.env.VITE_MODE == "test" ? "https://ai.medon.com.cn" : process.env.VITE_MODE == "prd" ? "https://ai.medsci.cn" : "") + `${locale.value == "zh" ? "" : "/" + locale.value}/chat/${item == null ? void 0 : item.appNameEn}`;
          } else if (item.appType === "写作") {
            item.url = (process.env.VITE_MODE == "development" ? "http://localhost:3000" : process.env.VITE_MODE == "test" ? "https://ai.medon.com.cn" : process.env.VITE_MODE == "prd" ? "https://ai.medsci.cn" : "") + `${locale.value == "zh" ? "" : "/" + locale.value}/write/${item == null ? void 0 : item.appNameEn}`;
          }
        });
        menuList.value = res == null ? void 0 : res.map((item) => {
          return {
            ...item,
            mapType: appTypes[item.appType]
          };
        });
        if (params.value.appType == "") {
          allData = [...menuList.value];
        }
        if (params.value.isMine == 1) {
          if (activeTabName.value == "first") {
            menuList.value = (_a = menuList.value) == null ? void 0 : _a.filter(
              (item) => {
                var _a2;
                return ((_a2 = item.appUser) == null ? void 0 : _a2.status) == 1;
              }
            );
          }
          if (activeTabName.value == "second") {
            menuList.value = (_b = menuList.value) == null ? void 0 : _b.filter(
              (item) => {
                var _a2;
                return ((_a2 = item.appUser) == null ? void 0 : _a2.status) == 2;
              }
            );
          }
        }
      }).catch((err) => {
      });
    };
    const handleOrder = async (item) => {
      currentItem.value = item;
      payShow.value = true;
    };
    const handleTabChange = () => {
      getAppListData();
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_HeaderNav = __nuxt_component_0;
      const _component_el_input = resolveComponent("el-input");
      const _component_el_icon = resolveComponent("el-icon");
      const _component_el_card = resolveComponent("el-card");
      const _component_el_button = resolveComponent("el-button");
      const _component_el_tabs = resolveComponent("el-tabs");
      const _component_el_tab_pane = resolveComponent("el-tab-pane");
      const _component_FooterNavZH = __nuxt_component_1;
      const _component_FooterNav = __nuxt_component_2;
      const _component_el_dialog = resolveComponent("el-dialog");
      const _component_client_only = __nuxt_component_0$1;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-[#F9F9F9] overflow-auto h-full" }, _attrs))} data-v-084a0b6a>`);
      _push(ssrRenderComponent(_component_HeaderNav, {
        onGetAppLang: getAppLang,
        subStatusDetail: subStatusDetail.value,
        onIsZHChange: isZHChange,
        onSubScript: subScript
      }, null, _parent));
      _push(`<div class="flex flex-col items-center h-[246px] relative min-w-[980px]" style="${ssrRenderStyle({
        background: `url(${bgImg.value}) no-repeat center`,
        backgroundSize: "cover"
      })}" data-v-084a0b6a><h1 class="pt-[75px] text-white mb-[30px] font-bold" data-v-084a0b6a>${ssrInterpolate(_ctx.$t(TITLE))}</h1><div class="flex justify-center" data-v-084a0b6a>`);
      _push(ssrRenderComponent(_component_el_input, {
        class: "!w-[888px] !h-[54px]",
        modelValue: inputValue.value,
        "onUpdate:modelValue": ($event) => inputValue.value = $event,
        placeholder: _ctx.$t("market.keywords"),
        clearable: "",
        onInput: handleInputChange
      }, {
        prefix: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_el_icon, {
              size: "24",
              class: "cursor-pointer mt-[2px]"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<img class="w-[24px] h-[24px]"${ssrRenderAttr("src", _imports_0)} alt="" data-v-084a0b6a${_scopeId2}>`);
                } else {
                  return [
                    createVNode("img", {
                      class: "w-[24px] h-[24px]",
                      src: _imports_0,
                      alt: ""
                    })
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
          } else {
            return [
              createVNode(_component_el_icon, {
                size: "24",
                class: "cursor-pointer mt-[2px]"
              }, {
                default: withCtx(() => [
                  createVNode("img", {
                    class: "w-[24px] h-[24px]",
                    src: _imports_0,
                    alt: ""
                  })
                ]),
                _: 1
              })
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div><main data-v-084a0b6a><div class="content" data-v-084a0b6a><div class="flex justify-center my-8 bg-[#F9F9F9]" data-v-084a0b6a><div class="flex items-center" data-v-084a0b6a><!--[-->`);
      ssrRenderList(typeList.value, (item, index2) => {
        _push(`<div class="${ssrRenderClass([
          typeActive.value == index2 ? "bg-[#409eff] text-white rounded-4xl" : "",
          "mr-2 px-4 py-1 cursor-pointer m_font"
        ])}" data-v-084a0b6a>${ssrInterpolate(_ctx.$t(`${unref(appTypes)[item.remark]}`))}</div>`);
      });
      _push(`<!--]--><div class="mr-2 px-4 cursor-pointer m_font" data-v-084a0b6a><a href="https://aisite.medsci.cn/" target="_blank" data-v-084a0b6a>${ssrInterpolate(_ctx.$t("tool.AINavigationSite"))}</a></div></div></div>`);
      if (typeActive.value != 0) {
        _push(`<div class="menu-box flex flex-wrap justify-between" data-v-084a0b6a><!--[-->`);
        ssrRenderList(menuList.value, (item, index2) => {
          _push(ssrRenderComponent(_component_el_card, {
            shadow: "hover",
            class: "max-w-[369px] cursor-pointer mb-[16px] !bg-[#FBFBFB] !border-0 cursor-item",
            style: {
              background: `url(${item.isInternalUser == 1 ? unref(bg1) : unref(bg)}) no-repeat center`,
              backgroundSize: "cover",
              width: "calc(33.33% - 8px)"
            },
            key: index2,
            onClick: ($event) => goToPage(item)
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              var _a, _b, _c, _d, _e, _f, _g, _h;
              if (_push2) {
                _push2(`<div class="flex mb-1 card-item" data-v-084a0b6a${_scopeId}><div class="flex" style="${ssrRenderStyle({ "width": "75%", "align-items": "center" })}" data-v-084a0b6a${_scopeId}><img class="w-[40px] h-[40px] block mr-2" style="${ssrRenderStyle({ "border-radius": "10px" })}"${ssrRenderAttr("src", item.appIcon)} alt="icon" data-v-084a0b6a${_scopeId}><div class="text-[16px] font-bold text-dark-200 two_lines" style="${ssrRenderStyle({ "width": "calc(100% - 40px)" })}"${ssrRenderAttr("title", item.appName)} data-v-084a0b6a${_scopeId}><a${ssrRenderAttr("href", item.url)}${ssrRenderAttr("title", item.appName)} target="_blank" data-v-084a0b6a${_scopeId}><h6 data-v-084a0b6a${_scopeId}>${item.appName ?? ""}</h6></a></div></div><div style="${ssrRenderStyle({ "width": "30%", "text-align": "right", "font-size": "14px" })}" data-v-084a0b6a${_scopeId}><a${ssrRenderAttr("href", item.url)}${ssrRenderAttr("title", item.appName)} target="_blank" data-v-084a0b6a${_scopeId}>`);
                _push2(ssrRenderComponent(_component_el_button, {
                  style: { "--el-button-bg-color": "#fff" },
                  size: "small",
                  color: "#2F92EE",
                  plain: "",
                  round: ""
                }, {
                  default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                    if (_push3) {
                      _push3(`${ssrInterpolate(_ctx.$t("market.open"))}`);
                      _push3(ssrRenderComponent(_component_el_icon, null, {
                        default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                          if (_push4) {
                            _push4(ssrRenderComponent(unref(DArrowRight), { style: { "margin-left": "4px" } }, null, _parent4, _scopeId3));
                          } else {
                            return [
                              createVNode(unref(DArrowRight), { style: { "margin-left": "4px" } })
                            ];
                          }
                        }),
                        _: 2
                      }, _parent3, _scopeId2));
                    } else {
                      return [
                        createTextVNode(toDisplayString(_ctx.$t("market.open")), 1),
                        createVNode(_component_el_icon, null, {
                          default: withCtx(() => [
                            createVNode(unref(DArrowRight), { style: { "margin-left": "4px" } })
                          ]),
                          _: 1
                        })
                      ];
                    }
                  }),
                  _: 2
                }, _parent2, _scopeId));
                _push2(`</a></div></div><div class="textOverflowFour tex-[#838383] leading-[18px] h-[72px] mb-[12px]"${ssrRenderAttr("title", item.appDescription)} data-v-084a0b6a${_scopeId}>${item.appDescription ?? ""}</div><div class="flex justify-between items-center" data-v-084a0b6a${_scopeId}><div class="text-[#B0B0B0]" data-v-084a0b6a${_scopeId}>${ssrInterpolate(_ctx.$t(`${unref(appTypes)[item.appType]}`))}</div>`);
                if (((_a = item.appUser) == null ? void 0 : _a.status) == 1) {
                  _push2(`<div class="during_order" data-v-084a0b6a${_scopeId}>${ssrInterpolate(_ctx.$t("market.subUntil"))}${ssrInterpolate((_b = item.appUser) == null ? void 0 : _b.expireAt)}${ssrInterpolate(_ctx.$t("market.expiredOn"))}</div>`);
                } else {
                  _push2(`<!---->`);
                }
                if (((_c = item.appUser) == null ? void 0 : _c.status) == 2) {
                  _push2(`<div class="delay_order" data-v-084a0b6a${_scopeId}>${ssrInterpolate(_ctx.$t("market.haveBeen"))}${ssrInterpolate((_d = item.appUser) == null ? void 0 : _d.expireAt)}${ssrInterpolate(_ctx.$t("market.expiredOn"))}</div>`);
                } else {
                  _push2(`<!---->`);
                }
                _push2(`</div>`);
              } else {
                return [
                  createVNode("div", { class: "flex mb-1 card-item" }, [
                    createVNode("div", {
                      class: "flex",
                      style: { "width": "75%", "align-items": "center" }
                    }, [
                      createVNode("img", {
                        class: "w-[40px] h-[40px] block mr-2",
                        style: { "border-radius": "10px" },
                        src: item.appIcon,
                        alt: "icon"
                      }, null, 8, ["src"]),
                      createVNode("div", {
                        class: "text-[16px] font-bold text-dark-200 two_lines",
                        style: { "width": "calc(100% - 40px)" },
                        title: item.appName
                      }, [
                        createVNode("a", {
                          href: item.url,
                          onClick: withModifiers(($event) => defaultA(item), ["stop", "prevent"]),
                          title: item.appName,
                          target: "_blank"
                        }, [
                          createVNode("h6", {
                            innerHTML: item.appName
                          }, null, 8, ["innerHTML"])
                        ], 8, ["href", "onClick", "title"])
                      ], 8, ["title"])
                    ]),
                    createVNode("div", { style: { "width": "30%", "text-align": "right", "font-size": "14px" } }, [
                      createVNode("a", {
                        href: item.url,
                        onClick: withModifiers(($event) => defaultA(item), ["stop", "prevent"]),
                        title: item.appName,
                        target: "_blank"
                      }, [
                        createVNode(_component_el_button, {
                          style: { "--el-button-bg-color": "#fff" },
                          size: "small",
                          color: "#2F92EE",
                          plain: "",
                          round: ""
                        }, {
                          default: withCtx(() => [
                            createTextVNode(toDisplayString(_ctx.$t("market.open")), 1),
                            createVNode(_component_el_icon, null, {
                              default: withCtx(() => [
                                createVNode(unref(DArrowRight), { style: { "margin-left": "4px" } })
                              ]),
                              _: 1
                            })
                          ]),
                          _: 1
                        })
                      ], 8, ["href", "onClick", "title"])
                    ])
                  ]),
                  createVNode("div", {
                    class: "textOverflowFour tex-[#838383] leading-[18px] h-[72px] mb-[12px]",
                    title: item.appDescription,
                    innerHTML: item.appDescription
                  }, null, 8, ["title", "innerHTML"]),
                  createVNode("div", { class: "flex justify-between items-center" }, [
                    createVNode("div", { class: "text-[#B0B0B0]" }, toDisplayString(_ctx.$t(`${unref(appTypes)[item.appType]}`)), 1),
                    ((_e = item.appUser) == null ? void 0 : _e.status) == 1 ? (openBlock(), createBlock("div", {
                      key: 0,
                      class: "during_order"
                    }, toDisplayString(_ctx.$t("market.subUntil")) + toDisplayString((_f = item.appUser) == null ? void 0 : _f.expireAt) + toDisplayString(_ctx.$t("market.expiredOn")), 1)) : createCommentVNode("", true),
                    ((_g = item.appUser) == null ? void 0 : _g.status) == 2 ? (openBlock(), createBlock("div", {
                      key: 1,
                      class: "delay_order"
                    }, toDisplayString(_ctx.$t("market.haveBeen")) + toDisplayString((_h = item.appUser) == null ? void 0 : _h.expireAt) + toDisplayString(_ctx.$t("market.expiredOn")), 1)) : createCommentVNode("", true)
                  ])
                ];
              }
            }),
            _: 2
          }, _parent));
        });
        _push(`<!--]--></div>`);
      } else {
        _push(`<div class="tab_box" data-v-084a0b6a>`);
        _push(ssrRenderComponent(_component_el_tabs, {
          modelValue: activeTabName.value,
          "onUpdate:modelValue": ($event) => activeTabName.value = $event,
          class: "demo-tabs",
          onTabChange: handleTabChange
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(_component_el_tab_pane, {
                label: _ctx.$t("market.subscribed"),
                name: "first"
              }, null, _parent2, _scopeId));
              _push2(ssrRenderComponent(_component_el_tab_pane, {
                label: _ctx.$t("market.expired"),
                name: "second"
              }, null, _parent2, _scopeId));
            } else {
              return [
                createVNode(_component_el_tab_pane, {
                  label: _ctx.$t("market.subscribed"),
                  name: "first"
                }, null, 8, ["label"]),
                createVNode(_component_el_tab_pane, {
                  label: _ctx.$t("market.expired"),
                  name: "second"
                }, null, 8, ["label"])
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`<div class="menu-box flex flex-wrap justify-between" data-v-084a0b6a><!--[-->`);
        ssrRenderList(menuList.value, (item, index2) => {
          _push(ssrRenderComponent(_component_el_card, {
            shadow: "hover",
            class: "max-w-[369px] cursor-pointer mb-[16px] !bg-[#FBFBFB] !border-0 cursor-item",
            style: {
              background: `url(${unref(bg)}) no-repeat center`,
              backgroundSize: "cover",
              width: "calc(33.33% - 8px)",
              maxHeight: "189.5px"
            },
            key: index2,
            onClick: ($event) => handleClick(item)
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l;
              if (_push2) {
                _push2(`<div class="flex mb-1 card-item" data-v-084a0b6a${_scopeId}><div class="flex" style="${ssrRenderStyle({ "width": "75%", "align-items": "center" })}" data-v-084a0b6a${_scopeId}><img class="w-[40px] h-[40px] block mr-2" style="${ssrRenderStyle({ "border-radius": "10px" })}"${ssrRenderAttr("src", item.appIcon)} alt="icon" data-v-084a0b6a${_scopeId}><div class="text-[16px] font-bold text-dark-200 two_lines" style="${ssrRenderStyle({ "width": "calc(100% - 40px)" })}"${ssrRenderAttr("title", item.appName)} data-v-084a0b6a${_scopeId}><a${ssrRenderAttr("href", item.url)}${ssrRenderAttr("title", item.appName)} target="_blank" data-v-084a0b6a${_scopeId}><h6 data-v-084a0b6a${_scopeId}>${item.appName ?? ""}</h6></a></div></div><div style="${ssrRenderStyle({ "width": "30%", "text-align": "right" })}" data-v-084a0b6a${_scopeId}><a${ssrRenderAttr("href", item.url)}${ssrRenderAttr("title", item.appName)} target="_blank" data-v-084a0b6a${_scopeId}>`);
                if (((_a = item.appUser) == null ? void 0 : _a.status) == 1) {
                  _push2(ssrRenderComponent(_component_el_button, {
                    style: { "--el-button-bg-color": "#fff" },
                    size: "small",
                    color: "#2F92EE",
                    plain: "",
                    round: ""
                  }, {
                    default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                      if (_push3) {
                        _push3(`${ssrInterpolate(_ctx.$t("market.open"))}`);
                        _push3(ssrRenderComponent(_component_el_icon, null, {
                          default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                            if (_push4) {
                              _push4(ssrRenderComponent(unref(DArrowRight), { style: { "margin-left": "4px" } }, null, _parent4, _scopeId3));
                            } else {
                              return [
                                createVNode(unref(DArrowRight), { style: { "margin-left": "4px" } })
                              ];
                            }
                          }),
                          _: 2
                        }, _parent3, _scopeId2));
                      } else {
                        return [
                          createTextVNode(toDisplayString(_ctx.$t("market.open")), 1),
                          createVNode(_component_el_icon, null, {
                            default: withCtx(() => [
                              createVNode(unref(DArrowRight), { style: { "margin-left": "4px" } })
                            ]),
                            _: 1
                          })
                        ];
                      }
                    }),
                    _: 2
                  }, _parent2, _scopeId));
                } else {
                  _push2(`<!---->`);
                }
                _push2(`</a>`);
                if (((_b = item.appUser) == null ? void 0 : _b.status) == 2) {
                  _push2(ssrRenderComponent(_component_el_button, {
                    style: { "--el-button-bg-color": "#fff" },
                    size: "small",
                    color: "#FF9A45",
                    plain: "",
                    round: "",
                    onClick: ($event) => handleOrder(item)
                  }, {
                    default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                      if (_push3) {
                        _push3(`${ssrInterpolate(_ctx.$t("market.renew"))}`);
                        _push3(ssrRenderComponent(_component_el_icon, null, {
                          default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                            if (_push4) {
                              _push4(ssrRenderComponent(unref(DArrowRight), { style: { "margin-left": "4px" } }, null, _parent4, _scopeId3));
                            } else {
                              return [
                                createVNode(unref(DArrowRight), { style: { "margin-left": "4px" } })
                              ];
                            }
                          }),
                          _: 2
                        }, _parent3, _scopeId2));
                      } else {
                        return [
                          createTextVNode(toDisplayString(_ctx.$t("market.renew")), 1),
                          createVNode(_component_el_icon, null, {
                            default: withCtx(() => [
                              createVNode(unref(DArrowRight), { style: { "margin-left": "4px" } })
                            ]),
                            _: 1
                          })
                        ];
                      }
                    }),
                    _: 2
                  }, _parent2, _scopeId));
                } else {
                  _push2(`<!---->`);
                }
                if (!item.appUser) {
                  _push2(ssrRenderComponent(_component_el_button, {
                    style: { "--el-button-bg-color": "#fff" },
                    size: "small",
                    color: "#FF9A45",
                    plain: "",
                    round: "",
                    onClick: ($event) => handleOrder(item)
                  }, {
                    default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                      if (_push3) {
                        _push3(`${ssrInterpolate(_ctx.$t("market.subscribe"))}`);
                        _push3(ssrRenderComponent(_component_el_icon, null, {
                          default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                            if (_push4) {
                              _push4(ssrRenderComponent(unref(DArrowRight), { style: { "margin-left": "4px" } }, null, _parent4, _scopeId3));
                            } else {
                              return [
                                createVNode(unref(DArrowRight), { style: { "margin-left": "4px" } })
                              ];
                            }
                          }),
                          _: 2
                        }, _parent3, _scopeId2));
                      } else {
                        return [
                          createTextVNode(toDisplayString(_ctx.$t("market.subscribe")), 1),
                          createVNode(_component_el_icon, null, {
                            default: withCtx(() => [
                              createVNode(unref(DArrowRight), { style: { "margin-left": "4px" } })
                            ]),
                            _: 1
                          })
                        ];
                      }
                    }),
                    _: 2
                  }, _parent2, _scopeId));
                } else {
                  _push2(`<!---->`);
                }
                _push2(`</div></div><div class="textOverflowFour tex-[#838383] leading-[18px] h-[72px] mb-[12px]" data-v-084a0b6a${_scopeId}>${item.appDescription ?? ""}</div><div class="flex justify-between items-center" data-v-084a0b6a${_scopeId}><div class="text-[#B0B0B0]" data-v-084a0b6a${_scopeId}>${ssrInterpolate(_ctx.$t(`${unref(appTypes)[item.appType]}`))}</div>`);
                if (((_c = item.appUser) == null ? void 0 : _c.status) == 1) {
                  _push2(`<div class="during_order" data-v-084a0b6a${_scopeId}>${ssrInterpolate(_ctx.$t("market.subUntil"))}${ssrInterpolate((_d = item.appUser) == null ? void 0 : _d.expireAt)}${ssrInterpolate(_ctx.$t("market.expiredOn"))}</div>`);
                } else {
                  _push2(`<!---->`);
                }
                if (((_e = item.appUser) == null ? void 0 : _e.status) == 2) {
                  _push2(`<div class="delay_order" data-v-084a0b6a${_scopeId}>${ssrInterpolate(_ctx.$t("market.haveBeen"))}${ssrInterpolate((_f = item.appUser) == null ? void 0 : _f.expireAt)}${ssrInterpolate(_ctx.$t("market.expiredOn"))}</div>`);
                } else {
                  _push2(`<!---->`);
                }
                _push2(`</div>`);
              } else {
                return [
                  createVNode("div", { class: "flex mb-1 card-item" }, [
                    createVNode("div", {
                      class: "flex",
                      style: { "width": "75%", "align-items": "center" }
                    }, [
                      createVNode("img", {
                        class: "w-[40px] h-[40px] block mr-2",
                        style: { "border-radius": "10px" },
                        src: item.appIcon,
                        alt: "icon"
                      }, null, 8, ["src"]),
                      createVNode("div", {
                        class: "text-[16px] font-bold text-dark-200 two_lines",
                        style: { "width": "calc(100% - 40px)" },
                        title: item.appName
                      }, [
                        createVNode("a", {
                          href: item.url,
                          onClick: withModifiers(($event) => defaultA(item), ["stop", "prevent"]),
                          title: item.appName,
                          target: "_blank"
                        }, [
                          createVNode("h6", {
                            innerHTML: item.appName
                          }, null, 8, ["innerHTML"])
                        ], 8, ["href", "onClick", "title"])
                      ], 8, ["title"])
                    ]),
                    createVNode("div", { style: { "width": "30%", "text-align": "right" } }, [
                      createVNode("a", {
                        href: item.url,
                        onClick: withModifiers(($event) => defaultA(item), ["stop", "prevent"]),
                        title: item.appName,
                        target: "_blank"
                      }, [
                        ((_g = item.appUser) == null ? void 0 : _g.status) == 1 ? (openBlock(), createBlock(_component_el_button, {
                          key: 0,
                          style: { "--el-button-bg-color": "#fff" },
                          size: "small",
                          color: "#2F92EE",
                          plain: "",
                          round: ""
                        }, {
                          default: withCtx(() => [
                            createTextVNode(toDisplayString(_ctx.$t("market.open")), 1),
                            createVNode(_component_el_icon, null, {
                              default: withCtx(() => [
                                createVNode(unref(DArrowRight), { style: { "margin-left": "4px" } })
                              ]),
                              _: 1
                            })
                          ]),
                          _: 1
                        })) : createCommentVNode("", true)
                      ], 8, ["href", "onClick", "title"]),
                      ((_h = item.appUser) == null ? void 0 : _h.status) == 2 ? (openBlock(), createBlock(_component_el_button, {
                        key: 0,
                        style: { "--el-button-bg-color": "#fff" },
                        size: "small",
                        color: "#FF9A45",
                        plain: "",
                        round: "",
                        onClick: withModifiers(($event) => handleOrder(item), ["stop"])
                      }, {
                        default: withCtx(() => [
                          createTextVNode(toDisplayString(_ctx.$t("market.renew")), 1),
                          createVNode(_component_el_icon, null, {
                            default: withCtx(() => [
                              createVNode(unref(DArrowRight), { style: { "margin-left": "4px" } })
                            ]),
                            _: 1
                          })
                        ]),
                        _: 2
                      }, 1032, ["onClick"])) : createCommentVNode("", true),
                      !item.appUser ? (openBlock(), createBlock(_component_el_button, {
                        key: 1,
                        style: { "--el-button-bg-color": "#fff" },
                        size: "small",
                        color: "#FF9A45",
                        plain: "",
                        round: "",
                        onClick: withModifiers(($event) => handleOrder(item), ["stop"])
                      }, {
                        default: withCtx(() => [
                          createTextVNode(toDisplayString(_ctx.$t("market.subscribe")), 1),
                          createVNode(_component_el_icon, null, {
                            default: withCtx(() => [
                              createVNode(unref(DArrowRight), { style: { "margin-left": "4px" } })
                            ]),
                            _: 1
                          })
                        ]),
                        _: 2
                      }, 1032, ["onClick"])) : createCommentVNode("", true)
                    ])
                  ]),
                  createVNode("div", {
                    class: "textOverflowFour tex-[#838383] leading-[18px] h-[72px] mb-[12px]",
                    innerHTML: item.appDescription
                  }, null, 8, ["innerHTML"]),
                  createVNode("div", { class: "flex justify-between items-center" }, [
                    createVNode("div", { class: "text-[#B0B0B0]" }, toDisplayString(_ctx.$t(`${unref(appTypes)[item.appType]}`)), 1),
                    ((_i = item.appUser) == null ? void 0 : _i.status) == 1 ? (openBlock(), createBlock("div", {
                      key: 0,
                      class: "during_order"
                    }, toDisplayString(_ctx.$t("market.subUntil")) + toDisplayString((_j = item.appUser) == null ? void 0 : _j.expireAt) + toDisplayString(_ctx.$t("market.expiredOn")), 1)) : createCommentVNode("", true),
                    ((_k = item.appUser) == null ? void 0 : _k.status) == 2 ? (openBlock(), createBlock("div", {
                      key: 1,
                      class: "delay_order"
                    }, toDisplayString(_ctx.$t("market.haveBeen")) + toDisplayString((_l = item.appUser) == null ? void 0 : _l.expireAt) + toDisplayString(_ctx.$t("market.expiredOn")), 1)) : createCommentVNode("", true)
                  ])
                ];
              }
            }),
            _: 2
          }, _parent));
        });
        _push(`<!--]--></div></div>`);
      }
      _push(`</div></main>`);
      if (isZH.value) {
        _push(ssrRenderComponent(customerService, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (isZH.value) {
        _push(ssrRenderComponent(_component_FooterNavZH, { class: "mobile_footer" }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (!isZH.value) {
        _push(ssrRenderComponent(_component_FooterNav, { class: "mobile_footer" }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (payShow.value) {
        _push(ssrRenderComponent(_component_el_dialog, {
          modelValue: payShow.value,
          "onUpdate:modelValue": ($event) => payShow.value = $event,
          class: "payPC",
          "show-close": false
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(_component_client_only, null, {}, _parent2, _scopeId));
            } else {
              return [
                createVNode(_component_client_only, null, {
                  default: withCtx(() => [
                    createVNode(pay, {
                      userInfo: userInfo.value,
                      subStatusDetail: subStatusDetail.value,
                      appTypes: unref(appTypes),
                      currentItem: currentItem.value,
                      onToAgreement: _ctx.toAgreement,
                      onClose: close,
                      onSubscribe: subscribe
                    }, null, 8, ["userInfo", "subStatusDetail", "appTypes", "currentItem", "onToAgreement"])
                  ]),
                  _: 1
                })
              ];
            }
          }),
          _: 1
        }, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(unref(Popup), {
        show: payShow.value,
        "onUpdate:show": ($event) => payShow.value = $event,
        round: "",
        closeable: "",
        class: "payMobile",
        position: "bottom",
        style: { height: "90%" }
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_client_only, null, {}, _parent2, _scopeId));
          } else {
            return [
              createVNode(_component_client_only, null, {
                default: withCtx(() => [
                  createVNode(payMobile, {
                    userInfo: userInfo.value,
                    subStatusDetail: subStatusDetail.value,
                    appTypes: unref(appTypes),
                    currentItem: currentItem.value,
                    onToAgreement: _ctx.toAgreement,
                    onClose: close
                  }, null, 8, ["userInfo", "subStatusDetail", "appTypes", "currentItem", "onToAgreement"])
                ]),
                _: 1
              })
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const index = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-084a0b6a"]]);

export { index as default };
//# sourceMappingURL=index.vue.mjs.map
