import { ref, reactive, resolveComponent, mergeProps, withCtx, createVNode, createTextVNode, toDisplayString, createBlock, openBlock, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrInterpolate, ssrRenderComponent } from 'vue/server-renderer';
import { s as sendEmailCode, r as register } from './requestDify.mjs';
import cookie from 'js-cookie';
import { ElMessage } from 'element-plus';
import { e as useI18n, f as useRoute, u as useRouter } from './server.mjs';
import { s as setInterval } from './interval.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper.mjs';
import 'axios';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import 'consola';
import 'node:path';
import 'node:crypto';
import 'vue-router';

const _sfc_main = {
  __name: "sign-up",
  __ssrInlineRender: true,
  setup(__props) {
    var _a;
    const { t, locale } = useI18n();
    const route = useRoute();
    useRouter();
    route.params.socialType;
    route.query.authCode;
    route.query.authState;
    const ruleFormRef = ref();
    const loginUrl = ref();
    const ruleForm = ref({
      email: "",
      password: "",
      emailCode: "",
      userName: ""
    });
    const isUp = ref(void 0);
    const rules = reactive({
      userName: [{ required: true, message: t("tool.username_cannot_be_empty"), trigger: "blur" }],
      password: [
        { required: true, message: t("tool.password_cannot_be_empty"), trigger: "blur" },
        { min: 8, max: 20, message: t("tool.pwdLength"), trigger: "blur" },
        {
          pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[a-zA-Z\d\W_]{8,20}$/,
          message: t("tool.password_includes_uppercase_lowercase_numbers_and_symbols"),
          trigger: "blur"
        }
      ],
      emailCode: [
        { required: true, message: t("tool.verification_code_cannot_be_empty"), trigger: "blur" },
        {
          min: 6,
          max: 6,
          message: t("tool.verification_code_must_be_6_digits"),
          trigger: "blur"
        }
      ],
      email: [
        { required: true, message: t("tool.email_address_cannot_be_empty"), trigger: "blur" },
        { type: "email", message: t("tool.please_enter_a_valid_email_address"), trigger: "blur" }
      ]
    });
    const isDisabled = ref(false);
    const countdown = ref(60);
    const btnText = ref(t("tool.send_verification_code"));
    cookie.get("userInfo") ? (_a = JSON.parse(cookie.get("userInfo"))) == null ? void 0 : _a.userId : "";
    const startCountdown = () => {
      isDisabled.value = true;
      btnText.value = `${countdown.value}${t("tool.retry_after_seconds")}`;
      setInterval();
    };
    const send = () => {
      if (!ruleForm.value.email) {
        ElMessage.error(t("tool.email_does_not_exist"));
        return;
      }
      let params = {
        email: ruleForm.value.email,
        type: "RegisterCode"
      };
      sendEmailCode(params).then((res) => {
        if (res) {
          startCountdown();
        }
      });
    };
    const registers = async (formEl) => {
      if (!formEl) return;
      await formEl.validate((valid, fields) => {
        if (valid) {
          register(ruleForm.value).then((res) => {
            if (res) {
              ElMessage({
                type: "success",
                message: t("tool.registersuccess")
              });
              setTimeout(() => {
                (void 0).href = isUp.value ? "/apps/login" : "login";
              }, 1e3);
            }
          });
        }
      });
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_el_form = resolveComponent("el-form");
      const _component_el_form_item = resolveComponent("el-form-item");
      const _component_el_input = resolveComponent("el-input");
      const _component_el_button = resolveComponent("el-button");
      const _component_el_link = resolveComponent("el-link");
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-background text-foreground antialiased min-h-screen flex items-center justify-center" }, _attrs))} data-v-5478f50f><div class="cl-rootBox cl-signUp-root justify-center" data-v-5478f50f><div class="cl-cardBox cl-signUp-start" data-v-5478f50f><div class="cl-card cl-signUp-start" data-v-5478f50f><div class="cl-header" data-v-5478f50f><div data-v-5478f50f><h1 class="cl-headerTitle" data-v-5478f50f>${ssrInterpolate(_ctx.$t(`tool.create_account`))}</h1><p class="cl-headerSubtitle" data-v-5478f50f>${ssrInterpolate(_ctx.$t(`tool.registration_greeting`))}</p></div></div><div class="cl-main" data-v-5478f50f><div class="cl-socialButtonsRoot" data-v-5478f50f><div class="cl-socialButtons" data-v-5478f50f><button class="cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google" data-v-5478f50f><span class="cl-socialButtonsBlockButton-d" data-v-5478f50f><span data-v-5478f50f><img src="https://img.medsci.cn/202412/cc7c7687b4804460a85d46c629132724-vcHTUHMGIylQ.png" class="cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google" alt="Sign in with Google" data-v-5478f50f></span><span class="cl-socialButtonsBlockButtonText" data-v-5478f50f>${ssrInterpolate(_ctx.$t(`tool.continue_with_google`))}</span></span></button></div><div class="cl-socialButtons" data-v-5478f50f><button class="cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google" data-v-5478f50f><span class="cl-socialButtonsBlockButton-d" data-v-5478f50f><span data-v-5478f50f><img src="https://img.medsci.cn/202412/f9bf95f463c04d3e801aa6e97ef3d4b8-OSsmrMWR677i.png" class="cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google" alt="Sign in with Google" data-v-5478f50f></span><span class="cl-socialButtonsBlockButtonText" data-v-5478f50f>${ssrInterpolate(_ctx.$t(`tool.continue_with_facebook`))}</span></span></button></div></div><div class="cl-dividerRow" data-v-5478f50f><div class="cl-dividerLine" data-v-5478f50f></div><p class="cl-dividerText" data-v-5478f50f>or</p><div class="cl-dividerLine" data-v-5478f50f></div></div><div class="cl-socialButtonsRoot" data-v-5478f50f>`);
      _push(ssrRenderComponent(_component_el_form, {
        ref_key: "ruleFormRef",
        ref: ruleFormRef,
        style: { "max-width": "600px" },
        model: ruleForm.value,
        rules,
        "label-width": "auto",
        class: "demo-ruleForm",
        "label-position": "left",
        size: _ctx.formSize,
        "status-icon": ""
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_el_form_item, {
              label: _ctx.$t("tool.username"),
              prop: "userName"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent(_component_el_input, {
                    modelValue: ruleForm.value.userName,
                    "onUpdate:modelValue": ($event) => ruleForm.value.userName = $event
                  }, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode(_component_el_input, {
                      modelValue: ruleForm.value.userName,
                      "onUpdate:modelValue": ($event) => ruleForm.value.userName = $event
                    }, null, 8, ["modelValue", "onUpdate:modelValue"])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_el_form_item, {
              label: _ctx.$t("tool.email"),
              prop: "email"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent(_component_el_input, {
                    modelValue: ruleForm.value.email,
                    "onUpdate:modelValue": ($event) => ruleForm.value.email = $event
                  }, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode(_component_el_input, {
                      modelValue: ruleForm.value.email,
                      "onUpdate:modelValue": ($event) => ruleForm.value.email = $event
                    }, null, 8, ["modelValue", "onUpdate:modelValue"])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_el_form_item, {
              label: _ctx.$t("tool.verification_code"),
              prop: "emailCode"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent(_component_el_input, {
                    modelValue: ruleForm.value.emailCode,
                    "onUpdate:modelValue": ($event) => ruleForm.value.emailCode = $event
                  }, {
                    append: withCtx((_3, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(ssrRenderComponent(_component_el_button, {
                          onClick: send,
                          disabled: isDisabled.value,
                          type: "primary"
                        }, {
                          default: withCtx((_4, _push5, _parent5, _scopeId4) => {
                            if (_push5) {
                              _push5(`${ssrInterpolate(btnText.value)}`);
                            } else {
                              return [
                                createTextVNode(toDisplayString(btnText.value), 1)
                              ];
                            }
                          }),
                          _: 1
                        }, _parent4, _scopeId3));
                      } else {
                        return [
                          createVNode(_component_el_button, {
                            onClick: send,
                            disabled: isDisabled.value,
                            type: "primary"
                          }, {
                            default: withCtx(() => [
                              createTextVNode(toDisplayString(btnText.value), 1)
                            ]),
                            _: 1
                          }, 8, ["disabled"])
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode(_component_el_input, {
                      modelValue: ruleForm.value.emailCode,
                      "onUpdate:modelValue": ($event) => ruleForm.value.emailCode = $event
                    }, {
                      append: withCtx(() => [
                        createVNode(_component_el_button, {
                          onClick: send,
                          disabled: isDisabled.value,
                          type: "primary"
                        }, {
                          default: withCtx(() => [
                            createTextVNode(toDisplayString(btnText.value), 1)
                          ]),
                          _: 1
                        }, 8, ["disabled"])
                      ]),
                      _: 1
                    }, 8, ["modelValue", "onUpdate:modelValue"])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_el_form_item, {
              label: _ctx.$t("tool.password"),
              prop: "password",
              style: { "padding-bottom": "20px" }
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent(_component_el_input, {
                    modelValue: ruleForm.value.password,
                    "onUpdate:modelValue": ($event) => ruleForm.value.password = $event,
                    "show-password": "true"
                  }, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode(_component_el_input, {
                      modelValue: ruleForm.value.password,
                      "onUpdate:modelValue": ($event) => ruleForm.value.password = $event,
                      "show-password": "true"
                    }, null, 8, ["modelValue", "onUpdate:modelValue"])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_el_form_item, null, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<div class="cl-internal-1pnppin" data-v-5478f50f${_scopeId2}><div id="clerk-captcha" class="cl-internal-3s7k9k" data-v-5478f50f${_scopeId2}></div><div class="cl-internal-742eeh" data-v-5478f50f${_scopeId2}>`);
                  _push3(ssrRenderComponent(_component_el_button, {
                    class: "cl-formButtonPrimary cl-button 🔒️ cl-internal-ttumny",
                    onClick: ($event) => registers(ruleFormRef.value)
                  }, {
                    default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`<span class="cl-internal-2iusy0" data-v-5478f50f${_scopeId3}>${ssrInterpolate(_ctx.$t("tool.continue"))}<svg class="cl-buttonArrowIcon 🔒️ cl-internal-1c4ikgf" data-v-5478f50f${_scopeId3}><path fill="currentColor" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="m7.25 5-3.5-2.25v4.5L7.25 5Z" data-v-5478f50f${_scopeId3}></path></svg></span>`);
                      } else {
                        return [
                          createVNode("span", { class: "cl-internal-2iusy0" }, [
                            createTextVNode(toDisplayString(_ctx.$t("tool.continue")), 1),
                            (openBlock(), createBlock("svg", { class: "cl-buttonArrowIcon 🔒️ cl-internal-1c4ikgf" }, [
                              createVNode("path", {
                                fill: "currentColor",
                                stroke: "currentColor",
                                "stroke-linecap": "round",
                                "stroke-linejoin": "round",
                                "stroke-width": "1.5",
                                d: "m7.25 5-3.5-2.25v4.5L7.25 5Z"
                              })
                            ]))
                          ])
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(`</div></div>`);
                } else {
                  return [
                    createVNode("div", { class: "cl-internal-1pnppin" }, [
                      createVNode("div", {
                        id: "clerk-captcha",
                        class: "cl-internal-3s7k9k"
                      }),
                      createVNode("div", { class: "cl-internal-742eeh" }, [
                        createVNode(_component_el_button, {
                          class: "cl-formButtonPrimary cl-button 🔒️ cl-internal-ttumny",
                          onClick: ($event) => registers(ruleFormRef.value)
                        }, {
                          default: withCtx(() => [
                            createVNode("span", { class: "cl-internal-2iusy0" }, [
                              createTextVNode(toDisplayString(_ctx.$t("tool.continue")), 1),
                              (openBlock(), createBlock("svg", { class: "cl-buttonArrowIcon 🔒️ cl-internal-1c4ikgf" }, [
                                createVNode("path", {
                                  fill: "currentColor",
                                  stroke: "currentColor",
                                  "stroke-linecap": "round",
                                  "stroke-linejoin": "round",
                                  "stroke-width": "1.5",
                                  d: "m7.25 5-3.5-2.25v4.5L7.25 5Z"
                                })
                              ]))
                            ])
                          ]),
                          _: 1
                        }, 8, ["onClick"])
                      ])
                    ])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
          } else {
            return [
              createVNode(_component_el_form_item, {
                label: _ctx.$t("tool.username"),
                prop: "userName"
              }, {
                default: withCtx(() => [
                  createVNode(_component_el_input, {
                    modelValue: ruleForm.value.userName,
                    "onUpdate:modelValue": ($event) => ruleForm.value.userName = $event
                  }, null, 8, ["modelValue", "onUpdate:modelValue"])
                ]),
                _: 1
              }, 8, ["label"]),
              createVNode(_component_el_form_item, {
                label: _ctx.$t("tool.email"),
                prop: "email"
              }, {
                default: withCtx(() => [
                  createVNode(_component_el_input, {
                    modelValue: ruleForm.value.email,
                    "onUpdate:modelValue": ($event) => ruleForm.value.email = $event
                  }, null, 8, ["modelValue", "onUpdate:modelValue"])
                ]),
                _: 1
              }, 8, ["label"]),
              createVNode(_component_el_form_item, {
                label: _ctx.$t("tool.verification_code"),
                prop: "emailCode"
              }, {
                default: withCtx(() => [
                  createVNode(_component_el_input, {
                    modelValue: ruleForm.value.emailCode,
                    "onUpdate:modelValue": ($event) => ruleForm.value.emailCode = $event
                  }, {
                    append: withCtx(() => [
                      createVNode(_component_el_button, {
                        onClick: send,
                        disabled: isDisabled.value,
                        type: "primary"
                      }, {
                        default: withCtx(() => [
                          createTextVNode(toDisplayString(btnText.value), 1)
                        ]),
                        _: 1
                      }, 8, ["disabled"])
                    ]),
                    _: 1
                  }, 8, ["modelValue", "onUpdate:modelValue"])
                ]),
                _: 1
              }, 8, ["label"]),
              createVNode(_component_el_form_item, {
                label: _ctx.$t("tool.password"),
                prop: "password",
                style: { "padding-bottom": "20px" }
              }, {
                default: withCtx(() => [
                  createVNode(_component_el_input, {
                    modelValue: ruleForm.value.password,
                    "onUpdate:modelValue": ($event) => ruleForm.value.password = $event,
                    "show-password": "true"
                  }, null, 8, ["modelValue", "onUpdate:modelValue"])
                ]),
                _: 1
              }, 8, ["label"]),
              createVNode(_component_el_form_item, null, {
                default: withCtx(() => [
                  createVNode("div", { class: "cl-internal-1pnppin" }, [
                    createVNode("div", {
                      id: "clerk-captcha",
                      class: "cl-internal-3s7k9k"
                    }),
                    createVNode("div", { class: "cl-internal-742eeh" }, [
                      createVNode(_component_el_button, {
                        class: "cl-formButtonPrimary cl-button 🔒️ cl-internal-ttumny",
                        onClick: ($event) => registers(ruleFormRef.value)
                      }, {
                        default: withCtx(() => [
                          createVNode("span", { class: "cl-internal-2iusy0" }, [
                            createTextVNode(toDisplayString(_ctx.$t("tool.continue")), 1),
                            (openBlock(), createBlock("svg", { class: "cl-buttonArrowIcon 🔒️ cl-internal-1c4ikgf" }, [
                              createVNode("path", {
                                fill: "currentColor",
                                stroke: "currentColor",
                                "stroke-linecap": "round",
                                "stroke-linejoin": "round",
                                "stroke-width": "1.5",
                                d: "m7.25 5-3.5-2.25v4.5L7.25 5Z"
                              })
                            ]))
                          ])
                        ]),
                        _: 1
                      }, 8, ["onClick"])
                    ])
                  ])
                ]),
                _: 1
              })
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div></div><div class="cl-footer 🔒️ cl-internal-4x6jej" data-v-5478f50f><div class="cl-footerAction cl-footerAction__signUp cl-internal-1rpdi70" data-v-5478f50f><span class="cl-footerActionText cl-internal-kyvqj0" data-localization-key="signUp.start.actionText" data-v-5478f50f>${ssrInterpolate(_ctx.$t("tool.alreadyhaveanaccount"))}</span>`);
      _push(ssrRenderComponent(_component_el_link, {
        href: loginUrl.value,
        class: "cl-footerActionLink"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`${ssrInterpolate(_ctx.$t("tool.signIn"))}`);
          } else {
            return [
              createTextVNode(toDisplayString(_ctx.$t("tool.signIn")), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div></div></div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/sign-up.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const signUp = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-5478f50f"]]);

export { signUp as default };
//# sourceMappingURL=sign-up.vue.mjs.map
