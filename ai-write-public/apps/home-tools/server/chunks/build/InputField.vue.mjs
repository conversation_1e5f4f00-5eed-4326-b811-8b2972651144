import { ref, watch, resolveComponent, mergeProps, unref, withCtx, createBlock, openBlock, Fragment, renderList, createVNode, createTextVNode, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrInterpolate, ssrRenderComponent, ssrRenderList } from 'vue/server-renderer';
import { u as upload } from './requestDify.mjs';
import cookie from 'js-cookie';
import { useRoute, useRouter } from 'vue-router';
import { UploadFilled } from '@element-plus/icons-vue';
import { g as getDefaultLanguageCode } from './commonJs.mjs';
import { e as useI18n } from './server.mjs';
import 'axios';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import 'consola';
import 'node:path';
import 'node:crypto';
import 'element-plus';

const _sfc_main = {
  __name: "InputField",
  __ssrInlineRender: true,
  props: {
    type: { type: String, required: true },
    label: { type: String, required: true },
    // value: { type: [String, Number], required: true },
    required: { type: Boolean, required: true },
    placeholder: { type: String, required: true },
    max_length: { type: Number, required: false },
    options: { type: Array, required: false },
    fileVerify: { type: Array, required: false, default: () => [""] },
    currentItem: { type: Object, required: false }
  },
  emits: ["update:value"],
  setup(__props, { expose: __expose, emit: __emit }) {
    var _a;
    const userInfo = ref(cookie.get("userInfo") ? JSON.parse(cookie.get("userInfo")) : {});
    useRoute();
    useRouter();
    const { locale } = useI18n();
    const fileList = ref([]);
    const fileType = ref({
      "jpg": "image",
      "jpeg": "image",
      "png": "image",
      "webp": "image",
      "gif": "image",
      "svg": "image",
      "mp4": "video",
      "mov": "video",
      "mpeg": "video",
      "mpga": "video",
      "mp3": "audio",
      "m4a": "audio",
      "wav": "audio",
      "webm": "audio",
      "amr": "audio",
      "txt": "document",
      "markdown": "document",
      "md": "document",
      "mdx": "document",
      "pdf": "document",
      "html": "document",
      "htm": "document",
      "xlsx": "document",
      "xls": "document",
      "docx": "document",
      "csv": "document",
      "eml": "document",
      "msg": "document",
      "pptx": "document",
      "xml": "document",
      "epub": "document"
    });
    const props = __props;
    const inputValue = ref(((_a = props.options) == null ? void 0 : _a[0]) || "");
    const type = props.type;
    const fileVerify = props.fileVerify;
    const label = props.label;
    const required = props.required;
    const max_length = props.max_length;
    const options = props.options;
    if (type == "file") {
      inputValue.value = null;
    }
    if (type == "file-list") {
      inputValue.value = [];
    }
    const acceptList = { "image": [".jpg", ".jpeg", ".png", ".webp", ".gif", ".svg"], "video": [".mp4", ".mov", ".mpeg", ".mpga"], "audio": [".mp3", ".m4a", ".wav", ".webm", ".amr"], "document": [".txt", ".markdown", ".md", ".mdx", ".pdf", ".html", ".htm", ".xlsx", ".xls", ".docx", ".csv", ".eml", ".msg", ".pptx", ".xml", ".epub"] };
    const checkFileType = () => {
      let strList = "";
      fileVerify.forEach((i, index) => {
        if (index < fileVerify.length - 1) {
          strList += acceptList[i].join(",") + ",";
        } else {
          strList += acceptList[i].join(",");
        }
      });
      return strList;
    };
    const emit = __emit;
    const focusSubscribe = async () => {
      var _a2, _b, _c, _d;
      if (!cookie.get("userInfo")) {
        cookie.remove("yudaoToken", { domain: "ai.medsci.cn" });
        cookie.remove("yudaoToken", { domain: "ai.medon.com.cn" });
        cookie.remove("yudaoToken", { domain: ".medsci.cn" });
        cookie.remove("yudaoToken", { domain: ".medon.com.cn" });
        cookie.remove("yudaoToken", { domain: "localhost" });
        localStorage.removeItem("hasuraToken");
        const language = getDefaultLanguageCode(locale.value);
        if (!language || language == "zh") {
          (void 0).addLoginDom();
        } else {
          (void 0).href = (void 0).origin + "/" + locale.value + "/login";
        }
        Array.from((void 0).getElementsByTagName("input")).forEach((i) => i.blur());
        Array.from((void 0).getElementsByTagName("textarea")).forEach((i) => i.blur());
        return false;
      }
      if (!((_b = (_a2 = props.currentItem) == null ? void 0 : _a2.appUser) == null ? void 0 : _b.status) || ((_d = (_c = props.currentItem) == null ? void 0 : _c.appUser) == null ? void 0 : _d.status) == 2) {
        emit("payShowStatus", true);
        Array.from((void 0).getElementsByTagName("input")).forEach((i) => i.blur());
        Array.from((void 0).getElementsByTagName("textarea")).forEach((i) => i.blur());
        return false;
      }
      return true;
    };
    const handleSuccess = (res, file, fileList2) => {
    };
    const handleRemove = () => {
      inputValue.value = "";
    };
    const handlePreview = (file) => {
    };
    const beforeRemove = (file, fileList2) => {
    };
    const handleExceed = (files, fileList2) => {
    };
    const customRequest = async (options2) => {
      var _a2;
      const isLoginAndSub = await focusSubscribe();
      if (!isLoginAndSub) {
        return false;
      }
      const { file, onSuccess, onError } = options2;
      const formData = new FormData();
      formData.append("file", file);
      formData.append("appId", (_a2 = props.currentItem) == null ? void 0 : _a2.dAppUuid);
      formData.append("user", userInfo.value.userName);
      try {
        const res = await upload(formData);
        if (type == "file-list") {
          inputValue.value.push({
            "type": fileType.value[res.extension],
            "transfer_method": "local_file",
            "url": "",
            "upload_file_id": res.id
          });
        } else {
          inputValue.value = {
            "type": fileType.value[res.extension],
            "transfer_method": "local_file",
            "url": "",
            "upload_file_id": res.id
          };
        }
        onSuccess(res, file);
      } catch (error) {
        onError(error);
      }
      return false;
    };
    const updateMessage = () => {
      if (options && options.length > 0) {
        inputValue.value = options[0];
      } else if (type == "file") {
        inputValue.value = null;
        fileList.value = [];
      } else if (type == "file-list") {
        inputValue.value = [];
        fileList.value = [];
      } else {
        inputValue.value = "";
      }
    };
    __expose({
      updateMessage
    });
    watch(inputValue, (newValue) => {
      emit("update:value", newValue);
    }, { immediate: true, deep: true });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_el_input = resolveComponent("el-input");
      const _component_el_select = resolveComponent("el-select");
      const _component_el_option = resolveComponent("el-option");
      const _component_el_upload = resolveComponent("el-upload");
      const _component_el_button = resolveComponent("el-button");
      const _component_el_icon = resolveComponent("el-icon");
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "p-3 flex-1 rounded-md" }, _attrs))}><h6 class="text-[14px] font-bold mb-2 text-gray-600">${ssrInterpolate(unref(label))}</h6>`);
      if (unref(type) === "paragraph" || unref(type) === "text-input") {
        _push(ssrRenderComponent(_component_el_input, {
          onFocus: focusSubscribe,
          modelValue: inputValue.value,
          "onUpdate:modelValue": ($event) => inputValue.value = $event,
          type: unref(type) === "paragraph" ? "textarea" : "text",
          rows: 5,
          required: unref(required),
          placeholder: `${unref(label)}`,
          "show-word-limit": "",
          resize: "none",
          maxlength: unref(max_length)
        }, null, _parent));
      } else if (unref(type) === "number") {
        _push(ssrRenderComponent(_component_el_input, {
          modelValue: inputValue.value,
          "onUpdate:modelValue": ($event) => inputValue.value = $event,
          modelModifiers: { number: true },
          onFocus: focusSubscribe,
          type: "number",
          required: unref(required),
          placeholder: `${unref(label)}`,
          resize: "none"
        }, null, _parent));
      } else if (unref(type) === "select") {
        _push(ssrRenderComponent(_component_el_select, {
          onChange: focusSubscribe,
          modelValue: inputValue.value,
          "onUpdate:modelValue": ($event) => inputValue.value = $event,
          required: unref(required),
          placeholder: `${unref(label)}`
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`<!--[-->`);
              ssrRenderList(unref(options), (option) => {
                _push2(ssrRenderComponent(_component_el_option, {
                  key: option,
                  label: option,
                  value: option
                }, null, _parent2, _scopeId));
              });
              _push2(`<!--]-->`);
            } else {
              return [
                (openBlock(true), createBlock(Fragment, null, renderList(unref(options), (option) => {
                  return openBlock(), createBlock(_component_el_option, {
                    key: option,
                    label: option,
                    value: option
                  }, null, 8, ["label", "value"]);
                }), 128))
              ];
            }
          }),
          _: 1
        }, _parent));
      } else if (unref(type) === "file" || unref(type) === "file-list") {
        _push(ssrRenderComponent(_component_el_upload, {
          "file-list": fileList.value,
          "onUpdate:fileList": ($event) => fileList.value = $event,
          class: "upload-demo",
          multiple: "",
          "show-file-list": "",
          "on-preview": handlePreview,
          "on-remove": handleRemove,
          "before-remove": beforeRemove,
          limit: unref(max_length),
          accept: checkFileType(),
          "auto-upload": true,
          "on-Success": handleSuccess,
          "http-request": customRequest,
          "on-exceed": handleExceed
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(_component_el_button, {
                disabled: unref(type) === "file" ? fileList.value.length == 1 : fileList.value.length == unref(max_length) ? true : false
              }, {
                default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(ssrRenderComponent(_component_el_icon, {
                      class: "el-icon--upload",
                      style: { "margin-right": "5px", "font-size": "16px" }
                    }, {
                      default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                        if (_push4) {
                          _push4(ssrRenderComponent(unref(UploadFilled), null, null, _parent4, _scopeId3));
                        } else {
                          return [
                            createVNode(unref(UploadFilled))
                          ];
                        }
                      }),
                      _: 1
                    }, _parent3, _scopeId2));
                    _push3(`从本地上传`);
                  } else {
                    return [
                      createVNode(_component_el_icon, {
                        class: "el-icon--upload",
                        style: { "margin-right": "5px", "font-size": "16px" }
                      }, {
                        default: withCtx(() => [
                          createVNode(unref(UploadFilled))
                        ]),
                        _: 1
                      }),
                      createTextVNode("从本地上传")
                    ];
                  }
                }),
                _: 1
              }, _parent2, _scopeId));
            } else {
              return [
                createVNode(_component_el_button, {
                  disabled: unref(type) === "file" ? fileList.value.length == 1 : fileList.value.length == unref(max_length) ? true : false
                }, {
                  default: withCtx(() => [
                    createVNode(_component_el_icon, {
                      class: "el-icon--upload",
                      style: { "margin-right": "5px", "font-size": "16px" }
                    }, {
                      default: withCtx(() => [
                        createVNode(unref(UploadFilled))
                      ]),
                      _: 1
                    }),
                    createTextVNode("从本地上传")
                  ]),
                  _: 1
                }, 8, ["disabled"])
              ];
            }
          }),
          _: 1
        }, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/tool/components/InputField.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=InputField.vue.mjs.map
