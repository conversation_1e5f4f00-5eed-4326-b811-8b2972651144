<!DOCTYPE html><html lang="en">
<head>

  <meta charset="UTF-8">

  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

  <title>科研机遇洞察平台 | 学术发展智能匹配</title>

  <link rel="icon" type="image/x-icon" href="../../images/favicon.ico">

  <meta name="keywords" content="科研机遇洞察,学术平台匹配,期刊推荐,研究转化,AI学术专家">

  <meta name="description" content="科研发展机遇智能分析工具！深度解读研究成果，精准匹配目标期刊/平台，挖掘学术合作与转化机遇，助力职业发展">

  <meta property="og:type" content="website">

  <meta property="og:title" content="科研机遇洞察平台 | 学术发展智能匹配">

  <meta property="og:description" content="科研发展机遇智能分析工具！深度解读研究成果，精准匹配目标期刊/平台，挖掘学术合作与转化机遇，助力职业发展">

  <meta property="og:image" content="https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png">

  <meta name="twitter:card" content="summary_large_image">

  <meta name="twitter:title" content="科研机遇洞察平台 | 学术发展智能匹配">

  <meta name="twitter:description" content="科研发展机遇智能分析工具！深度解读研究成果，精准匹配目标期刊/平台，挖掘学术合作与转化机遇，助力职业发展">

  <meta name="twitter:image" content="https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png">

  <script type="module" crossorigin="" src="/zh/elavax-base/assets/index-TJ9fpb-G.js"></script>

  <link rel="stylesheet" crossorigin="" href="/zh/elavax-base/assets/index-CAQhjvyP.css">

  <script type="application/ld+json">{ "@context": "https://schema.org", "@type": "WebApplication", "name": "科研机遇洞察平台 | 学术发展智能匹配", "description": "科研发展机遇智能分析工具！深度解读研究成果，精准匹配目标期刊/平台，挖掘学术合作与转化机遇，助力职业发展", "url": "https://ai.medsci.cn/zh/elavax-base", "applicationCategory": "HealthApplication", "operatingSystem": "Web", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "CNY" } }</script><style> /* 预渲染内容控制 */ .prerender-content { /* 默认显示，便于SEO */ display: block; } /* 当React应用加载后隐藏预渲染内容 */ .react-loaded .prerender-content { display: none; } /* 防止预渲染内容闪烁的过渡效果 */ .prerender-content { transition: opacity 0.2s ease-in-out; } .react-loaded .prerender-content { opacity: 0; pointer-events: none; } </style>
</head>


<body>
<div id="root"><div class="prerender-content" data-prerendered="true"><div class="flex h-screen" style="background-color: var(--bg-main);"><div class="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"></div><div class=" fixed md:relative inset-y-0 left-0 z-40 md:z-auto transform translate-x-0 transition-transform duration-300 ease-in-out "><div class="w-60 h-full flex flex-col p-4 slide-in bg-white shadow-xl transition-all duration-300" style="background-color: var(--bg-sidebar);"><div class="flex justify-between items-center mb-6 md:hidden"><h2 class="text-lg font-semibold text-gray-900"></h2><button class="p-2 text-gray-500 hover:text-gray-700 rounded-lg transition-colors">✕</button></div><div class="flex items-center gap-2 md:gap-3 mb-6"><button class="flex items-center gap-3 transition-opacity cursor-pointer"><div class="w-10 h-10 md:w-12 md:h-12 rounded-xl flex items-center justify-center"><img src="https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png" alt="​​ElavaX​-Base" class="w-full h-full object-cover rounded-xl"></div><span class="font-bold text-gray-800 text-lg sm:text-xl md:text-2xl tracking-tight">​​ElavaX​-Base</span></button></div><button class="flex items-center gap-3 w-full p-3 bg-white rounded-xl text-gray-600 hover:bg-gray-50 transition-colors mb-6 btn-hover shadow-sm border border-gray-200" style="display: none;"><div class="nav-icon"><span>🔍</span></div><span class="text-sm font-medium">搜索</span><span class="ml-auto text-xs bg-gray-100 px-2 py-1 rounded-lg">⌘+K</span></button><div class="mb-1"><button class="w-full flex items-center gap-3 p-3 text-gray-700 hover:text-blue-600 rounded-lg transition-all duration-200 font-medium"><div class="flex items-center justify-center w-5 h-5"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg></div><span>新建对话</span></button></div><div class="mb-1"><button class="w-full flex items-center gap-3 p-3 text-gray-700 hover:text-blue-600 rounded-lg transition-all duration-200 font-medium"><div class="flex items-center justify-center w-5 h-5"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path></svg></div><span>知识库</span></button></div><div class="flex-1 mb-6"><div class="relative"><div class="flex items-center gap-3 py-2 px-3 cursor-pointer mb-0 text-gray-700 rounded-lg transition-all duration-200 relative group"><div class="flex items-center justify-center w-5 h-5"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-600 group-hover:text-blue-600 transition-colors duration-200" aria-label="历史记录"><circle cx="12" cy="12" r="10"></circle><polyline points="12,6 12,12 16,14"></polyline></svg></div><span class="font-medium text-sm text-gray-600 group-hover:text-blue-600 truncate flex-1 transition-colors duration-200">对话历史</span></div><div class="ml-2 mt-1 pl-2"><div class="relative"><ul class="relative pl-0 list-none m-0"><li class="relative py-2 text-sm text-gray-500 pl-6">暂无历史记录</li></ul></div></div></div></div><div class="text-xs leading-5 mt-auto border-t border-gray-200 pt-3 "><div class="mb-2 font-medium flex items-center gap-6" style="color: rgb(168, 174, 185);"><div class="flex items-center cursor-pointer hover:text-blue-600 transition-colors duration-200"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="transition-colors duration-200"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg><span class="ml-1 transition-colors duration-200">关于我们</span></div><div class="flex items-center cursor-pointer hover:text-blue-600 transition-colors duration-200"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="transition-colors duration-200"><circle cx="12" cy="12" r="10"></circle><path d="m9,9 0,0 a3,3 0 0,1 6,0c0,2 -3,3 -3,3"></path><path d="m9,17 6,0"></path></svg><span class="ml-1 transition-colors duration-200">FAQ</span></div><div class="flex items-center cursor-pointer hover:text-blue-600 transition-colors duration-200" title="意见反馈"><svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="transition-colors duration-200"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg></div></div><div class="cursor-pointer transition-colors"><a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener noreferrer" class="hover:text-blue-600 hover:underline transition-all duration-200 font-medium" style="color: rgb(215, 219, 224);">沪ICP备14018916号-1</a></div><div class="cursor-pointer transition-colors mt-1"><a href="https://www.medsci.cn/about/index.do?id=18" target="_blank" rel="noopener noreferrer" class="hover:text-blue-600 hover:underline transition-all duration-200 font-medium" style="color: rgb(215, 219, 224);">隐私政策</a></div></div></div></div><main class="flex-1 flex flex-col w-full md:w-auto"><header class="top-0 left-0 right-0 z-100 md:z-50 h-16 border-gray-100 backdrop-blur-md bg-opacity-95 flex items-center justify-between px-4 md:px-8" style="background-color: var(--bg-main);"><div class="flex items-center gap-2 md:gap-3"><button class="h-[40px] w-[30px] flex items-center justify-center text-gray-600 hover:text-gray-900 rounded-lg transition-colors focus:outline-none"><span class="text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="1.5em" height="1.5em" fill="none" viewBox="0 0 20 20" class="btn-icon"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.5 3h-15m8 7h-8m8 7h-8m15-7-3.484 3.5L17.5 17"></path></svg></span></button></div><div class="flex-1"></div><div class="flex items-center flex-shrink-0 space-x-4 md:space-x-6"><div class="relative inline-block h-[40px]"><button class=" flex items-center space-x-2 h-[40px] px-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 group " aria-label="切换语言" aria-expanded="false" aria-haspopup="true"><span class="text-base" role="img" aria-label="中文">🇨🇳</span><svg class="w-3.5 h-3.5 text-gray-400 group-hover:text-gray-600 transition-all duration-200 " fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></button></div><div class="relative flex justify-center"><button class="px-3 py-2 bg-gradient-to-r to-indigo-600 text-sm font-medium rounded-lg transition-all duration-200 hover:scale-105">登录</button></div></div></header><div class="pt-16 md:pt-20 flex-1 overflow-y-auto"><div class="flex-1 flex flex-col" style="background-color: var(--bg-main);"><div class="flex-1 flex flex-col pt-4 px-4 md:pt-6 md:px-6 lg:pt-8 lg:px-8"><div class="flex flex-col items-center justify-start"><div class="w-full max-w-6xl"><div class="text-center mb-6 md:mb-8"><div class="flex flex-col items-center gap-2 mb-3"><div class="flex items-center gap-3"><h2 class="text-xl md:text-2xl font-bold text-gray-900 flex-1 min-w-0">~Hi，欢迎体验 ​​ElavaX​-Base</h2><div class="w-8 h-8 min-w-[32px] min-h-[32px] flex items-center justify-center flex-shrink-0"><img src="https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png" alt="​​ElavaX​-Base" class="w-8 h-8 object-contain"></div></div></div><p class="text-sm md:text-lg text-gray-600 mb-6 md:mb-10 whitespace-pre-line">精准剖析，深度洞察，赋能您的卓越创见</p></div><div class="relative transition-all duration-300 ease-in-out"><div class="relative bg-white shadow-xl mb-6 md:mb-8 rounded-2xl border border-gray-200 hover:shadow-2xl transition-shadow duration-300 ease-in-out"><div class="pt-4 px-4"><div style="display: flex; align-items: flex-start;"><img src="https://img.medsci.cn/202507/41d58de3863f47f0a9af7df896412ffd-sTnD3VvQ1426.png" class="w-7 h-7" style="background-color: white; position: relative; z-index: 10; padding: 4px; border-radius: 2px;"><span class="text-blue-600" style="white-space: nowrap; font-size: 16px; font-weight: 500; line-height: 1.6; background-color: white; position: relative; z-index: 10; padding: 1px 2px; border-radius: 2px;">​​ElavaX​-Base</span></div><div style="margin-top: -26px; margin-left: 0px;"><textarea class="w-full border-0 resize-none focus:outline-none focus:border-transparent text-gray-800 placeholder-gray-500 transition-all duration-300 ease-in-out" placeholder="请输入您的需求..." style="padding: 0px; min-height: 140px; font-size: 16px; line-height: 1.6; outline: none; border: none; box-shadow: none; background: transparent; text-indent: 129.438px; margin-left: 0px;"></textarea></div></div><div class="flex items-center justify-between p-2 border-gray-100"><div class="flex items-center gap-3"><div class="relative"><button class="flex items-center gap-2 px-4 py-2.5 bg-gray-100 hover:bg-gray-200 rounded-xl text-sm font-medium text-gray-700 transition-colors duration-200 ease-in-out shadow-sm hover:shadow-md scene-select-button min-h-[44px]"><img class="w-4 h-4 object-contain" src="https://img.medsci.cn/202507/5a4f482dc99f49938f229862f6192c3a-RX57Duz1XbGU.png"><span class="text-sm hidden md:inline">通用场景</span><span class="text-xs transform transition-transform duration-200"><svg class="w-3 h-3 text-gray-500 group-hover:text-indigo-600 transition-colors duration-300 transition-transform " fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></span></button></div><div class="tooltip-container "><button class="flex items-center gap-2 px-4 py-2.5 bg-gray-100 hover:bg-gray-200 rounded-xl text-sm font-medium text-gray-700 cursor-pointer transition-colors duration-200 ease-in-out shadow-sm hover:shadow-md min-h-[44px]"><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path></svg><span class="hidden md:inline">上传文件</span></button></div></div><div class="flex items-center gap-3"><div class="text-xs text-gray-400 hidden sm:block">Enter发送</div><button disabled="" class="relative flex items-center justify-center w-10 h-10 rounded-full text-white focus:outline-none focus:ring-2 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed group shadow-lg hover:shadow-xl transition-all duration-200 ease-out transform hover:scale-105 bg-gradient-to-br from-purple-500 to-blue-500 hover:from-purple-400 hover:to-blue-400 focus:ring-purple-500 hover:shadow-purple-500/30"><div class="flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" class="group-hover:animate-[takeoff_1.5s_ease-out_infinite] transition-transform duration-200"><path d="M22 2L11 13"></path><path d="M22 2L15 22L11 13L2 9L22 2Z"></path></svg></div></button></div></div></div><input type="file" accept=".txt,.md,.mdx,.markdown,.pdf,.html,.xlsx,.xls,.doc,.docx,.csv,.eml,.msg,.pptx,.ppt,.xml,.epub" class="sr-only" tabindex="-1" style="display: none; position: absolute; left: -9999px; top: -9999px; opacity: 0; visibility: hidden; width: 0px; height: 0px;"></div><div class="mb-8"><div class="md:hidden"><div class="overflow-x-auto scrollbar-hide"><div class="flex space-x-3 px-4 pb-2" style="width: max-content;"><button class=" relative p-3 rounded-xl border transition-all duration-200 hover:shadow-sm flex-shrink-0 bg-white border-gray-100 hover:border-gray-200 " style="width: 160px;"><div class="absolute top-0 right-0 text-white text-xs px-1.5 py-0.5 bg-gray-400" style="border-top-right-radius: 12px; border-bottom-left-radius: 6px; font-size: 10px; line-height: 1.2;">免费体验</div><div class="flex justify-center mb-2 mt-1"><img src="https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png" alt="NovaX-Base" class="w-7 h-7"></div><div class="text-sm font-medium text-center text-gray-700">NovaX-Base</div></button><button class=" relative p-3 rounded-xl border transition-all duration-200 hover:shadow-sm flex-shrink-0 bg-white border-gray-100 hover:border-gray-200 " style="width: 160px;"><div class="absolute top-0 right-0 text-white text-xs px-1.5 py-0.5 bg-gray-400" style="border-top-right-radius: 12px; border-bottom-left-radius: 6px; font-size: 10px; line-height: 1.2;">专家</div><div class="flex justify-center mb-2 mt-1"><img src="https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png" alt="NovaX-Pro" class="w-7 h-7"></div><div class="text-sm font-medium text-center text-gray-700">NovaX-Pro</div></button><button class=" relative p-3 rounded-xl border transition-all duration-200 hover:shadow-sm flex-shrink-0 bg-white border-blue-200 bg-blue-25 shadow-sm " style="width: 160px; background-color: rgb(240, 247, 255);"><div class="absolute top-0 right-0 text-white text-xs px-1.5 py-0.5 bg-blue-500" style="border-top-right-radius: 12px; border-bottom-left-radius: 6px; font-size: 10px; line-height: 1.2;">免费体验</div><div class="flex justify-center mb-2 mt-1"><img src="https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png" alt="​​ElavaX​-Base" class="w-7 h-7"></div><div class="text-sm font-medium text-center text-blue-600">​​ElavaX​-Base</div></button><button class=" relative p-3 rounded-xl border transition-all duration-200 hover:shadow-sm flex-shrink-0 bg-white border-gray-100 hover:border-gray-200 " style="width: 160px;"><div class="absolute top-0 right-0 text-white text-xs px-1.5 py-0.5 bg-gray-400" style="border-top-right-radius: 12px; border-bottom-left-radius: 6px; font-size: 10px; line-height: 1.2;">专家</div><div class="flex justify-center mb-2 mt-1"><img src="https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png" alt="​​ElavaX​-Pro" class="w-7 h-7"></div><div class="text-sm font-medium text-center text-gray-700">​​ElavaX​-Pro</div></button></div></div></div><div class="hidden md:grid grid-cols-5 gap-4 px-0"><button class=" relative p-4 rounded-xl border transition-all duration-200 bg-white text-gray-700 dark:text-gray-200 hover:shadow-sm hover:bg-gray-50 dark:hover:bg-gray-800 "><div class=" absolute top-0 right-0 text-white text-xs px-2 py-1 bg-gray-400 " style="border-top-right-radius: 12px; border-bottom-left-radius: 8px; font-size: 11px; line-height: 1.2;">免费体验</div><div class="flex justify-center mb-2 mt-1"><img src="https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png" alt="NovaX-Base" class="w-8 h-8"></div><div class="text-sm font-medium text-center text-gray-700">NovaX-Base</div></button><button class=" relative p-4 rounded-xl border transition-all duration-200 bg-white text-gray-700 dark:text-gray-200 hover:shadow-sm hover:bg-gray-50 dark:hover:bg-gray-800 "><div class=" absolute top-0 right-0 text-white text-xs px-2 py-1 bg-gray-400 " style="border-top-right-radius: 12px; border-bottom-left-radius: 8px; font-size: 11px; line-height: 1.2;">专家</div><div class="flex justify-center mb-2 mt-1"><img src="https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png" alt="NovaX-Pro" class="w-8 h-8"></div><div class="text-sm font-medium text-center text-gray-700">NovaX-Pro</div></button><button class=" relative p-4 rounded-xl border transition-all duration-200 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 hover:shadow-sm "><div class=" absolute top-0 right-0 text-white text-xs px-2 py-1 bg-blue-500 " style="border-top-right-radius: 12px; border-bottom-left-radius: 8px; font-size: 11px; line-height: 1.2;">免费体验</div><div class="flex justify-center mb-2 mt-1"><img src="https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png" alt="​​ElavaX​-Base" class="w-8 h-8"></div><div class="text-sm font-medium text-center text-blue-600">​​ElavaX​-Base</div></button><button class=" relative p-4 rounded-xl border transition-all duration-200 bg-white text-gray-700 dark:text-gray-200 hover:shadow-sm hover:bg-gray-50 dark:hover:bg-gray-800 "><div class=" absolute top-0 right-0 text-white text-xs px-2 py-1 bg-gray-400 " style="border-top-right-radius: 12px; border-bottom-left-radius: 8px; font-size: 11px; line-height: 1.2;">专家</div><div class="flex justify-center mb-2 mt-1"><img src="https://ai-base.medsci.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png" alt="​​ElavaX​-Pro" class="w-8 h-8"></div><div class="text-sm font-medium text-center text-gray-700">​​ElavaX​-Pro</div></button></div></div><div class="mb-6 md:mb-8 px-3 md:px-0"><div class="text-center mb-4 md:mb-6"><h3 class="text-base md:text-lg font-semibold text-gray-800 mb-2">案例展示</h3><p class="text-xs md:text-sm text-gray-600">查看其他用户使用小智智能体创作的优秀文档案例</p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-6"><a href="http://localhost:3000/zh/cases/7b8c9d0e1f2a" title="Endocrinology Frontiers" class="case-card block no-underline hover:no-underline"><div class="case-card-image h-52 md:h-40"><img src="https://img.medsci.cn/20240508/1715158312120_92910.png" alt="Endocrinology Frontiers" class="w-full h-full object-cover"></div><h4 class="font-medium text-gray-900 mb-1 md:mb-2 text-xs md:text-sm">Endocrinology Frontiers</h4><p class="text-xs text-gray-600 leading-relaxed">对学术稿件进行详细评审，提供质量改进建议。</p></a><a href="http://localhost:3000/zh/cases/5cc79098588" title="青年项目" class="case-card block no-underline hover:no-underline"><div class="case-card-image h-52 md:h-40"><img src="https://img.medsci.cn/202401012/1728697600406_8538692.png" alt="青年项目" class="w-full h-full object-cover"></div><h4 class="font-medium text-gray-900 mb-1 md:mb-2 text-xs md:text-sm">青年项目</h4><p class="text-xs text-gray-600 leading-relaxed">通过去泛素化调控HIF-1α稳定性，进而促进卒中后血管新生的分子机制，旨在揭示USP21/HIF-1α信号轴在脑缺血修复中的关键作用，并探索其作为治疗靶点的潜在价值。</p></a><a href="http://localhost:3000/zh/cases/4058e3629" title="评估需求" class="case-card block no-underline hover:no-underline"><div class="case-card-image h-52 md:h-40"><img src="https://img.medsci.cn/20241216/1734342493683_8538692.jpg" alt="评估需求" class="w-full h-full object-cover"></div><h4 class="font-medium text-gray-900 mb-1 md:mb-2 text-xs md:text-sm">评估需求</h4><p class="text-xs text-gray-600 leading-relaxed">需求评估通过系统分析，识别知识、技能或资源方面的差距，为医疗和科研领域的有效规划与干预提供依据</p></a><a href="http://localhost:3000/zh/cases/a68ce342b" title="Hepatology Update" class="case-card block no-underline hover:no-underline"><div class="case-card-image h-52 md:h-40"><img src="https://img.medsci.cn/20220719/1658202183571_5552845.jpg" alt="Hepatology Update" class="w-full h-full object-cover"></div><h4 class="font-medium text-gray-900 mb-1 md:mb-2 text-xs md:text-sm">Hepatology Update</h4><p class="text-xs text-gray-600 leading-relaxed">提供肝病研究与临床诊疗最新进展的简明综述，助力医学专业人士及时掌握领域动态。</p></a></div></div><div class="text-center text-gray-500 text-xs md:text-sm pb-6 md:pb-8 px-4 md:px-0"><p class="cursor-pointer hover:text-blue-600 transition-all duration-300 hover:scale-105 transform">立即开启您的创作</p></div></div></div></div></div></div></main></div></div></div>

  <script src="/zh/elavax-base/js/analytics.js"></script>

  <script src="/zh/elavax-base/js/jquery-latest.min.js"></script>

  <script src="/zh/elavax-base/js/iframe-test-v1.js" defer=""></script>

</body>
</html>