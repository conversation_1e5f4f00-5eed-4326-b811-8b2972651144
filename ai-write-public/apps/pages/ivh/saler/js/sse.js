class SSEClient {
    constructor(url, options = {}) {
        this.url = url;
        this.options = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            method: 'POST',
            body: JSON.stringify(options.params || {}),
            withCredentials: options.withCredentials || false,
            retry: options.retry || 3000
        };
        this.eventSource = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = options.maxReconnectAttempts || 5;
        this.listeners = new Map();
    }

    connect() {
        return new Promise((resolve, reject) => {
            try {
                fetch(this.url, this.options)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        if (!response.body) {
                            throw new Error('ReadableStream not supported');
                        }

                        const reader = response.body.getReader();
                        const decoder = new TextDecoder();
                        let buffer = '';

                        const processStream = async () => {
                            try {
                                const {value, done} = await reader.read();
                                if (done) {
                                    console.log('Stream complete');
                                    return;
                                }

                                buffer += decoder.decode(value, {stream: true});
                                const lines = buffer.split('\n');
                                buffer = lines.pop() || '';

                                lines.forEach(line => {
                                    if (line.trim()) {
                                        this.processMessage(line);
                                    }
                                });

                                processStream();
                            } catch (error) {
                                this.handleError(error);
                            }
                        };

                        processStream();
                        resolve();
                    })
                    .catch(error => {
                        this.handleError(error);
                        reject(error);
                    });

            } catch (error) {
                console.error('SSE 初始化错误:', error);
                reject(error);
            }
        });
    }

    processMessage(line) {
        if (line.startsWith('data:')) {
            const data = line.slice(5);
            try {
                const parsedData = JSON.parse(data);
                this.listeners.get('message')?.forEach(callback => callback(parsedData));
            } catch (error) {
                this.listeners.get('message')?.forEach(callback => callback(data));
            }
        }
    }

    addEventListener(eventName, callback) {
        if (!this.listeners.has(eventName)) {
            this.listeners.set(eventName, new Set());
        }
        this.listeners.get(eventName).add(callback);
    }

    handleError(error) {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`尝试重新连接 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            setTimeout(() => this.connect(), this.options.retry);
        } else {
            console.error('达到最大重连次数');
        }
    }

    close() {
        // 清理资源
        this.listeners.clear();
    }
}

// 使用示例：
/*
const sseClient = new SSEClient('https://api.example.com/events', {
    headers: {
        'Authorization': 'Bearer your-token'
    },
    withCredentials: true,
    retry: 3000,
    maxReconnectAttempts: 5
});

// 连接 SSE
sseClient.connect()
    .then(() => {
        // 监听特定事件
        sseClient.addEventListener('message', (data) => {
            console.log('收到消息:', data);
        });

        sseClient.addEventListener('custom-event', (data) => {
            console.log('收到自定义事件:', data);
        });
    })
    .catch(error => {
        console.error('连接失败:', error);
    });

// 关闭连接
// sseClient.close();
*/