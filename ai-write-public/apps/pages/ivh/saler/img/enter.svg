<svg width="122" height="122" viewBox="0 0 122 122" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_bddi_3411_1480)">
<circle cx="60.7778" cy="59.7778" r="57.7778" fill="url(#paint0_linear_3411_1480)" fill-opacity="0.9" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter1_d_3411_1480)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M81.5556 36.667C82.3533 36.667 83 37.3137 83 38.1114V61.9448L83 66.3758C83 66.6216 82.6924 66.7327 82.5353 66.5436L78.7159 61.9448H51.5833C50.7856 61.9448 50.1389 61.2981 50.1389 60.5003V38.1114C50.1389 37.3137 50.7856 36.667 51.5833 36.667H81.5556Z" fill="url(#paint1_linear_3411_1480)" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter2_bd_3411_1480)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M44.0001 30.5283C43.2024 30.5283 42.5557 31.175 42.5557 31.9728V55.0839L42.5557 60.9594C42.5557 61.2005 42.8534 61.3142 43.0141 61.1344L48.4234 55.0839H77.2223C78.0201 55.0839 78.6668 54.4372 78.6668 53.6394V31.9728C78.6668 31.175 78.0201 30.5283 77.2223 30.5283H44.0001Z" fill="url(#paint2_linear_3411_1480)" shape-rendering="crispEdges"/>
</g>
<circle cx="53.0871" cy="42.5209" r="2.16667" fill="white"/>
<circle cx="68.9762" cy="42.5209" r="2.16667" fill="white"/>
<ellipse cx="61.0314" cy="42.5209" rx="2.16667" ry="2.16666" fill="white"/>
<path d="M43.7098 79.4512H40.1118V82.6852V83.2032H43.7098V79.4512ZM35.4638 83.2032H38.7538V82.6712V79.4512H35.9678V78.1912H47.6858V79.4512H45.0958V83.2032H48.1198V84.4632H45.0958V90.3292H43.7098V84.4632H40.0418C39.8178 86.6192 39.0478 88.7752 36.5278 90.3712C36.3038 90.0772 35.7998 89.6012 35.4638 89.3912C37.6898 88.0192 38.4318 86.2552 38.6558 84.4632H35.4638V83.2032ZM55.1618 84.5332H61.5178V90.2872H60.2578V89.7132H56.3798V90.3152H55.1618V84.5332ZM56.3798 88.5512H60.2578V85.7232H56.3798V88.5512ZM54.7978 83.5672C54.7418 83.2872 54.5178 82.6572 54.3638 82.3072C54.6578 82.2372 54.9238 81.9152 55.2598 81.4112C55.6238 80.9352 56.7018 79.0732 57.3598 77.3092L58.7318 77.6872C58.0598 79.2272 57.1918 80.7672 56.3378 81.9992L60.2718 81.7472C59.8938 81.0892 59.4598 80.4172 59.0398 79.8432L60.0898 79.3252C61.0418 80.5572 62.0218 82.1812 62.4418 83.2732L61.2938 83.8752C61.1958 83.5812 61.0558 83.2452 60.8878 82.8952C55.9178 83.3012 55.2458 83.3852 54.7978 83.5672ZM51.6618 81.3832C51.4378 82.5032 51.1858 83.5952 50.9338 84.5192C51.3398 84.8272 51.7458 85.1492 52.1378 85.4712C52.5298 84.3232 52.8098 82.9652 52.9638 81.3832H51.6618ZM49.5758 84.9812C49.8698 84.0292 50.1918 82.7412 50.4578 81.3832H49.3378V80.1512H50.6958C50.8498 79.1992 50.9898 78.2472 51.0738 77.3932L52.3198 77.4632C52.2078 78.3032 52.0678 79.2272 51.8998 80.1512H53.2578L53.4818 80.1232L54.2378 80.2352C54.0978 82.7692 53.7338 84.7712 53.1318 86.3532C53.6918 86.8852 54.1818 87.4032 54.5038 87.8512L53.7058 88.9292C53.4258 88.5232 53.0338 88.0612 52.5718 87.5852C51.9278 88.7752 51.1018 89.6432 50.0658 90.3012C49.8838 89.9792 49.5478 89.4892 49.2678 89.2512C50.2338 88.7192 51.0178 87.8512 51.6198 86.6752C50.9618 86.0592 50.2338 85.4712 49.5758 84.9812ZM64.1778 77.8272H68.4058V88.8172C68.4058 89.4612 68.2938 89.8532 67.8878 90.0772C67.4958 90.2872 66.9078 90.3152 65.9698 90.3152C65.9418 89.9792 65.7878 89.3772 65.6058 89.0412C66.1938 89.0692 66.7538 89.0552 66.9358 89.0552C67.1178 89.0412 67.1878 88.9852 67.1878 88.7892V85.6252H65.2838C65.1578 87.2912 64.8778 89.0272 64.2618 90.3432C64.0378 90.1332 63.4918 89.8392 63.1838 89.7272C64.1218 87.7672 64.1778 84.9532 64.1778 82.8812V77.8272ZM65.3818 79.0592V81.0612H67.1878V79.0592H65.3818ZM65.3818 82.2792V82.8812L65.3538 84.3652H67.1878V82.2792H65.3818ZM74.5938 83.8752H72.0738C72.3958 84.8552 72.8578 85.7932 73.4178 86.6052C73.9078 85.8072 74.3138 84.8832 74.5938 83.8752ZM69.4418 77.8552H75.6298V80.5712C75.6298 81.1872 75.4898 81.5232 74.9858 81.6912C74.4958 81.8732 73.7538 81.8732 72.6898 81.8732C72.6198 81.5232 72.4378 81.0752 72.2838 80.7532C73.0678 80.7812 73.8518 80.7812 74.0618 80.7672C74.2858 80.7672 74.3558 80.7112 74.3558 80.5432V79.0872H70.7018V82.6712H75.0418L75.2658 82.6292L76.0358 82.8812C75.6858 84.7852 75.0558 86.3532 74.2158 87.6132C74.8598 88.3272 75.6158 88.9152 76.4558 89.3072C76.1618 89.5452 75.7978 90.0072 75.6018 90.3292C74.8038 89.8952 74.0898 89.3072 73.4598 88.5932C72.8578 89.2652 72.1858 89.8252 71.4578 90.2592C71.2898 89.9652 70.9678 89.5312 70.7018 89.3072V90.3152H69.4418V77.8552ZM70.9398 83.8752H70.7018V89.2792C71.4298 88.9012 72.1018 88.3272 72.7038 87.6132C71.9618 86.5352 71.3738 85.2612 70.9398 83.8752ZM82.8538 83.8192L84.2258 83.9312C84.1558 84.3512 84.0718 84.7432 83.9738 85.1212H89.0698C89.0698 85.1212 89.0418 85.4992 89.0138 85.6812C88.6918 88.1732 88.3698 89.2652 87.8658 89.7132C87.5158 90.0352 87.1378 90.1192 86.5778 90.1472C86.1018 90.1752 85.2478 90.1752 84.3658 90.1192C84.3518 89.7692 84.1698 89.2792 83.9458 88.9572C84.8558 89.0412 85.8078 89.0552 86.1718 89.0552C86.4798 89.0552 86.6478 89.0412 86.8158 88.9152C87.1378 88.6772 87.4038 87.8792 87.6278 86.2692H83.5818C82.6578 88.4532 80.9638 89.6152 78.2898 90.3012C78.1638 90.0072 77.7718 89.4472 77.5338 89.1812C79.8438 88.7332 81.3278 87.8652 82.1538 86.2692H78.4858V85.1212H82.6018C82.7138 84.7292 82.7978 84.2952 82.8538 83.8192ZM86.9838 79.8292H81.5378L81.3838 79.9832C81.9718 80.6692 82.8118 81.2152 83.8478 81.6492C85.0938 81.1592 86.1718 80.5712 86.9838 79.8292ZM81.9998 77.3092L83.3578 77.5752C83.1198 77.9532 82.8678 78.3452 82.5738 78.7092H88.0618L88.2718 78.6532L89.0838 79.1852C88.2438 80.4452 86.9698 81.4252 85.4578 82.1812C86.8718 82.5452 88.5238 82.7552 90.3018 82.8532C90.0218 83.1472 89.7138 83.6792 89.5598 84.0292C87.4038 83.8612 85.4718 83.4972 83.8478 82.8672C81.9998 83.5392 79.9418 83.9592 77.9258 84.1972C77.8278 83.8612 77.6038 83.3432 77.4078 83.0352C79.0738 82.8812 80.7818 82.6012 82.3498 82.1532C81.6498 81.7472 81.0338 81.2572 80.5158 80.6972C79.9838 81.1032 79.3958 81.4952 78.7378 81.8452C78.5698 81.5372 78.2058 81.0472 77.9398 80.8372C79.9278 79.8712 81.2718 78.5132 81.9998 77.3092Z" fill="white"/>
<defs>
<filter id="filter0_bddi_3411_1480" x="0.0454547" y="0.522727" width="121.465" height="121.465" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="0.738636"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_3411_1480"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.47727"/>
<feGaussianBlur stdDeviation="1.47727"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.175486 0 0 0 0 0.231958 0 0 0 0 0.316667 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_3411_1480" result="effect2_dropShadow_3411_1480"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.295455"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.203351 0 0 0 0 0.484979 0 0 0 0 0.920833 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_3411_1480" result="effect3_dropShadow_3411_1480"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_3411_1480" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.295455"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect4_innerShadow_3411_1480"/>
</filter>
<filter id="filter1_d_3411_1480" x="49.3135" y="36.2544" width="34.5116" height="31.6222" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.412562"/>
<feGaussianBlur stdDeviation="0.412562"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="color-burn" in2="BackgroundImageFix" result="effect1_dropShadow_3411_1480"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3411_1480" result="shape"/>
</filter>
<filter id="filter2_bd_3411_1480" x="40.8397" y="28.4692" width="39.5432" height="34.1262" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="0.600575"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_3411_1480"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.343185"/>
<feGaussianBlur stdDeviation="0.857964"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_3411_1480" result="effect2_dropShadow_3411_1480"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_3411_1480" result="shape"/>
</filter>
<linearGradient id="paint0_linear_3411_1480" x1="27.4281" y1="-56.1466" x2="154.907" y2="-32.2262" gradientUnits="userSpaceOnUse">
<stop stop-color="#5F9CFF"/>
<stop offset="1" stop-color="#266FE8"/>
</linearGradient>
<linearGradient id="paint1_linear_3411_1480" x1="67.4334" y1="51.12" x2="67.4334" y2="68.3015" gradientUnits="userSpaceOnUse">
<stop stop-color="#F5F9FF" stop-opacity="0.12"/>
<stop offset="1" stop-color="#8ECFFF"/>
</linearGradient>
<linearGradient id="paint2_linear_3411_1480" x1="60.7122" y1="30.5962" x2="60.7122" y2="61.3612" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.5"/>
<stop offset="1" stop-color="white" stop-opacity="0.65"/>
</linearGradient>
</defs>
</svg>
