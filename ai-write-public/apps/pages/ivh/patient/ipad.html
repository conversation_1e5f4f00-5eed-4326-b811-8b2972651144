<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <title>梅斯小智</title>
    <link rel="stylesheet" href="./css/index.css" />
    <link rel="icon" href="//cloudcache.tencent-cloud.com/qcloud/favicon.ico" type="image/x-icon"/>
  </head>

  <body>
    <div class="wrapper">
      <img class="bg2" src="img/medsci-bg2.png" alt="">
      <img class="bg-font" src="img/bg-font.png" alt="">
      <div class="welcome">
        <div>您好，我是梅斯小智</div>
        <div>你可以向我提问</div>
      </div>
      <!-- 数智人视频播放器 -->
      <div class="video-area"></div>
      <!-- video第一帧图片 -->
      <canvas id="titleCanvas" style="display: none;"></canvas>
      <!-- 加载界面 -->
      <div class="loading">
        <div>数智人加载中<span class="dot"></span></div>
      </div>
      <div class="loading ai-loading">
        <div>数智人正在思考中<span class="dot"></span></div>
      </div>

      <!-- 开始服务按钮 -->
      <button class="btn-enter"></button>

      <!-- 再次服务按钮 -->
      <button class="btn-re-enter"></button>

      <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none" >
        <symbol id="icon-arrow" viewBox="0 0 24 24">
         <path d="M5.29405 7.59107L10.9709 13.3284L16.7318 7.6282C16.9848 7.3752 17.3959 7.3761 17.65 7.63016L17.65 7.63018C17.904 7.88424 17.9049 8.29529 17.587 8.54817L11.5063 14.6288C11.4145 14.7109 11.3061 14.8051 11.2475 14.8208C11.0088 14.9168 10.7155 14.8687 10.4744 14.6766L4.30872 8.51097C4.11864 8.25583 4.11765 7.84288 4.37173 7.5888C4.62581 7.33476 5.03874 7.33575 5.29405 7.59107Z" fill="#9296A7" style="fill:#9296A7;fill:color(display-p3 0.5725 0.5882 0.6549);fill-opacity:1;"/>
       </symbol>
      </svg>
      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="14" viewBox="0 0 12 14" fill="none">
        <symbol id="icon-reasonning" viewBox="0 0 12 14">
          <path d="M5.18049 1.06715C5.02209 1.23842 4.8987 1.43948 4.81737 1.65883C4.73603 1.87818 4.69835 2.11154 4.70647 2.34558C4.71459 2.57961 4.76835 2.80974 4.86468 3.02283C4.96102 3.23592 5.09803 3.42779 5.26792 3.58749C5.4378 3.7472 5.63722 3.8716 5.85478 3.9536C6.07235 4.0356 6.30381 4.07359 6.53594 4.0654C6.76807 4.05722 6.99633 4.00302 7.20768 3.90589C7.41904 3.80877 7.60935 3.67062 7.76775 3.49935C8.08765 3.15344 8.25816 2.69358 8.24177 2.22092C8.22537 1.74826 8.02341 1.30153 7.68032 0.978999C7.33723 0.656469 6.88111 0.48456 6.4123 0.501091C5.94349 0.517621 5.50039 0.721237 5.18049 1.06715ZM1.64854 2.92679C1.50173 3.07494 1.386 3.25136 1.30826 3.44556C1.23051 3.63976 1.19234 3.84775 1.19603 4.05714C1.19971 4.26652 1.24517 4.47302 1.32969 4.66431C1.41421 4.85561 1.53607 5.02779 1.688 5.1706C1.83993 5.3134 2.01882 5.42391 2.21401 5.49552C2.4092 5.56714 2.6167 5.5984 2.82413 5.58745C3.03156 5.57649 3.23468 5.52355 3.42138 5.43176C3.60808 5.33998 3.77454 5.21124 3.91084 5.05321C4.05468 4.90441 4.16759 4.72814 4.24294 4.53474C4.31829 4.34134 4.35457 4.13471 4.34963 3.92699C4.3447 3.71927 4.29866 3.51463 4.21421 3.32509C4.12976 3.13555 4.00861 2.96493 3.85787 2.82324C3.70713 2.68155 3.52983 2.57164 3.33639 2.49997C3.14294 2.42831 2.93725 2.39632 2.73138 2.40589C2.5255 2.41547 2.3236 2.46641 2.13752 2.55572C1.95144 2.64504 1.78493 2.77093 1.64776 2.92601L1.64854 2.92679ZM0.356456 6.28336C0.23763 6.41181 0.145063 6.56261 0.0840406 6.72713C0.0230181 6.89166 -0.00526516 7.06669 0.000805731 7.24224C0.00687662 7.41778 0.0471828 7.5904 0.119423 7.75024C0.191663 7.91008 0.294422 8.05401 0.421834 8.17381C0.549245 8.29361 0.698813 8.38694 0.861999 8.44846C1.02518 8.50999 1.19879 8.5385 1.37291 8.53238C1.54702 8.52626 1.71824 8.48562 1.87678 8.41279C2.03532 8.33996 2.17807 8.23636 2.2969 8.1079C2.53688 7.84847 2.66481 7.50355 2.65255 7.14902C2.64029 6.79449 2.48884 6.45939 2.23152 6.21744C1.9742 5.97549 1.63209 5.84651 1.28045 5.85887C0.928806 5.87123 0.596436 6.02393 0.356456 6.28336ZM2.11508 9.94804C1.90527 10.1747 1.79334 10.4761 1.80393 10.7859C1.81453 11.0957 1.94677 11.3887 2.17156 11.6002C2.39636 11.8117 2.69529 11.9246 3.00261 11.9139C3.30992 11.9032 3.60045 11.7699 3.81026 11.5433C4.01464 11.3158 4.12205 11.0164 4.1093 10.7098C4.09655 10.4031 3.96466 10.1139 3.74212 9.90447C3.51959 9.69507 3.22427 9.58233 2.91997 9.59063C2.61568 9.59893 2.32684 9.72759 2.11585 9.94882L2.11508 9.94804ZM5.70274 11.6142C5.60144 11.7209 5.52216 11.8467 5.4695 11.9845C5.41684 12.1222 5.39185 12.2692 5.39598 12.4167C5.40011 12.5643 5.43328 12.7096 5.49356 12.8442C5.55384 12.9787 5.64003 13.0998 5.74712 13.2005C5.85422 13.3012 5.98009 13.3795 6.11742 13.4307C6.25476 13.4819 6.40082 13.5052 6.54713 13.499C6.69344 13.4929 6.83709 13.4575 6.96972 13.3949C7.10236 13.3324 7.22134 13.2439 7.31977 13.1346C7.51515 12.9176 7.61791 12.6318 7.60583 12.339C7.59374 12.0462 7.46778 11.7699 7.2552 11.5701C7.04263 11.3702 6.76054 11.2628 6.46999 11.2711C6.17944 11.2793 5.9038 11.4026 5.70274 11.6142ZM9.55887 10.0596C9.47967 10.1452 9.41797 10.2458 9.37731 10.3554C9.33664 10.4651 9.3178 10.5818 9.32186 10.6988C9.32592 10.8158 9.3528 10.9309 9.40097 11.0374C9.44913 11.144 9.51764 11.2399 9.60258 11.3198C9.68752 11.3996 9.78723 11.4618 9.89602 11.5028C10.0048 11.5438 10.1205 11.5628 10.2366 11.5587C10.3527 11.5546 10.4668 11.5275 10.5725 11.479C10.6781 11.4304 10.7733 11.3613 10.8525 11.2757C11.0058 11.1017 11.0856 10.8742 11.0748 10.6418C11.064 10.4093 10.9635 10.1903 10.7947 10.0315C10.6259 9.87277 10.4023 9.78684 10.1715 9.79208C9.94074 9.79732 9.72115 9.89333 9.55964 10.0596H9.55887ZM10.851 6.70224C10.7314 6.83219 10.6677 7.00457 10.6738 7.18171C10.6799 7.35886 10.7552 7.52638 10.8834 7.64767C10.9795 7.73796 11.0999 7.79751 11.2296 7.81877C11.3592 7.84003 11.4921 7.82205 11.6116 7.76711C11.7311 7.71217 11.8318 7.62273 11.9008 7.51011C11.9699 7.39749 12.0042 7.26676 11.9996 7.13444C11.9949 7.00212 11.9514 6.87417 11.8747 6.76676C11.7979 6.65936 11.6912 6.57734 11.5681 6.53108C11.4451 6.48481 11.3112 6.47638 11.1834 6.50684C11.0556 6.53731 10.9396 6.60531 10.8502 6.70224H10.851ZM9.69504 3.22243C9.65313 3.26481 9.62011 3.31525 9.59793 3.37075C9.57575 3.42626 9.56488 3.4857 9.56595 3.54553C9.56702 3.60536 9.58002 3.66436 9.60417 3.71902C9.62832 3.77368 9.66313 3.82289 9.70652 3.86371C9.74992 3.90454 9.80102 3.93614 9.85678 3.95665C9.91254 3.97715 9.97182 3.98614 10.0311 3.98308C10.0904 3.98001 10.1484 3.96496 10.2018 3.93881C10.2552 3.91266 10.3028 3.87595 10.3419 3.83087C10.4174 3.74365 10.4563 3.63025 10.4504 3.51462C10.4445 3.399 10.3943 3.29021 10.3103 3.21123C10.2264 3.13225 10.1153 3.08927 10.0004 3.09135C9.88563 3.09344 9.77612 3.14045 9.69504 3.22243Z" fill="#11121B" style="fill:#11121B;fill:color(display-p3 0.0667 0.0706 0.1059);fill-opacity:1;"/>
        </symbol>
        </svg>
      <!-- 页面header -->
      <div class="header">
        <!-- <h1>数智人会话互动 <span class="hot"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
          <path d="M10.6965 5.10201C10.8934 5.36017 10.9613 5.67764 11 6V6.86979C11 8.84479 9.4172 10.5145 7.28817 11V10.3491C7.28817 9.73853 6.71184 9.24571 6.00216 9.24571H5.99784C5.28815 9.24571 4.70752 9.73853 4.70752 10.3491V11C2.57849 10.5109 1 8.8411 1 6.86979L1.18746 5.37295L1.5 4.40936C2.07205 4.80657 3.5 6 4 7.30346C3.5 5.5 3.5 5.37295 4.51397 4.18871C5.55485 3.1773 4.86451 1.06658 4.26666 0C8 0.5 8.52688 5.74038 8.31183 7.30346C9.24516 5.88748 9.41936 4.28801 10 4.18871L10.6965 5.10201Z" fill="url(#paint0_linear_13388_130760)" style=""/>
          <defs>
          <linearGradient id="paint0_linear_13388_130760" x1="6" y1="1" x2="6" y2="11" gradientUnits="userSpaceOnUse">
          <stop stop-color="#B968D4" style="stop-color:#B968D4;stop-color:color(display-p3 0.7255 0.4078 0.8314);stop-opacity:1;"/>
          <stop offset="1" stop-color="#FFA759" style="stop-color:#FFA759;stop-color:color(display-p3 1.0000 0.6533 0.3500);stop-opacity:1;"/>
          </linearGradient>
          </defs>
          </svg><span class="text">Deepseek</span></span></h1>
        <p>基于大模型对话能力全面升级</p> -->
        <!-- 结束服务按钮 -->
        <button class="btn-exit"></button>
      </div>

      <!-- 背景颜色header -->
      <!-- <div class="header-bg"></div> -->

      <div class="chat-container">
        <!-- 用户输入文本气泡 -->
         <div style="display: flex;align-items: center;"><div class="chat-user">
          <div></div>
        </div><img style="margin-left: 10px;" class="chat-user chat-user-img" src="img/user-touxiang.png" alt="用户头像"></div>
        <!-- 数智人返回文本气泡 -->
        <div class="chat-ai chat-ai-text">
          <div>
            <div class="reason">
              <div class="title" id="reason-title">
              </div>
              <pre></pre></div>
            <div class="answer">
              <pre></pre>
            </div>
            <div class="refs"></div>
          </div>
        </div>

        <!-- 数智人返回选择题气泡 -->
        <div class="chat-ai chat-ai-option">
          <div>
            <p class="title"></p>
            <div class="list"></div>
          </div>
        </div>

        <!-- 数智人返回图片气泡 -->
        <div class="chat-ai chat-ai-image">
          <div>
            <p class="title"></p>
            <div class="image" style="background-image: url('')"></div>
          </div>
        </div>

        <!-- 数智人返回视频气泡 -->
        <div class="chat-ai chat-ai-video">
          <div>
            <p class="title"></p>
            <div class="video">
              <video src=""></video>
            </div>
          </div>
        </div>

        <!-- 数智人返回文本弹窗气泡 -->
        <div class="chat-ai chat-ai-popup">
          <div>
            <p class="title"></p>
            <p>
              <button class="button">查看详情</button>
            </p>
          </div>
        </div>

        <!-- 跑马灯 -->
        <div class="chat-marquee">
          <div class="title"></div>
          <div class="list">
            <div class="marquee"></div>
            <div class="marquee"></div>
            <div class="marquee"></div>
          </div>
        </div>
      </div>

      <!-- Logo盒子 -->
      <div class="logo-box" style="position: absolute; top: 35px; left: 70%; transform: translateX(-50%); text-align: center; z-index: 10;">
        <img src="img/medsci-logo.png" alt="Logo" style="width: 100px; height: auto; display: block; margin: 0 auto;">
        <!-- 文本盒子 -->

      </div>
      <div class="text-box" style="display: none; padding: 20px; margin-top: 40px;">
        <div>
          <div class="text-line1">你可以这样问我：</div>
          <div class="text-line2 clickable-text">前列腺癌要做哪些常规检查？</div>
          <div class="text-line2 clickable-text">手术后饮食要注意些什么？</div>
          <div class="text-line2 clickable-text">除了手术还能怎么治？</div>
        </div>
      </div>
      <!-- 操作区 -->
      <div class="action-wrapper">

        <!-- 停止生成按钮 -->
        <button class="btn-stop-create"></button>
        <!-- 重新生成按钮 -->
        <button class="btn-recreate"></button>
        <!-- 语音输入按钮界面 -->
        <div class="audio-action-wrapper">
          <div><button class="btn-audio"></button>
            <button class="btn-keyboard"></button></div>
        </div>
         <!-- asr识别界面 -->
         <div class="asr-pop">
          <div class="asr-text"></div>
          <div class="asr-wave">
            <canvas class="wave-left"></canvas>
            <canvas class="wave-right"></canvas>
          </div>
        </div>

        <!-- 文本输入界面 -->
        <div class="keyboard-action-wrapper">
          <div class="button-wrapper">
            <button class="btn-new-chat">新对话</button>
             <button class="btn-help">帮助</button>
          </div>
          <div class="text-wrapper">
            <button class="btn-mic"></button>
            <div class="text-input" contenteditable="true" style="color: #999999;" placeholder="请输入您的问题"></div>
            <button class="btn-send"></button>
          </div>
        </div>
      </div>

      <!-- 遮罩 -->
      <div class="mask"></div>

      <!-- 弹窗 -->
      <div class="chat-popup">
        <button class="close"></button>
        <div></div>
      </div>

      <!-- 异常弹窗 -->
      <div class="warn">
        <img src="./img/warn.svg" style="width: 50px;height: 50px;" alt="">
        <p class="info">数智人配置有变更</p>
        <button>刷新页面</button>
      </div>
    </div>

    <script src="./lib/speechrecognizer.js"></script>
    <script src="./lib/TXIVHSDK_Web_Cloud_V5.1.2_Release.js"></script>
    <script src="./lib/uuid.min.js"></script>
    <script src="./lib/marked.min.js"></script>
    <script src="./js/index.js"></script>
  </body>
</html>
