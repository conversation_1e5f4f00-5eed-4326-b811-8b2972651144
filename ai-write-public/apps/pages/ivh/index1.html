<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>梅斯小智</title>
    <style>
        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            background-image: url(./saler/img/medsci-bg.png);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        .container {
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            width: 360px;
            max-width: 90%;
        }

        .form-title {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
            font-size: 24px;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 25px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #34495e;
            font-size: 15px;
            font-weight: 500;
        }

        select, input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 15px;
            transition: all 0.3s ease;
            background-color: #fff;
        }

        select {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }

        select option {
            padding: 12px;
            font-size: 14px;
            color: #333;
            background-color: #fff;
            cursor: pointer;
        }

        select option:hover {
            background-color: #f5f5f5;
        }

        select:focus option:checked {
            background: #1892FF;
            color: #fff;
        }

        /* 添加选择框hover效果 */
        .form-group select:hover {
            border-color: #1892FF;
        }

        button {
            width: 100%;
            padding: 14px;
            background: #1892FF;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            font-family: PingFangSC-Regular;
        }

        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        button:active {
            transform: translateY(1px);
        }

        iframe {
            width: 100%;
            height: 100%;
            border: none;
            position: fixed;
            top: 0;
            left: 0;
            display: none;
        }

        @media (max-width: 480px) {
            .container {
                padding: 25px;
            }
        }
    </style>
</head>
<body>
    <div class="container" id="formContainer">
        <h1 class="form-title">梅斯小智</h1>
        <form id="configForm">
            <div class="form-group">
                <label for="scene">场景选择：</label>
                <select id="scene" name="scene" required>
                    <option value="初次陌生拜访">初次陌生拜访</option>
                    <option value="常规日常拜访">常规日常拜访</option>
                </select>
            </div>
            <div class="form-group">
                <label for="cognition">医生认知：</label>
                <select id="cognition" name="cognition" required>
                    <option value="未接触过产品，可能对竞品更熟悉">未接触过产品，可能对竞品更熟悉</option>
                    <option value="曾少量处方，但对疗效或安全性存疑">曾少量处方，但对疗效或安全性存疑</option>
                </select>
            </div>
            <div class="form-group">
                <label for="u_name">你的姓名：</label>
                <input type="text" id="u_name" name="u_name" required value="小王" placeholder="请输入你的姓名">
            </div>
            <button type="submit">开始对话</button>
        </form>
        <div style="margin-top: 25px;;">
            <div class="text-line2 clickable-text">使用场景：培训（对抗演练）+ 考试（包含交互、打分、报告生成、个体能力分析、群体能力分析、推荐拜访医生类型）、拜访前预拜访模拟。</div>
        </div>
    </div>

    <iframe id="chatFrame" allow="camera;microphone"></iframe>

    <script>
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                // 进入全屏模式
                document.documentElement.requestFullscreen()
                    .then(() => console.log("进入全屏成功"))
                    .catch(err => console.log("进入全屏失败: ", err));
            } else {
                // 退出全屏模式
                document.exitFullscreen()
                    .then(() => console.log("退出全屏成功"))
                    .catch(err => console.log("退出全屏失败: ", err));
            }
        }

        document.getElementById('configForm').addEventListener('submit', function(e) {
            e.preventDefault();
            toggleFullscreen();
            
            const scene = document.getElementById('scene').value;
            const cognition = document.getElementById('cognition').value;
            const u_name = document.getElementById('u_name').value;

            if (!scene || !cognition || !u_name) {
                alert('请填写完整信息');
                return;
            }

            const params = {
                virtualmanKey: 'f9088d0a573b4d7db2d5181da3caa19a',
                sign: 'lKSqKgg5sY9+GlFHCG5ErK+dMvQajxmR3bV44psycM1gz0IpxfztPB5NM2AevqCnRfzFxuCX9ec9oquh0Beq/Dw5byuZj3207VnHnvh6upgS9+jB4W6MJ0Pit2Y177+LBR6GgNBOL8m0R6gcnFNoiw==',
                secretId: 'AKIDMdnpfJm7JLD55DE2eRMPJ4g6MRwHSSIx',
                secretKey: 'XqzykJftwnSEoCNK4cy9B2jGqMirLdie',
                appId: '1253188136',
                scene: encodeURIComponent(scene),
                cognition: encodeURIComponent(cognition),
                u_name: encodeURIComponent(u_name)
            };

            // 构建URL参数
            const queryString = Object.keys(params)
                .map(key => `${key}=${params[key]}`)
                .join('&');

            // 设置iframe的src并显示
            const chatFrame = document.getElementById('chatFrame');
            chatFrame.src = `./saler/ipad.html?${queryString}`;
            chatFrame.style.display = 'block';

            // 隐藏表单容器
            document.getElementById('formContainer').style.display = 'none';

            // 移除背景渐变
            document.body.style.background = 'none';
        });
    </script>
</body>
</html>
