package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.config.BaseIService;
import cn.iocoder.yudao.aiBase.dto.param.AppUserParam;
import cn.iocoder.yudao.aiBase.entity.AiAppUsers;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface AiAppUsersService extends BaseIService<AiAppUsers, AppUserParam> {

    /**
     * 根据参数查询AiAppUsers列表
     * @param reqVO 请求参数
     * @return AiAppUsers列表
     */
    List<AiAppUsers> selectList(AppUserParam reqVO);

    /**
     * 根据参数分页查询AiAppUsers列表
     * @param reqVO 请求参数
     * @return AiAppUsers分页结果
     */
    PageResult<AiAppUsers> selectPage(AppUserParam reqVO);

    /**
     * 查询指定用户ID和三方类型的用户信息，并以Map形式返回
     * @param socialUserId 主站用户ID
     * @param socialType 三方类型
     * @return 用户信息的Map，键为用户ID，值为AiAppUsers对象
     */
    Map<String, AiAppUsers> selectMap(Long socialUserId, Integer socialType);

    /**
     * 处理定时任务，更新过期时间相关的用户订阅状态
     * @param end 截止时间
     */
    void handleTask(LocalDateTime end);

    /**
     * 获取指定用户ID、三方类型和appUuid的用户信息
     * @param socialUserId 主站用户ID
     * @param socialType 三方类型
     * @param appUuid 应用唯一标识
     * @return AiAppUsers对象
     */
    AiAppUsers getAppUser(Long socialUserId, Integer socialType, String appUuid);

    /**
     * 获取或创建用户信息。如果存在则获取，否则创建新的用户记录
     * @param socialUserId 主站用户ID
     * @param socialType 三方类型
     * @param stripeCustomerId 客户ID
     * @param appUuid 应用唯一标识
     * @return AiAppUsers对象
     */
    AiAppUsers getOrCreateAppUser(Long socialUserId, Integer socialType, String stripeCustomerId, String appUuid);

    /**
     * 更新用户的订阅信息，基于月份数计算新的开始和结束时间
     * @param socialUserId 主站用户ID
     * @param socialType 三方类型
     * @param stripeCustomerId 客户ID
     * @param appUuid 应用唯一标识
     * @param monthNum 订阅月数
     * @return 更新后的AiAppUsers对象
     */
    AiAppUsers updateSub(Long socialUserId, Integer socialType, String stripeCustomerId, String appUuid, Integer monthNum);

    /**
     * 更新用户的订阅信息，基于指定的开始和结束时间
     * @param socialUserId 主站用户ID
     * @param socialType 三方类型
     * @param appUuid 应用唯一标识
     * @param startAt 开始时间
     * @param expireAt 过期时间
     * @return 更新后的AiAppUsers对象
     */
    AiAppUsers updateSub(Long socialUserId, Integer socialType, String appUuid, LocalDateTime startAt, LocalDateTime expireAt);

    /**
     * 统计满足条件的用户数量
     * @param socialUserId 主站用户ID
     * @param socialType 三方类型
     * @param appUuid 应用唯一标识
     * @return 满足条件的用户数量
     */
    int countAll(Long socialUserId, Integer socialType, String appUuid);

}
