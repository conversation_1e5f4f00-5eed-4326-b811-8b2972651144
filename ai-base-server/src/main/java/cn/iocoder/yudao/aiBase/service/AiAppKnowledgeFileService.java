package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.dto.param.KnowledgeParam;
import cn.iocoder.yudao.aiBase.dto.request.FileUploadRequest;
import cn.iocoder.yudao.aiBase.dto.response.KnowledgeResponse;
import cn.iocoder.yudao.aiBase.entity.AiAppKnowledgeFile;
import jakarta.servlet.http.HttpServletRequest;
import cn.iocoder.yudao.aiBase.config.BaseIService;

import java.util.List;

public interface AiAppKnowledgeFileService extends BaseIService<AiAppKnowledgeFile, KnowledgeParam> {

    /**
     * 查询知识文件列表
     * @param reqVO 查询参数
     * @return 知识文件列表
     */
    List<AiAppKnowledgeFile> selectList(KnowledgeParam reqVO);

    List<KnowledgeResponse> getKnowledgeByDir(KnowledgeParam reqVO);

    Boolean updateFileName(Integer id, String fileName);

    KnowledgeResponse create(FileUploadRequest reqVO, HttpServletRequest request);

    Boolean delete(Integer id);

    List<KnowledgeResponse> getFilePath(Integer id);


}