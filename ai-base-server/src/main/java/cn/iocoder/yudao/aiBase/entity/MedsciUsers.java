package cn.iocoder.yudao.aiBase.entity;

import cn.iocoder.yudao.module.system.enums.social.SocialTypeEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023-09-15
 */
@TableName("medsci_users")
@KeySequence("medsci_users1_id_seq")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MedsciUsers extends Model<MedsciUsers> {

    @Schema(description =  "id")
    @TableId
    private Long id;

    /**
     * 枚举 {@link SocialTypeEnum}
     */
    @Schema(description =  "三方平台类型")
    private Integer socialType;

    @Schema(description =  "social_user关联ID")
    private Long socialUserId;

    @Schema(description =  "三方ID")
    private String openid;

    @Schema(description =  "邮箱")
    private String email;

    @Schema(description =  "firstName")
    private String firstName;

    @Schema(description =  "lastName")
    private String lastName;

    @Schema(description =  "用户名")
    private String userName;

    @Schema(description =  "头像")
    private String avatar;

    @Schema(description =  "手机号")
    private String mobile;

    @Schema(description =  "真实名")
    private String realName;

    @Schema(description =  "stripe支付用户ID")
    private String stripeCustomerId;

    @Schema(description =  "密码")
    private String password;

    /**
     * 1启用，2禁用
     *
     * 枚举 {@link user_status_type 对应的类}
     */
    @Schema(description =  "1启用，2禁用")
    private Integer status;

    @Schema(description =  "过期时间")
    private LocalDateTime expireAt;

    @Schema(description =  "创建时间")
    private LocalDateTime createdAt;

    @Schema(description =  "修改时间")
    private LocalDateTime updatedAt;

    @Schema(description =  "0正常 1删除")
    @TableLogic
    private Integer deleted;

    @Schema(description = "是否是内部用户 0否 1是")
    private Integer isInternalUser;

}
