package cn.iocoder.yudao.aiBase.dto.param;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KnowledgeParam extends PageParam {

    @Schema(description = "id")
    private Integer id;

    @Schema(description = "三方id")
    private Long socialUserId;

    @Schema(description = "三方类型")
    private Integer socialType;

    @Schema(description =  "目录或文件名")
    @Length(max = 100, message = "{ERROR_5057}")
    private String fileName;

    @Schema(description = "父级id")
    private Integer pid;

    @Schema(description = "类型")
    private Integer type;


}
