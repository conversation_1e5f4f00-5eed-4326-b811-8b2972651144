package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.config.BaseIService;
import cn.iocoder.yudao.aiBase.dto.param.MsUserParam;
import cn.iocoder.yudao.aiBase.dto.request.*;
import cn.iocoder.yudao.aiBase.dto.request.dify.MedsciUserRequest;
import cn.iocoder.yudao.aiBase.dto.response.PurchaseRecord;
import cn.iocoder.yudao.aiBase.dto.response.TokenResponse;
import cn.iocoder.yudao.aiBase.entity.MedsciUsers;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface MedsciUsersService extends BaseIService<MedsciUsers, MsUserParam> {

    public static final String IS_INTERNAL_USER = "IS_INTERNAL_USER";

    MedsciUsers getUser(MedsciUserRequest param, String locale);

    /**
     * 创建主站用户
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createUsers(@Valid MedsciUsersSaveReqVO createReqVO);

    /**
     * 更新主站用户
     *
     * @param updateReqVO 更新信息
     */
    void updateUsers(@Valid MedsciUsersSaveReqVO updateReqVO);

    /**
     * 删除主站用户
     *
     * @param id 编号
     */
    void deleteUsers(Integer id);

    /**
     * 获得主站用户
     *
     * @param id 编号
     * @return 主站用户
     */
    MedsciUsers getUsers(Integer id);

    /**
     * 获得主站用户分页
     *
     * @param pageReqVO 分页查询
     * @return 主站用户分页
     */
    PageResult<MedsciUsers> selectPage(MedsciUsersPageReqVO pageReqVO);

    /**
     * 分页查询主站用户列表
     *
     * @param param 分页查询参数
     * @return 主站用户列表
     */
    List<MedsciUsers> selectList(MsUserParam param);

    /**
     * 根据社交用户ID查询用户信息
     *
     * @param socialType     社交平台类型
     * @param socialUserId   社交用户ID
     * @return 主站用户信息
     */
    MedsciUsers getBySocialUserId(Integer socialType, Long socialUserId);

    /**
     * 根据社交OpenID查询用户信息
     *
     * @param socialType 社交平台类型
     * @param openid     社交用户的OpenID
     * @return 主站用户信息
     */
    MedsciUsers getBySocialOpenid(Integer socialType, String openid);

    /**
     * 根据社交用户ID和OpenID查询用户信息
     *
     * @param socialUserId 社交用户ID
     * @param openid       社交用户的OpenID
     * @return 主站用户信息
     */
    MedsciUsers getBySocialUserIdAndOpenid(Long socialUserId, String openid);

    /**
     * 根据用户名查询用户信息
     *
     * @param userName 用户名
     * @return 主站用户信息
     */
    MedsciUsers getByUserName(String userName, Integer socialType);

    /**
     * 根据邮箱地址查询用户信息
     *
     * @param email 邮箱地址
     * @return 主站用户信息
     */
    MedsciUsers getByEmail(String email, String fromPlatform);
    MedsciUsers getByEmail(String email, Integer socialType);

    /**
     * 更新用户状态
     *
     * @param user 用户信息
     * @return 操作结果 true-成功，false-失败
     */
    Boolean updateUserStatus(MedsciUsers user);

    /**
     * 根据OAuth2认证信息检查用户状态
     *
     * @param authUser OAuth2认证响应数据
     * @return 用户状态是否有效
     */
    Boolean checkStatusByAuthUser(OAuth2AccessTokenCheckRespDTO authUser);

    /**
     * 根据OAuth2认证信息获取用户信息
     *
     * @param authUser OAuth2认证响应数据
     * @return 主站用户信息
     */
    MedsciUsers getUserByAuthUser(OAuth2AccessTokenCheckRespDTO authUser);

    /**
     * 根据社交类型和用户信息获取用户
     *
     * @param socialType 社交平台类型
     * @param userInfo   用户信息字符串
     * @return 主站用户信息
     */
    MedsciUsers getUser(Integer socialType, String userInfo);

    /**
     * 更新用户Stripe客户ID
     * @param user
     * @return
     */
    String updateStripeCustomerId(MedsciUsers user);

    /**
     * 更新用户过期时间
     *
     * @param socialUserId 社交用户ID
     * @param socialType       社交用户的OpenID
     * @param expireAt     新的过期时间
     * @return 操作结果 true-成功，false-失败
     */
    Boolean updateExpireAt(Long socialUserId, Integer socialType, LocalDateTime expireAt);

    /**
     * 注册新用户
     *
     * @param param 注册请求参数
     * @return 操作结果 true-成功，false-失败
     */
    Boolean register(RegisterReqVO param);

    /**
     * 用户登录
     *
     * @param param 登录请求参数
     * @return Token响应信息
     */
    TokenResponse login(LoginReqVO param);

    /**
     * 用户登出
     *
     * @param response 响应对象
     * @param request  请求对象
     * @param auth     授权信息
     */
    void logout(HttpServletResponse response, HttpServletRequest request, String auth);

    /**
     * 根据社交平台类型和用户ID列表查询并返回对应的用户映射关系
     *
     * @param socialTypes    社交平台类型列表
     * @param socialUserIds  社交用户ID列表
     * @return Map<String, String> 返回社交用户标识与对应值的映射关系
     */
    Map<String, String> selectMap(List<Integer> socialTypes, List<Long> socialUserIds);

    /**
     * 获取AI写作Token
     *
     * @param param 请求参数
     * @return Token响应信息
     */
    TokenResponse getAiWriteToken(MedsciUserRequest param, String locale);

    /**
     * 社交登录
     *
     * @param socialType 社交平台类型
     * @param code       授权码
     * @param state      状态标识
     * @return Token响应信息
     */
    TokenResponse socialLogin(Integer socialType, String code, String state);

    /**
     * 获取Token
     *
     * @param user 用户信息
     * @return Token响应信息
     */
    TokenResponse getToken(MedsciUsers user);

    /**
     * 根据OAuth2认证信息获取用户名
     *
     * @param authUser OAuth2认证响应数据
     * @return 用户名称
     */
    String getUserName(OAuth2AccessTokenCheckRespDTO authUser);

    /**
     * 获取内部用户标识，从缓存中获取
     *
     * @param socialUserId 社交用户ID
     * @param socialType   社交平台类型
     * @return 内部用户标识
     */
    String getIsInternalUser(Long socialUserId, Integer socialType);

    /**
     * 获取滚动订阅信息
     * @return
     */
    List<PurchaseRecord> getPurchaseRecords();

    List<UserImportExcelVO> importUser(List<UserImportExcelVO> list);

}
