package cn.iocoder.yudao.aiBase.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * <AUTHOR>
 * @since 2023-09-15
 */
@TableName("ai_app_users")
@KeySequence("ai_app_users_id_seq")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiAppUsers extends Model<AiAppUsers> {

    @Schema(description =  "主键")
    @TableId
    private Integer id;

    @Schema(description =  "主站用户ID")
    private Long socialUserId;

    @Schema(description =  "三方类型")
    private Integer socialType;

    @Schema(description =  "语言")
    private String appUuid;

    @Schema(description =  "客户id")
    private String stripeCustomerId;

    @Schema(description =  "状态 1订阅中 2已过期")
    private Integer status;

    @Schema(description =  "售出数量")
    private Integer useNum;

    @Schema(description =  "开始时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime startAt;

    @Schema(description =  "过期时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime expireAt;

    @Schema(description =  "首次时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime firstBuyTime;

    @Schema(description =  "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime createdAt;

    @Schema(description =  "更新时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime updatedAt;

    @Schema(description =  "售出数量")
    @TableLogic
    private Integer deleted;


}
