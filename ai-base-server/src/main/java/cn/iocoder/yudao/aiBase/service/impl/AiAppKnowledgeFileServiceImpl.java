package cn.iocoder.yudao.aiBase.service.impl;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.ErrorCodeConstants;
import cn.iocoder.yudao.aiBase.dto.request.FileUploadRequest;
import cn.iocoder.yudao.aiBase.dto.response.KnowledgeResponse;
import cn.iocoder.yudao.aiBase.entity.AiAppKnowledgeFile;
import cn.iocoder.yudao.aiBase.mapper.AiAppKnowledgeFileMapper;
import cn.iocoder.yudao.aiBase.service.AiAppKnowledgeFileService;
import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.dto.param.KnowledgeParam;
import cn.iocoder.yudao.aiBase.service.OauthService;
import cn.iocoder.yudao.aiBase.service.OpenApiService;
import cn.iocoder.yudao.aiBase.service.YudaoSystemService;
import cn.iocoder.yudao.aiBase.util.CommonUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

@Service
@DS(DBConstant.AiBase)
@Slf4j
public class AiAppKnowledgeFileServiceImpl extends ServiceImpl<AiAppKnowledgeFileMapper, AiAppKnowledgeFile> implements AiAppKnowledgeFileService {

    @Autowired
    private OauthService oauthService;

    @Autowired
    private OpenApiService openApiService;

    @Autowired
    private YudaoSystemService yudaoSystemService;

    @Override
    public List<AiAppKnowledgeFile> selectList(KnowledgeParam reqVO) {
        return baseMapper.selectList(reqVO);
    }

    @Override
    public List<KnowledgeResponse> selectList(String auth, KnowledgeParam reqVO) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);
        reqVO.setSocialUserId(authUser.getUserId());
        reqVO.setSocialType(authUser.getUserType());

        List<AiAppKnowledgeFile> files = selectList(reqVO);
        if (files.isEmpty()) {
            initDataScore(auth);
            files = selectList(reqVO);
        } else {
            Boolean hasDataScore = files.stream().anyMatch(file -> file.getFileName().equals("DataScore"));
            if (!hasDataScore) {
                initDataScore(auth);
                files = selectList(reqVO);
            }
        }
        List<KnowledgeResponse> res = new ArrayList<>();
        for (AiAppKnowledgeFile file : files) {
            res.add(toBean(file));
        }
        return res;
    }

    private void initDataScore(String auth) {
        FileUploadRequest req = FileUploadRequest.builder()
                .type(BaseConstant.ONE)
                .pid(BaseConstant.ZERO)
                .fileName("DataScore")
                .build();
        create(auth, req, null);
        req.setFileName("参赛作品");
        create(auth, req, null);
    }

    public void updateCdn(AiAppKnowledgeFile file) {
        Long time = System.currentTimeMillis()/1000;
        if (BaseConstant.TWO.equals(file.getType()) && time >= file.getT() - BaseConstant.SIXTY) {
            try {
                JSONObject res = openApiService.getCosPresignedUrl(file.getCosFilePath());
                log.info("获取签名地址失败，结果: {}", res);
                if (res.getString("data") != null) {
                    file.setCdnSignedUrl(res.getString("data"));
                    file.setT(System.currentTimeMillis()/BaseConstant.KILO + 1800);
                    baseMapper.updateById(file);
                } else {
                    throw exception(ErrorCodeConstants.ERROR_5038);
                }
            } catch (Exception e) {
                log.error("获取签名地址失败，错误信息: {}", e);
                throw exception(ErrorCodeConstants.ERROR_5038);
            }
        }
    }
    

    
    @Override
    public Boolean updateFileName(String auth, Integer id, String fileName) {
        if (id == null || StringUtils.isEmpty(fileName)) {
            throw exception(ErrorCodeConstants.BAD_REQUEST);
        }
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);
        AiAppKnowledgeFile file = getById(id);
        if (file == null) {
            return true;
        }

        if (file.getSocialUserId().equals(authUser.getUserId()) && file.getSocialType().equals(authUser.getUserType())) {
            file.setFileName(fileName);
            file.setUpdatedAt(LocalDateTime.now());
            return updateById(file);
        }

        throw exception(ErrorCodeConstants.FORBIDDEN);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public KnowledgeResponse create(String auth, FileUploadRequest reqVO, HttpServletRequest request) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);
        Integer level = 0;
        if (reqVO.getPid() > 0) {
            AiAppKnowledgeFile parent = getById(reqVO.getPid());
            if (parent == null || BaseConstant.TWO.equals(parent.getType()) ||
                    !parent.getSocialUserId().equals(authUser.getUserId()) || !parent.getSocialType().equals(authUser.getUserType())) {
                throw exception(ErrorCodeConstants.FORBIDDEN);
            }
            level = parent.getLevel() + 1;
            if (BaseConstant.ONE.equals(reqVO.getType()) && level >= BaseConstant.TEN) {
                throw exception(ErrorCodeConstants.ERROR_5056);
            }
        }

        AiAppKnowledgeFile file = new AiAppKnowledgeFile();
        file.setPid(reqVO.getPid());
        file.setType(reqVO.getType());
        file.setFileName(reqVO.getFileName());
        file.setSocialUserId(authUser.getUserId());
        file.setSocialType(authUser.getUserType());
        file.setLevel(level);
        file.setT(Long.valueOf(BaseConstant.ZERO));
        file.setCreatedAt(LocalDateTime.now());
        file.setUpdatedAt(LocalDateTime.now());
        save(file);

        // 如果是文件类型(type=2)，需要通过OpenAPI代理上传图片
        if (BaseConstant.TWO.equals(reqVO.getType())) {
            if (reqVO.getFile() == null) {
                throw exception(ErrorCodeConstants.ERROR_5019);
            }
            String ext = CommonUtil.getFileExtension(reqVO.getFile().getOriginalFilename());
            if (StringUtils.isEmpty(ext)) {
                throw exception(ErrorCodeConstants.ERROR_5025);
            }

            Integer MAX_FILE_SIZE = yudaoSystemService.getUploadLimitSize(ext.toLowerCase());
            if (reqVO.getFile().getSize() > MAX_FILE_SIZE*1024*1024) {
                throw exception(ErrorCodeConstants.ERROR_5026, MAX_FILE_SIZE+"MB");
            }
            try {
                // 调用OpenAPI服务代理上传请求
                Object uploadResult = openApiService.proxyRequest(request, "/upload_to_cos_private");
                log.info("文件上传代理请求完成，结果: {}", uploadResult);
                JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(uploadResult));
                if (result.getJSONObject("data") != null) {
                    file.setCdnSignedUrl(result.getJSONObject("data").getString("cdn_signed_url"));
                    file.setCosFilePath(result.getJSONObject("data").getString("cos_file_path"));
                    file.setFileMd5(result.getJSONObject("data").getString("file_md5"));
                    file.setFileSize(result.getJSONObject("data").getLong("file_size"));
                    file.setT(System.currentTimeMillis()/BaseConstant.KILO + 1800);
                    updateById(file);
                } else {
                    throw exception(ErrorCodeConstants.ERROR_5038);
                }
            } catch (Exception e) {
                log.error("文件上传失败，错误信息: {}", e);
                throw exception(ErrorCodeConstants.ERROR_5038);
            }
        }

        return toBean(file);
    }

    @Override
    public List<KnowledgeResponse> getFilePath(String auth, Integer id) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);
        AiAppKnowledgeFile file = getById(id);
        if (file == null || !file.getSocialUserId().equals(authUser.getUserId()) || !file.getSocialType().equals(authUser.getUserType())) {
            throw exception(ErrorCodeConstants.FORBIDDEN);
        }

        List<KnowledgeResponse> res = new ArrayList<>();
        res.add(toBean(file));
        // 递归查询所有父节点
        recursiveSelectParents(res, file.getPid());

        return res.stream().sorted((o1, o2) -> o1.getLevel().compareTo(o2.getLevel())).collect(Collectors.toList());
    }

    /**
     * 递归查询父节点
     *
     * @param res 结果列表
     * @param pid 父节点ID
     */
    private void recursiveSelectParents(List<KnowledgeResponse> res, Integer pid) {
        // 查询父节点
        if (pid != null && pid > BaseConstant.ZERO) {
            AiAppKnowledgeFile parent = getById(pid);
            if (parent != null) {
                res.add(toBean(parent));
                // 继续递归查询父节点的父节点
                recursiveSelectParents(res, parent.getPid());
            }
        }
    }

    @Override
    public Boolean delete(String auth, Integer id) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);
        AiAppKnowledgeFile file = getById(id);
        if (file == null) {
            return true;
        }

        if (file.getSocialUserId().equals(authUser.getUserId()) && file.getSocialType().equals(authUser.getUserType())) {
            return removeById(id);
        }

        throw exception(ErrorCodeConstants.FORBIDDEN);
    }

    public KnowledgeResponse toBean(AiAppKnowledgeFile file) {
        updateCdn(file);
        return BeanUtils.toBean(file, KnowledgeResponse.class, item -> {
            item.setCreatedAt(file.getCreatedAt().format(CommonUtil.DateTimeFormat2));
        });
    }

}