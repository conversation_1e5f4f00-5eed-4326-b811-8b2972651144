package cn.iocoder.yudao.aiBase.dto.response.admin;

import cn.iocoder.yudao.aiBase.entity.AiAppUsers;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UserAppResponse extends AiAppUsers {
    @Schema(description =  "appName")
    private String appName;

    @Schema(description =  "appNameEn")
    private String appNameEn;

    @Schema(description =  "username")
    private String userName;
}
