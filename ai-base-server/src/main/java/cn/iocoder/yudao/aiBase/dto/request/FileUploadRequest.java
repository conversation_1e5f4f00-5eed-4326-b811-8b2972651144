package cn.iocoder.yudao.aiBase.dto.request;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.springframework.web.multipart.MultipartFile;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FileUploadRequest {

    @Schema(description =  "1目录，2文件")
    @NotNull
    private Integer type = BaseConstant.ONE;

    @Schema(description =  "父级id")
    @NotNull
    private Integer pid = BaseConstant.ZERO;

    @Schema(description =  "目录名称")
    @Length(max = 100, message = "{ERROR_5057}")
    private String fileName;

    @Schema(description = "文件附件")
    private MultipartFile file;

}