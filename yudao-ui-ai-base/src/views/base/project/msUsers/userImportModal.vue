<template>
  <BasicModal 
    v-bind="$attrs" 
    @register="registerModal" 
    :title="t('action.import')" 
    :showOkBtn="!resultData"
    width="1000px"
  >
    <div v-if="!resultData || resultData.length == 0">
      <BasicForm @register="registerForm" />
      <div class="mt-4">
        <Divider>示例数据</Divider>
        <Table :dataSource="exampleData" :columns="resultColumns" size="small" bordered :pagination="false" />
      </div>
    </div>
    <div v-else class="mt-4">
      <Divider>导入结果</Divider>
      <div v-if="Array.isArray(resultData) && resultData.length > 0">
        <Table :dataSource="resultData" :columns="resultColumns" size="small" bordered />
      </div>
      <div class="mt-4 text-center">
        <a-button @click="handleReImport">重新导入</a-button>
        <a-button class="ml-4" type="primary" @click="handleClose">关闭</a-button>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
import { ref, nextTick } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { BasicModal, useModalInner } from '@/components/Modal'
import { BasicForm, useForm } from '@/components/Form'
import { importUser } from '@/api/medsciUsers'
import { Divider, Table, Empty } from 'ant-design-vue'

defineOptions({ name: 'UserImportModal' })

const emit = defineEmits(['success', 'register'])

const { t } = useI18n()

const resultData = ref([])

const exampleData = [
  {
    socialType: 55,
    socialUserId: 20,
    openid: "<EMAIL>",
    email: "<EMAIL>",
    userName: "zhangsan",
    password: null,
    realName: "张三",
    avatar: "https://img.medsci.cn/web/img/user_icon.png",
    mobile: "19112345678",
    remark: ""
  }
]

const resultColumns = [
  {
    title: '三方平台',
    dataIndex: 'socialType',
    width: 80
  },
  {
    title: '用户ID',
    dataIndex: 'socialUserId',
    width: 100
  },
  {
    title: 'openid',
    dataIndex: 'openid',
    width: 150
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    width: 150
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    width: 100
  },
  {
    title: '真实姓名',
    dataIndex: 'realName',
    width: 100
  },
  {
    title: '手机号',
    dataIndex: 'mobile',
    width: 120
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200
  }
]

const [registerForm, { resetFields }] = useForm({
  labelWidth: 120,
  showActionButtonGroup: false,
  schemas: [
    {
      label: '上传文件',
      field: 'file',
      required: true,
      component: 'Upload',
      componentProps: {
        maxNumber: 1,
        accept: ['.xls','.xlsx'],
        api: async (e) => {
          const res = await importUser(e)
          const {data} = res
          resultData.value = data.data
          return data
        },
        showUploadList: false
      }
    }
  ]
})

const [registerModal, { setModalProps, closeModal }] = useModalInner(() => {
  nextTick(() => {
    resetFields()
    resultData.value = []
  })
})

function handleReImport() {
  resultData.value = []
  nextTick(() => {
    resetFields()
  })
}

function handleClose() {
  closeModal()
}
</script>
<style scoped>
.mt-4 {
  margin-top: 1rem;
}

.ml-4 {
  margin-left: 1rem;
}
</style>