import { defHttp } from '@/utils/http/axios'


// 查询主站用户列表
export function getUsersPage(params) {
  return defHttp.get({ url: '/medsciUsers/page', params })
}

// 查询主站用户详情
export function getUsers(id: number) {
  return defHttp.get({ url: `/medsciUsers/get?id=${id}` })
}

// 新增主站用户
export function createUsers(data) {
  return defHttp.post({ url: '/medsciUsers/create', data })
}

// 修改主站用户
export function updateUsers(data) {
  return defHttp.put({ url: '/medsciUsers/update', data })
}

// 删除主站用户
export function deleteUsers(id: number) {
  return defHttp.delete({ url: `/medsciUsers/delete?id=${id}` })
}

// 导出主站用户 Excel
export function exportUsers(params) {
  return defHttp.download({ url: '/medsciUsers/export-excel', params }, '主站用户.xls')
}

// 清除缓存
export function clearConfigCache(name: string) {
  return defHttp.get({ url: `/medsciUsers/clearConfigCache?name=${name}` })
}

// 激活dify用户
export function activeDifyAccount(data) {
  return defHttp.post({ url: '/medsciUsers/activeDifyAccount', data })
}

// 获取导入用户模板
export function getTemplate() {
  return defHttp.download({ url: '/medsciUsers/getTemplate'}, '用户导入模板.xlsx')
}

// 导入用户
export function importUser(data) {
  return defHttp.uploadFile({ url: '/dev-api/admin-api/medsciUsers/importUser' }, data)
}
